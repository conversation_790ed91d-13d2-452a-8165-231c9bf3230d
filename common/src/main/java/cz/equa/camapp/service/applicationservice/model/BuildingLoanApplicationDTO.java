package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

@Data
@ToString
@XmlAccessorType(XmlAccessType.FIELD)
public class BuildingLoanApplicationDTO implements Serializable {

    private String applTpId;
    private String applStatId;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime applDate;
    private String posId;
    private String firstTouchPoint;
    private String applReason;
    private String hash;
    private String preApprOfrId;
    private String opportunityId;
    private String ccyId;
    private String distCnlId;
    private String fulfillmentCnl;
    private String wrkPlaceId;
    private String busProdSubTp;
    private String browserInfo;
    private Boolean telcoQueryAllowedFlag;
    private Boolean authMobSearchFlag;
    private String firstTouchPointOwnr;
    private String promoCode;
    private String applDsnKey;
    private String applComplPosId;
    private String contrNum;
    private String contrNumBs;
    private String contrNumReg;
    private String contrNumCr;
    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate contrSignDate;
    private String contrSignPosId;
    private String contrSignAdvisorId;
    private String contrSignAdvisorName;
    private String lastChangeAdvisorId;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime validFromDate;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime validToDate;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime moneyTransferDate;
    private String rejectRsnId;
    private String rejectRsn;
    private String rejectRsnTp;
    private String personApplModelId;
    private BigDecimal computedSalary;
    private Boolean incVerifPSD2Flag;
    private Boolean incVerifAccStmFlag;
    private Boolean incVerifStmUploadFlag;
    private Boolean incVerifCallFlag;
    private String incVerifSrcId;
    private BigDecimal incVerifPSD2Discount;
    private String contrCond;
    private String registryResult;
    private String finalRiskClass;
    private List<ConsentDTO> consents;
    private QuesterDTO quester;

    //FIXME tyto atributy nejsou v CtGetBuildingLoanAppl a tudíž budou vždy null
    private String rstsLoanNumber;
    private String rstsBuildingSavingsNumber;
    private String rstsRegistrationLoanNumber;
    private String rstsLoanContractNumber;
    private Boolean rstsDataSharingConsent;
    private Boolean rbSegmentDataSharingConsent;
    private Boolean rbIncomeDataSharingConsent;
    private OffsetDateTime consentTimestamp;

    public BuildingLoanApplicationDTO() {
        // needed for deserialization
    }
}
