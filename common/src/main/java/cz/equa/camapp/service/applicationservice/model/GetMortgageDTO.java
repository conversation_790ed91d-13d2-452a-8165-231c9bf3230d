package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@Setter
@Getter
public class GetMortgageDTO implements Serializable {
    private List<String> applVariantTpId;
    private boolean personsFlag;
    private boolean personsVerificationFlag;
    private boolean docFlag;
    private boolean householdsFlag;
    private boolean applFlag;
    private boolean applVariantsFlag;
    private boolean applVariantsFeeFlag;
    private boolean applVariantsInsurFlag;
    private boolean applVariantsSurchrgFlag;
    private boolean applMetadataFlag;
    private boolean applVariantsHycProdTpFlag;
    private boolean incomeFlag;
    private boolean oblgtnFlag;
    private boolean collateralFlag;
    private boolean loanSubjectFlag;
    private String hash;

    public GetMortgageDTO() {
        // needed for deserialization
    }
}
