package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class QuesterDTO {
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime sbmDate;
    private Boolean refreshFlag;
    private List<QuesterQuestDTO> quests;
}
