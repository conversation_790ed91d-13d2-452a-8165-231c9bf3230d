package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import lombok.Data;

import java.time.OffsetDateTime;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class QuesterQuestDTO {
    private String catgQuestId;
    private String questId;
    private String questStatId;
    private String questRsp;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime questSbmDate;
}
