package cz.equa.camapp.rest.service;

import cz.equa.camapp.lovs.LovApplVariantTp;
import cz.equa.camapp.rest.model.CreditCardRequest;
import cz.equa.camapp.rest.model.GetLoanApplicationResult;
import cz.equa.camapp.rest.util.Utils;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.rb.las.application.handler.ApiException;
import cz.rb.las.application.handler.DefaultApi;
import cz.rb.las.application.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static cz.equa.camapp.utils.Constants.CL_CONTEXT_ID_RB;
import static cz.equa.camapp.utils.Constants.CL_CONTEXT_ID_RSTS;

@Component(value = "restApplicationService")
@RequiredArgsConstructor
@Slf4j
public class ApplicationService extends CommonRestService {

    private static final String APPL_STATE_XAPI = "las-application-v1-getApplStatus";
    private static final String APPL_LOAN_XAPI = "las-application-v1-getLoanAppl";
    private static final String SET_APPL_XAPI = "las-application-v1-setLoanAppl";
    private static final String GET_CARD_XAPI = "las-application-v1-getCreditCardAppl";
    private static final String SET_CARD_XAPI = "las-application-v1-setCreditCardAppl";
    private static final String SET_CONTRACT_SIGN_XAPI = "las-application-v1-setContractSign";
    private static final String CHECK_ECOMMERCE_APPL_RIGHTS_XAPI = "las-application-v1-checkEcommerceRights";
    private static final String GET_APPS_TO_CANCEL_XAPI = "las-application-v1-getApplListToCancel";
    private static final String SET_PERSON_VERIFICATION_XAPI = "las-application-v1-setPersonVerification";
    private static final String GET_MORTGAGE_APPL = "las-application-v1-getMortgageAppl";
    private static final String SET_MORTGAGE_APPL = "las-application-v1-setMortgageAppl";
    private static final String CHECK_LOAN_APPL_EXISTS = "las-application-v1-checkLoanApplExists";
    private static final String GET_OVERDRAFT_APPL = "las-application-v1-getOverdraftAppl";
    private static final String SET_OVERDRAFT_APPL = "las-application-v1-setOverdraftAppl";
    private static final String GET_APPL_STATUS_HISTORY = "las-application-v1-getApplStatusHistory";
    private static final String SET_HYC_APPLICATION = "las-application-v1-setHycApplication";
    private static final String GET_BUILDING_LOAN_APPLICATION = "las-application-v1-getBuildingLoanAppl";
    private static final String SET_BUILDING_LOAN_APPLICATION = "las-application-v1-setBuildingLoanAppl";
    private static final String GET_APPL_LIST_TO_STATE_CHANGE = "las-application-v1-getApplListToStateChange";
    private static final String GET_MORTGAGE_SERVICING = "las-application-v1-getMortgageServicing";
    private static final String SET_MORTGAGE_SERVICING = "las-application-v1-setMortgageServicing";
    private static final String UPLOAD_HYC_REALTIES_XAPI = "las-application-v1-uploadHycRealties";

    private DefaultApi defaultApi;

    @Autowired
    public ApplicationService(DefaultApi defaultApi) {
        this.defaultApi = defaultApi;
    }

    public LoanApplicationDTO getApplication(final Long applKey, String correlationId) throws ServiceException {
        GetLoanApplicationResult getLoanApplicationResult = getApplicationWithProperties(applKey, false, false, null, false, correlationId);
        return (getLoanApplicationResult != null) ? getLoanApplicationResult.getLoanApplication() : null;
    }

    public GetLoanApplicationResult getApplicationWithOwner(final Long applKey, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, true, false, null, false, correlationId);
    }

    public GetLoanApplicationResult getApplicationWithVariants(final Long applKey, final List<LovApplVariantTp> variants, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, false, false, variants, false, correlationId);
    }

    public GetLoanApplicationResult getApplicationWithVariantsAndObligations(final Long applKey, final List<LovApplVariantTp> variants, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, false, false, variants, true, correlationId);
    }

    public GetLoanApplicationResult getApplicationWithOwnerIncome(final Long applKey, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, true, true, null, false, correlationId);
    }

    public GetLoanApplicationResult getApplicationWithVariantsOwnerIncome(final Long applKey, final List<LovApplVariantTp> variants, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, true, true, variants, false, correlationId);
    }

    public GetLoanApplicationResult getApplicationWithObligations(final Long applKey, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, true, false, null, true, correlationId);
    }

    public GetLoanApplicationResult getApplicationWithObligationsAndVariants(final Long applKey, final List<LovApplVariantTp> variants, String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, true, false, variants, true, correlationId);
    }

    public String getApplState(Long applKey, String busApplId, String correlationId) throws ServiceException {
        GetApplStatusResponse response = getApplInfo(applKey, busApplId, correlationId);
        if (response == null) {
            return null;
        }
        return response.getApplStatus();
    }

    public GetApplStatusResponse getApplInfo(Long applKey, String busApplId,
                                             String correlationId) throws ServiceException {
        return getApplInfo(applKey, busApplId, correlationId, null);
    }

    public GetApplStatusResponse getApplInfo(Long applKey, String busApplId,
                                             String correlationId,
                                             String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busApplId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        try {
            GetApplStatusResponse response = defaultApi.lasApplicationV1GetApplStatus(applicationId,
                    applicationIdType,
                    APPL_STATE_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
            if ((response == null) || (response.getApplStatus() == null)) {
                return null;
            }
            return response;
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetLoanApplicationResult getApplicationWithProperties(final Long applKey,
                                                                 final boolean owner,
                                                                 final boolean income,
                                                                 final List<LovApplVariantTp> variants,
                                                                 final boolean obligations,
                                                                 final String correlationId) throws ServiceException {
        return getApplicationWithProperties(applKey, owner, income, variants, obligations, false, correlationId, null);
    }

    public GetLoanApplicationResult getApplicationWithProperties(final Long applKey,
                                                                 final boolean owner,
                                                                 final boolean income,
                                                                 final List<LovApplVariantTp> variants,
                                                                 final boolean obligations,
                                                                 final boolean signingChannels,
                                                                 final String correlationId,
                                                                 String clContextID) throws ServiceException {
        final GetLoanApplResponse response = getApplicationWithPropertiesResponse(applKey, owner, income, variants, obligations, signingChannels, correlationId, clContextID);
        if (response == null) {
            return null;
        }
        GetLoanApplicationResult getLoanApplicationResult = new GetLoanApplicationResult(
                response.getLoanAppl(),
                response.getPrimaryOwner(),
                response.getIncomes(),
                response.getSignChannels(),
                response.getApplVariants(),
                response.getApplObligations(),
                response.getApplVariantParams());
        log.info("Incomes are required: {}, result is {}", income, getLoanApplicationResult.getIncomes());
        return getLoanApplicationResult;
    }

    private GetLoanApplResponse getApplicationWithPropertiesResponse(final Long applKey,
                                                                     final boolean owner,
                                                                     final boolean incomeAndVerification,
                                                                     final List<LovApplVariantTp> variants,
                                                                     final boolean obligations,
                                                                     final boolean signingChannels,
                                                                     final String correlationId) throws ServiceException {
        return getApplicationWithPropertiesResponse(applKey, owner, incomeAndVerification, variants, obligations, signingChannels, correlationId, null);
    }

    private GetLoanApplResponse getApplicationWithPropertiesResponse(final Long applKey,
                                                                     final boolean owner,
                                                                     final boolean incomeAndVerification,
                                                                     final List<LovApplVariantTp> variants,
                                                                     final boolean obligations,
                                                                     boolean signChannelsFlag,
                                                                     final String correlationId,
                                                                     String clContextID) throws ServiceException {
        String applicationId = applKey.toString();
        String applicationIdType = "APPLKEY";
        List<String> personIncomeVerificationResultIds = null;
        List<String> personIncomeVerificationSrcIds = null;
        List<String> personVerificationResultIds = null;
        List<String> personVerificationTpIds = null;
        boolean primaryOwnerFlag;
        boolean primaryOwnerIncomeFlag;
        boolean personVerificationFlag;
        boolean personIncomeVerificationFlag;
        boolean applFlag = true;
        boolean applVariantsFlag = false;
        boolean applVariantParamsFlag = false;
        boolean applMetadataFlag = false;
        boolean oblgtnFlag = false;
        boolean oblgtnActiveFlag = false;
        boolean usedInterestChangeFlag = false;

        // income
        primaryOwnerIncomeFlag = incomeAndVerification;
        personIncomeVerificationFlag = incomeAndVerification;

        personVerificationFlag = incomeAndVerification;
        if (incomeAndVerification) {
            // get PSD2 data from Galileo
            personVerificationTpIds = new ArrayList<>();
            personVerificationTpIds.add("PSD");
        }

        primaryOwnerFlag = owner;
        if (obligations) {
            oblgtnFlag = true;
            oblgtnActiveFlag = true;
        }

        List<String> lovApplVariantTpIds = new ArrayList<>();
        if (variants != null) {
            primaryOwnerFlag = true;

            // variants
            for (LovApplVariantTp variant : variants) {
                lovApplVariantTpIds.add(variant.getCode());

                if (LovApplVariantTp.SEL.getCode().equals(variant.getCode())) {
                    signChannelsFlag = true;
                }
            }
            applVariantsFlag = true;
            applVariantParamsFlag = true;
        }
        try {
            return defaultApi.lasApplicationV1GetLoanAppl(
                    applicationId,
                    applicationIdType,
                    APPL_LOAN_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    lovApplVariantTpIds,
                    personIncomeVerificationResultIds,
                    personIncomeVerificationSrcIds,
                    personVerificationResultIds,
                    personVerificationTpIds,
                    primaryOwnerFlag,
                    primaryOwnerIncomeFlag,
                    personVerificationFlag,
                    personIncomeVerificationFlag,
                    applFlag,
                    applVariantsFlag,
                    applVariantParamsFlag,
                    signChannelsFlag,
                    applMetadataFlag,
                    oblgtnFlag,
                    oblgtnActiveFlag,
                    usedInterestChangeFlag,
//                    false, //removed applCmtFlag
                    "",
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               String correlationId) throws ServiceException {
        setApplication(applKey, applicationDTO, true, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               String correlationId,
                               String clContextID) throws ServiceException {
        setApplication(applKey, applicationDTO, true, correlationId, clContextID);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final PtIncomeListDTO incomes,
                               String correlationId) throws ServiceException {
        setApplication(applKey, applicationDTO, null, incomes, true, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final PtIncomeListDTO incomes,
                               String correlationId,
                               String clContextID) throws ServiceException {
        setApplication(applKey, applicationDTO, null, incomes, true, correlationId, clContextID);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final Boolean waitForUnification,
                               String correlationId) throws ServiceException {
        setApplication(applKey, applicationDTO, null, null, waitForUnification, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final Boolean waitForUnification,
                               String correlationId,
                               String clContextID) throws ServiceException {
        setApplication(applKey, applicationDTO, null, null, waitForUnification, correlationId, clContextID);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final NewPersonDTO primaryOwner,
                               final Boolean waitForUnification,
                               String correlationId) throws ServiceException {
        setApplication(applKey, applicationDTO, primaryOwner, null, waitForUnification, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final NewPersonDTO primaryOwner,
                               final Boolean waitForUnification,
                               String correlationId,
                               String clContextID) throws ServiceException {
        setApplication(applKey, applicationDTO, primaryOwner, null, waitForUnification, correlationId, clContextID);
    }

    public void setApplication(final Long applKey,
                               final NewPersonDTO primaryOwner,
                               String correlationId) throws ServiceException {
        setApplication(applKey, null, primaryOwner, null, false, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final NewPersonDTO primaryOwner,
                               String correlationId,
                               String clContextID) throws ServiceException {
        setApplication(applKey, null, primaryOwner, null, false, correlationId, clContextID);
    }

    private void setSignChannelOnCardApplication(final Long applKey,
                                                 String signChannelId,
                                                 String signPosId,
                                                 String correlationId) throws ServiceException {
        setSignChannelOnCardApplication(applKey, signChannelId, signPosId, correlationId, null);
    }

    private void setSignChannelOnCardApplication(final Long applKey,
                                                 String signChannelId,
                                                 String signPosId,
                                                 String correlationId,
                                                 String clContextID) throws ServiceException {
        try {
            SetCreditCardApplRequest request = prepareSetCreditCardRequest(applKey, null);
            CtSignChannels signChannels = new CtSignChannels();
            signChannels.setApplVariantTpId("SEL");

            CtSignChannel signChannel = new CtSignChannel();
            signChannel.setSignCnlId(signChannelId);

            signChannels.setSignChannels(List.of(signChannel));

            if (signPosId != null) {
                signChannel.setSignPosId(signPosId);
            }

            request.setSignChannels(signChannels);
            defaultApi.lasApplicationV1SetCreditCardAppl(
                    SET_APPL_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private void setSignChannelOnLoanApplication(final Long applKey,
                                                 String signChannelId,
                                                 String signPosId,
                                                 String correlationId) throws ServiceException {
        setSignChannelOnLoanApplication(applKey, signChannelId, signPosId, correlationId, null);
    }

    private void setSignChannelOnLoanApplication(final Long applKey,
                                                 String signChannelId,
                                                 String signPosId,
                                                 String correlationId,
                                                 String clContextID) throws ServiceException {
        try {
            SetLoanApplRequest request = prepareApplicationRequest(applKey, null);
            CtSignChannels signChannels = new CtSignChannels();
            signChannels.setApplVariantTpId("SEL");
            CtSignChannel signChannel = new CtSignChannel();
            signChannel.setSignCnlId(signChannelId);

            signChannels.setSignChannels(List.of(signChannel));
            if (signPosId != null) {
                signChannel.setSignPosId(signPosId);
            }

            request.setSignChannels(signChannels);
            defaultApi.lasApplicationV1SetLoanAppl(
                    SET_APPL_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setSignChannelOnBuildingApplication(final Long applKey,
                                                    String signChannelId,
                                                    String signPosId,
                                                    String correlationId,
                                                    String clContextID) throws ServiceException {
        try {
            SetBuildingLoanApplRequest request = new SetBuildingLoanApplRequest();
            CtApplIdKeyIdChoice applId = new CtApplIdKeyIdChoice();
            applId.setApplKey(applKey);
            request.setApplId(applId);

            CtSignChannel signChannel = new CtSignChannel();

            List<CtSignChannel> signChannels = new ArrayList<>();
            signChannel.setSignCnlId(signChannelId);
            if (signPosId != null) {
                signChannel.setSignPosId(signPosId);
            }
            signChannels.add(signChannel);
            CtBuildingApplVariant variantSel = new CtBuildingApplVariant();
            variantSel.setApplVariantTpId(LovApplVariantTp.SEL.getCode());
            variantSel.setSignChannels(signChannels);

            List<CtBuildingApplVariant> variants = new ArrayList<>();
            variants.add(variantSel);
            request.setApplVariants(variants);

            defaultApi.lasApplicationV1SetBuildingLoanAppl(
                    SET_BUILDING_LOAN_APPLICATION,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private void setSignChannelOnOverdraftApplication(final Long applKey,
                                                      String signChannelId,
                                                      String signPosId,
                                                      String correlationId) throws ServiceException {
        setSignChannelOnOverdraftApplication(applKey, signChannelId, signPosId, correlationId, null);
    }

    private void setSignChannelOnOverdraftApplication(final Long applKey,
                                                      String signChannelId,
                                                      String signPosId,
                                                      String correlationId,
                                                      String clContextID) throws ServiceException {
        try {
            SetOverdraftApplRequest request = new SetOverdraftApplRequest();
            CtApplIdKeyIdChoice ctApplId = new CtApplIdKeyIdChoice();
            ctApplId.setApplKey(applKey);
            request.setApplId(ctApplId);

            CtSignChannels signChannels = new CtSignChannels();
            signChannels.setApplVariantTpId("SEL");

            CtSignChannel signChannel = new CtSignChannel();
            signChannel.setSignCnlId(signChannelId);

            signChannels.setSignChannels(List.of(signChannel));
            if (signPosId != null) {
                signChannel.setSignPosId(signPosId);
            }

            request.setSignChannels(signChannels);

            defaultApi.lasApplicationV1SetOverdraftAppl(
                    SET_OVERDRAFT_APPL,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setSignChannelOnApplication(String productId,
                                            final Long applKey,
                                            String signChannelId,
                                            String signPosId,
                                            String correlationId) throws ServiceException {
        setSignChannelOnApplication(productId, applKey, signChannelId, signPosId, correlationId, null);
    }

    public void setSignChannelOnApplication(String productId,
                                            final Long applKey,
                                            String signChannelId,
                                            String signPosId,
                                            String correlationId,
                                            String clContextID) throws ServiceException {
        if (productId.startsWith("RCC")) {
            setSignChannelOnCardApplication(applKey, signChannelId, signPosId, correlationId, clContextID);
        } else if (productId.startsWith("ROD")) {
            setSignChannelOnOverdraftApplication(applKey, signChannelId, signPosId, correlationId, clContextID);
        } else {
            setSignChannelOnLoanApplication(applKey, signChannelId, signPosId, correlationId, clContextID);
        }
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final NewPersonDTO primaryOwner,
                               final PtIncomeListDTO incomes,
                               final Boolean waitForUnification,
                               final String correlationId) throws ServiceException {
        setApplication(applKey, applicationDTO, primaryOwner, incomes, waitForUnification, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final NewPersonDTO primaryOwner,
                               final PtIncomeListDTO incomes,
                               final Boolean waitForUnification,
                               final String correlationId,
                               final String clContextID) throws ServiceException {

        SetLoanApplRequest request = prepareApplicationRequest(applKey, applicationDTO);
        request.setWaitForUnification(waitForUnification);
        if (primaryOwner != null) {
            request.setPrimaryOwner(ApplicationMapper.INSTANCE.dtoToEsb(primaryOwner));
        }
        if ((incomes != null) && (incomes.getIncomes() != null) && !incomes.getIncomes().isEmpty()) {
            List<CtPtIncome> ctPtIncomes = ApplicationMapper.INSTANCE.dtoIncomesToEsb(incomes.getIncomes());
            request.setPrimaryOwnerIncome(ctPtIncomes);
        }

        try {
            defaultApi.lasApplicationV1SetLoanAppl(
                    SET_APPL_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setBuildingApplication(final Long applKey,
                                       final BuildingLoanApplicationDTO buildingApplicationDTO,
                                       final BuildingLoanApplicationOwnerDTO primaryOwner,
                                       final ApplVariantsDTO variants,
                                       final BuildingObligationsDTO obligations,
                                       final Boolean waitForUnification,
                                       final String correlationId,
                                       final String clContextID) throws ServiceException {


        var applId = new CtApplIdKeyIdChoice();
        applId.setApplKey(applKey);

        SetBuildingLoanApplRequest request = new SetBuildingLoanApplRequest();
        request.setBuildingLoanAppl(ApplicationMapper.INSTANCE.dtoToEsb(buildingApplicationDTO));
        request.setApplId(applId);
        // request.setApplMetadata();
        request.setPersons(List.of(ApplicationMapper.INSTANCE.dtoToEsbRsts(primaryOwner)));
        if (variants != null && variants.getAllVariants() != null) {
            request.setApplVariants(ApplicationMapper.INSTANCE.dtoToBuildingApplVariantsEsb(variants.getAllVariants()));
        }
        if (obligations != null && obligations.getObligations() != null) {
            request.setApplObligations(ApplicationMapper.INSTANCE.dtoToOblEsb(obligations.getObligations()));
        }


        try {
            defaultApi.lasApplicationV1SetBuildingLoanAppl(
                    SET_BUILDING_LOAN_APPLICATION,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final ApplVariantsDTO applVariantsDTO,
                               String correlationId) throws ServiceException {
        setApplication(applKey, applicationDTO, applVariantsDTO, correlationId, null);
    }

    public void setApplication(final Long applKey,
                               final LoanApplicationDTO applicationDTO,
                               final ApplVariantsDTO applVariantsDTO,
                               String correlationId,
                               String clContextID) throws ServiceException {
        SetLoanApplRequest request = prepareApplicationRequest(applKey, applicationDTO);
        if (applVariantsDTO != null) {
            final List<CtApplVariant> ctApplVariants = ApplicationMapper.INSTANCE.dtoVariantsToEsb(applVariantsDTO.getAllVariants());
            request.setApplVariants(ctApplVariants);
        }

        try {
            defaultApi.lasApplicationV1SetLoanAppl(
                    SET_APPL_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private SetLoanApplRequest prepareApplicationRequest(Long applKey,
                                                         LoanApplicationDTO applicationDTO) {
        final SetLoanApplRequest request = new SetLoanApplRequest();
        CtApplIdKeyIdChoice ctApplId = new CtApplIdKeyIdChoice();
        ctApplId.setApplKey(applKey);
        request.setApplId(ctApplId);
        if (applicationDTO != null) {
            final CtSetLoanAppl loanApplication = ApplicationMapper.INSTANCE.dtoToEsb(applicationDTO);
            request.setLoanAppl(loanApplication);
        }
        return request;
    }

    public GetCreditCardApplResponseDTO getCreditCard(Long applKey,
                                                      String busApplId,
                                                      Boolean owner,
                                                      Boolean income,
                                                      List<String> variants,
                                                      Boolean signingChannel,
                                                      String correlationId) throws ServiceException {
        return getCreditCard(applKey, busApplId, owner, income, variants, signingChannel, correlationId, null);
    }

    public GetCreditCardApplResponseDTO getCreditCard(Long applKey,
                                                      String busApplId,
                                                      Boolean owner,
                                                      Boolean income,
                                                      List<String> variants,
                                                      Boolean signingChannel,
                                                      String correlationId,
                                                      String clContextID) throws ServiceException {
        CreditCardRequest request = new CreditCardRequest.CreditCardRequestBuilder()
                .setPrimaryOwnerFlag(owner)
                .setPersonIncomeVerificationFlag(income)
                .setPrimaryOwnerIncomeFlag(income)
                .setApplVariantsFlag(!variants.isEmpty())
                .setApplVariantParamsFlag(!variants.isEmpty())
                .setApplVariantTpId(variants)
                .setSignChannelsFlag(signingChannel)
                .build();

        Assert.notNull(request, "Credit card request can't be null");

        String applicationId = Utils.getApplicationId(applKey, busApplId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        try {
            GetCreditCardApplResponse creditCardResponse = defaultApi.lasApplicationV1GetCreditCardAppl(applicationId,
                    applicationIdType,
                    GET_CARD_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request.getApplVariantTpId(),
                    request.getPersonIncomeVerificationResultId(),
                    request.getPersonIncomeVerificationSrcId(),
                    request.getPersonVerificationResultId(),
                    request.getPersonVerificationTpId(),
                    request.getPrimaryOwnerFlag(),
                    request.getPrimaryOwnerIncomeFlag(),
                    request.getPersonVerificationFlag(),
                    request.getPersonIncomeVerificationFlag(),
                    request.getApplFlag(),
                    request.getApplVariantsFlag(),
                    request.getApplVariantParamsFlag(),
                    request.getSignChannelsFlag(),
                    request.getApplMetadataFlag(),
//                    false, //removed applCmtFlag
                    "",
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
            if (creditCardResponse == null) {
                return null;
            }
            return ApplicationMapper.INSTANCE.esbToDto(creditCardResponse);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public SetCreditCardApplResponse setCreditCard(final Long applKey,
                                                   final LoanApplicationDTO applicationDTO,
                                                   final NewPersonDTO primaryOwner,
                                                   final PtIncomeListDTO incomes,
                                                   final Boolean waitForUnification,
                                                   final String correlationId) throws ServiceException {
        return setCreditCard(applKey, applicationDTO, primaryOwner, incomes, waitForUnification, correlationId, null);
    }

    public SetCreditCardApplResponse setCreditCard(final Long applKey,
                                                   final LoanApplicationDTO applicationDTO,
                                                   final NewPersonDTO primaryOwner,
                                                   final PtIncomeListDTO incomes,
                                                   final Boolean waitForUnification,
                                                   final String correlationId,
                                                   final String clContextID) throws ServiceException {
        SetCreditCardApplRequest request = prepareSetCreditCardRequest(applKey, applicationDTO);
        request.setWaitForUnification(waitForUnification);
        if (primaryOwner != null) {
            request.setPrimaryOwner(ApplicationMapper.INSTANCE.dtoToEsb(primaryOwner));
        }
        if ((incomes != null) && (incomes.getIncomes() != null) && !incomes.getIncomes().isEmpty()) {
            List<CtPtIncome> ctPtIncomes = ApplicationMapper.INSTANCE.dtoIncomesToEsb(incomes.getIncomes());
            request.setPrimaryOwnerIncome(ctPtIncomes);
        }

        try {
            return defaultApi.lasApplicationV1SetCreditCardAppl(
                    SET_CARD_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private SetCreditCardApplRequest prepareSetCreditCardRequest(Long applKey,
                                                                 LoanApplicationDTO applicationDTO) {
        final SetCreditCardApplRequest request = new SetCreditCardApplRequest();
        CtApplIdKeyIdChoice ctApplId = new CtApplIdKeyIdChoice();
        ctApplId.setApplKey(applKey);
        request.setApplId(ctApplId);
        if (applicationDTO != null) {
            final CreditCardAppl creditCardAppl = ApplicationMapper.INSTANCE.dtoToEsbCreditCard(applicationDTO);
            request.setCreditCardAppl(creditCardAppl);
        }
        return request;
    }

    public void setPersonVerification(String incVerifResult,
                                      Long instPtIncVerifKey,
                                      Long applKey,
                                      String busApplId,
                                      String correlationId) throws ServiceException {
        setPersonVerification(incVerifResult, instPtIncVerifKey, applKey, busApplId, correlationId, null);
    }

    public void setPersonVerification(String incVerifResult,
                                      Long instPtIncVerifKey,
                                      Long applKey,
                                      String busApplId,
                                      String correlationId,
                                      String clContextID) throws ServiceException {
        SetPersonVerificationRequest request = new SetPersonVerificationRequest();
        request.setInstPtIncVerifKey(instPtIncVerifKey);
        request.setIncVerifResult(incVerifResult);

        try {
            defaultApi.lasApplicationV1SetPersonVerification(Utils.getApplicationId(applKey, busApplId),
                    Utils.getApplicationIdType(applKey),
                    SET_PERSON_VERIFICATION_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setPersonVerification(final Long applKey,
                                      final String busApplId,
                                      NewPersonVerificationDTO verificationDTO,
                                      String correlationId) throws ServiceException {
        setPersonVerification(applKey, busApplId, verificationDTO, correlationId, null);
    }

    public void setPersonVerification(final Long applKey,
                                      final String busApplId,
                                      NewPersonVerificationDTO verificationDTO,
                                      String correlationId,
                                      String clContextID) throws ServiceException {
        SetPersonVerificationRequest request = new SetPersonVerificationRequest();

        request.setVerifType(verificationDTO.getVerifSource().getCode());
        if ((verificationDTO.getAccounts() != null) && !verificationDTO.getAccounts().isEmpty()) {
            request.setAccounts(ApplicationMapper.INSTANCE.dtoAccountsToEsb(verificationDTO.getAccounts()));
        }

        if (verificationDTO.getVerifTime() != null) {
            request.setVerifTime(verificationDTO.getVerifTime().toOffsetDateTime());
        }
        try {
            defaultApi.lasApplicationV1SetPersonVerification(Utils.getApplicationId(applKey, busApplId),
                    Utils.getApplicationIdType(applKey),
                    SET_PERSON_VERIFICATION_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public List<ApplicationToCancelDTO> getApplListToCancel(String correlationId) throws ServiceException {
        List<ApplicationToCancelDTO> applicationToCancelList = new ArrayList<>();

        try {
            List<ApplicationToCancelDTO> responseSubTp = getApplicationsToCancel(correlationId,
                    null,
                    List.of("RCL_STANDARD", "RCL_DEFPAY", "RCL_REFI", "ROD_AUTDEBIT", "ROD_INFRE", "RCL_OPTI"),
                    CL_CONTEXT_ID_RB);
            if (responseSubTp != null) {
                applicationToCancelList.addAll(responseSubTp);
            }

            List<ApplicationToCancelDTO> responseTp = getApplicationsToCancel(correlationId, List.of("RCC", "RML"), null, CL_CONTEXT_ID_RB);
            if (responseTp != null) {
                applicationToCancelList.addAll(responseTp);
            }

            List<ApplicationToCancelDTO> responseTpBslRsts = getApplicationsToCancel(correlationId, List.of("BSL"), null, CL_CONTEXT_ID_RSTS);
            if (responseTpBslRsts != null) {
                applicationToCancelList.addAll(responseTpBslRsts);
            }

            List<ApplicationToCancelDTO> responseTpBslRb = getApplicationsToCancel(correlationId, List.of("BSL"), null, CL_CONTEXT_ID_RB);
            if (responseTpBslRb != null) {
                applicationToCancelList.addAll(responseTpBslRb);
            }

            return applicationToCancelList;
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private List<ApplicationToCancelDTO> getApplicationsToCancel(String correlationId,
                                                                 List<String> prodTps,
                                                                 List<String> prodSubTps,
                                                                 String clContextID) throws ApiException {
        return ApplicationMapper.INSTANCE.esbCancelToDto(defaultApi.lasApplicationV1GetApplListToCancel(
                        GET_APPS_TO_CANCEL_XAPI,
                        UUID.randomUUID().toString(),
                        correlationId,
                        X_REQUEST_APP,
                        prodTps,
                        prodSubTps,
                        null,
                        null,
                        null,
                        xFrontendApp,
                        xFrontendService,
                        null,
                        null,
                        null,
                        clContextID),
                clContextID);
    }

    public void setContractSign(final Long applKey,
                                final String busApplId,
                                final String signingChannel,
                                String signPosId,
                                String correlationId) throws ServiceException {
        setContractSign(applKey, busApplId, signingChannel, signPosId, correlationId, null);
    }

    public void setContractSign(final Long applKey,
                                final String busApplId,
                                final String signingChannel,
                                String signPosId,
                                String correlationId,
                                String clContextID) throws ServiceException {
        SetContractSignRequest request = new SetContractSignRequest();
        request.setContrSignDate(LocalDate.now());
        request.setFulfillmentCnlId(signingChannel);
        if (signPosId != null) {
            request.setContrSignPosId(signPosId);
        }

        try {
            defaultApi.lasApplicationV1SetContractSign(Utils.getApplicationId(applKey, busApplId),
                    Utils.getApplicationIdType(applKey),
                    SET_CONTRACT_SIGN_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public boolean checkEcommerceApplRights(final Long applKey,
                                            final String busApplId,
                                            String firstTouchPoint,
                                            String correlationId) throws ServiceException {
        return checkEcommerceApplRights(applKey, busApplId, firstTouchPoint, correlationId, null);
    }

    public boolean checkEcommerceApplRights(final Long applKey,
                                            final String busApplId,
                                            String firstTouchPoint,
                                            String correlationId,
                                            String clContextID) throws ServiceException {

        try {
            CheckEcommerceRightsResponse response = defaultApi.lasApplicationV1CheckEcommerceRights(Utils.getApplicationId(applKey, busApplId),
                    Utils.getApplicationIdType(applKey),
                    CHECK_ECOMMERCE_APPL_RIGHTS_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    firstTouchPoint,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
            if (response == null) {
                return false;
            }
            return Boolean.TRUE.equals(response.getResult());
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetMortApplResponseDTO getMortgageAppl(Long applKey,
                                                  String busApplId,
                                                  GetMortgageDTO getMortgageDTO,
                                                  String correlationId) throws ServiceException {
        return getMortgageAppl(applKey, busApplId, getMortgageDTO, correlationId, null);
    }

    public GetMortApplResponseDTO getMortgageAppl(Long applKey,
                                                  String busApplId,
                                                  GetMortgageDTO getMortgageDTO,
                                                  String correlationId,
                                                  String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busApplId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        try {
            GetMortApplResponse response = defaultApi.lasApplicationV1GetMortgageAppl(applicationId,
                    applicationIdType,
                    GET_MORTGAGE_APPL,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    getMortgageDTO.getApplVariantTpId(),
                    getMortgageDTO.isPersonsFlag(),
                    getMortgageDTO.isPersonsVerificationFlag(),
                    getMortgageDTO.isDocFlag(),
                    getMortgageDTO.isHouseholdsFlag(),
                    getMortgageDTO.isApplFlag(),
                    getMortgageDTO.isApplVariantsFlag(),
                    getMortgageDTO.isApplVariantsHycProdTpFlag(),
                    getMortgageDTO.isApplVariantsFeeFlag(),
                    getMortgageDTO.isApplVariantsInsurFlag(),
                    getMortgageDTO.isApplVariantsSurchrgFlag(),
                    getMortgageDTO.isApplMetadataFlag(),
                    getMortgageDTO.isIncomeFlag(),
                    getMortgageDTO.isOblgtnFlag(),
                    getMortgageDTO.isCollateralFlag(),
                    getMortgageDTO.isLoanSubjectFlag(),
                    getMortgageDTO.getHash(),
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
            return ApplicationMapper.INSTANCE.esbToDto(response);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setOverdraft(final Long applKey,
                             final NewPersonDTO primaryOwner,
                             String correlationId) throws ServiceException {
        setOverdraft(applKey, null, primaryOwner, null, false, correlationId, null);
    }

    public void setOverdraft(final Long applKey,
                             final NewPersonDTO primaryOwner,
                             String correlationId,
                             String clContextID) throws ServiceException {
        setOverdraft(applKey, null, primaryOwner, null, false, correlationId, clContextID);
    }

    public SetOverdraftResponseDTO setOverdraft(final Long applKey,
                                                final LoanApplicationDTO applicationDTO,
                                                final NewPersonDTO primaryOwner,
                                                final PtIncomeListDTO incomes,
                                                final Boolean waitForUnification,
                                                final String correlationId) throws ServiceException {
        return setOverdraft(applKey, applicationDTO, primaryOwner, incomes, waitForUnification, correlationId, null);
    }

    public SetOverdraftResponseDTO setOverdraft(final Long applKey,
                                                final LoanApplicationDTO applicationDTO,
                                                final NewPersonDTO primaryOwner,
                                                final PtIncomeListDTO incomes,
                                                final Boolean waitForUnification,
                                                final String correlationId,
                                                final String clContextID) throws ServiceException {

        SetOverdraftRequestDTO request = SetOverdraftRequestDTO.SetOverdraftRequestDTOBuilder.aSetOverdraftRequestDTO()
                .setApplId(new ApplIdKeyIdChoiceDTO(applKey, null, null))
                .setOverdraftAppl(applicationDTO != null ? ApplicationMapper.INSTANCE.loanApplToOverdraftAppl(applicationDTO) : null)
                .setPrimaryOwner(primaryOwner)
                .setPrimaryOwnerIncome((incomes != null) && (incomes.getIncomes() != null) && !incomes.getIncomes().isEmpty() ? incomes.getIncomes() : null)
                .setWaitForUnification(waitForUnification)
                .build();

        try {
            SetOverdraftApplResponse response = defaultApi.lasApplicationV1SetOverdraftAppl(SET_OVERDRAFT_APPL,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    ApplicationMapper.INSTANCE.dtoToEsb(request),
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID
            );
            return ApplicationMapper.INSTANCE.esbToDto(response);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public SetMortApplResponseDTO setMortgageAppl(SetMortApplRequestDTO setMortApplRequestDTO,
                                                  String correlationId) throws ServiceException {
        return setMortgageAppl(setMortApplRequestDTO, correlationId, null);
    }

    public SetMortApplResponseDTO setMortgageAppl(SetMortApplRequestDTO setMortApplRequestDTO,
                                                  String correlationId,
                                                  String clContextID) throws ServiceException {
        try {
            SetMortApplResponse response = defaultApi.lasApplicationV1SetMortgageAppl(SET_MORTGAGE_APPL,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    ApplicationMapper.INSTANCE.dtoToEsb(setMortApplRequestDTO),
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
            return ApplicationMapper.INSTANCE.esbToDto(response);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public Long checkLoanApplExists(Long applKey,
                                    String busApplId,
                                    String correlationId) throws ServiceException {
        return checkLoanApplExists(applKey, busApplId, correlationId, null);
    }

    public Long checkLoanApplExists(Long applKey,
                                    String busApplId,
                                    String correlationId,
                                    String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busApplId);
        String applicationIdType = Utils.getApplicationIdType(applKey);
        try {
            CheckLoanApplExistResponse response = defaultApi.lasApplicationV1CheckLoanApplExists(
                    applicationId,
                    applicationIdType,
                    CHECK_LOAN_APPL_EXISTS,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID
            );
            return response.getApplKey();
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetOverdraftApplResponseDTO getOverdraft(Long applKey,
                                                    String busApplId,
                                                    GetOverdraftRequest request,
                                                    String correlationId) throws ServiceException {
        return getOverdraft(applKey, busApplId, request, correlationId, null);
    }

    public GetOverdraftApplResponseDTO getOverdraft(Long applKey,
                                                    String busApplId,
                                                    GetOverdraftRequest request,
                                                    String correlationId,
                                                    String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busApplId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        try {
            GetOverdraftApplResponse getOverdraftApplResponse = defaultApi.lasApplicationV1GetOverdraftAppl(applicationId,
                    applicationIdType,
                    GET_OVERDRAFT_APPL,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request.getApplVariantTpId(),
                    request.getPersonIncomeVerificationResultId(),
                    request.getPersonIncomeVerificationSrcId(),
                    request.getPersonVerificationResultId(),
                    request.getPersonVerificationTpId(),
                    request.getPrimaryOwnerFlag(),
                    request.getPrimaryOwnerIncomeFlag(),
                    request.getPersonVerificationFlag(),
                    request.getPersonIncomeVerificationFlag(),
                    request.getApplFlag(),
                    request.getApplVariantsFlag(),
                    request.getApplVariantParamsFlag(),
                    request.getSignChannelsFlag(),
                    request.getApplMetadataFlag(),
//                    false, // removed applCmtFlag
                    "",
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
            return ApplicationMapper.INSTANCE.esbToDto(getOverdraftApplResponse);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetOverdraftApplResponseDTO getOverdraft(Long applKey,
                                                    String busApplId,
                                                    boolean owner,
                                                    boolean income,
                                                    List<String> variants,
                                                    String correlationId) throws ServiceException {
        return getOverdraft(applKey, busApplId, owner, income, variants, false, correlationId, null);
    }

    public GetOverdraftApplResponseDTO getOverdraft(Long applKey,
                                                    String busApplId,
                                                    boolean owner,
                                                    boolean income,
                                                    List<String> variants,
                                                    boolean signingChannels,
                                                    String correlationId,
                                                    String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busApplId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        GetOverdraftRequest request = new GetOverdraftRequest.GetOverdraftRequestBuilder()
                .setPrimaryOwnerFlag(owner)
                .setPersonIncomeVerificationFlag(income)
                .setPrimaryOwnerIncomeFlag(income)
                .setApplVariantsFlag(!variants.isEmpty())
                .setApplVariantParamsFlag(!variants.isEmpty())
                .setApplVariantTpId(variants)
                .setSignChannelsFlag(signingChannels)
                .build();

        try {
            GetOverdraftApplResponse getOverdraftApplResponse = defaultApi.lasApplicationV1GetOverdraftAppl(applicationId,
                    applicationIdType,
                    GET_OVERDRAFT_APPL,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    request.getApplVariantTpId(),
                    request.getPersonIncomeVerificationResultId(),
                    request.getPersonIncomeVerificationSrcId(),
                    request.getPersonVerificationResultId(),
                    request.getPersonVerificationTpId(),
                    request.getPrimaryOwnerFlag(),
                    request.getPrimaryOwnerIncomeFlag(),
                    request.getPersonVerificationFlag(),
                    request.getPersonIncomeVerificationFlag(),
                    request.getApplFlag(),
                    request.getApplVariantsFlag(),
                    request.getApplVariantParamsFlag(),
                    request.getSignChannelsFlag(),
                    request.getApplMetadataFlag(),
//                    false, // applCmtFlag ?
                    "",
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
            return ApplicationMapper.INSTANCE.esbToDto(getOverdraftApplResponse);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public List<StateLog> getApplStatusHistory(Long applKey,
                                               String busAppId,
                                               String correlationId) throws ServiceException {
        return getApplStatusHistory(applKey, busAppId, correlationId, null);
    }

    public List<StateLog> getApplStatusHistory(Long applKey,
                                               String busAppId,
                                               String correlationId,
                                               String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busAppId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        try {
            List<StateLog> response = defaultApi.lasApplicationV1GetApplStatusHistory(
                    applicationId,
                    applicationIdType,
                    GET_APPL_STATUS_HISTORY,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    null,
                    xFrontendApp,
                    xFrontendService,
                    null,
                    null,
                    null,
                    clContextID);
            if (response == null) {
                return new ArrayList<>();
            }
            return response;
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setHycApplication(Long applKey,
                                  String busAppId,
                                  String correlationId) throws ServiceException {
        setHycApplication(applKey, busAppId, correlationId, null);
    }

    public void setHycApplication(Long applKey,
                                  String busAppId,
                                  String correlationId,
                                  String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busAppId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        try {
            defaultApi.lasApplicationV1SetHycAppl(
                    applicationId,
                    applicationIdType,
                    SET_HYC_APPLICATION,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    null,
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetBuildingLoanApplResponse getBuildingLoanAppl(final Long applKey,
                                                           final String busAppId,
                                                           final boolean owner,
                                                           final boolean incomeAndVerification,
                                                           final List<LovApplVariantTp> variants,
                                                           final boolean obligations,
                                                           final String correlationId,
                                                           final String clContextID) throws ServiceException {
        String applicationId = Utils.getApplicationId(applKey, busAppId);
        String applicationIdType = Utils.getApplicationIdType(applKey);

        List<String> personIncomeVerificationResultIds = null;
        List<String> personIncomeVerificationSrcIds = null;
        List<String> personVerificationResultIds = null;
        List<String> personVerificationTpIds = null;
        boolean primaryOwnerFlag;
        boolean primaryOwnerIncomeFlag;
        boolean personVerificationFlag;
        boolean personIncomeVerificationFlag;
        boolean applFlag = true;
        boolean applVariantsFlag = false;
        boolean applVariantParamsFlag = false;
        boolean signChannelsFlag = false;
        boolean applMetadataFlag = false;
        boolean oblgtnFlag = false;
        boolean oblgtnActiveFlag = false;
        boolean docFlag = false;
        boolean csaPtMetadFlag = false;
        boolean applVariantsSurchargeFlag = false;

        //income
        primaryOwnerIncomeFlag = incomeAndVerification;
        personIncomeVerificationFlag = incomeAndVerification;
        personVerificationFlag = incomeAndVerification;
        if (incomeAndVerification) {
            // get PSD2 data from Galileo
            personVerificationTpIds = new ArrayList<>();
            personVerificationTpIds.add("PSD");
        }

        primaryOwnerFlag = owner;
        csaPtMetadFlag = owner;
        if (obligations) {
            oblgtnFlag = true;
            oblgtnActiveFlag = true;
        }

        List<String> lovApplVariantTpIds = new ArrayList<>();
        if (variants != null) {
            primaryOwnerFlag = true;

            // variants
            for (LovApplVariantTp variant : variants) {
                lovApplVariantTpIds.add(variant.getCode());

                if (LovApplVariantTp.SEL.getCode().equals(variant.getCode())) {
                    signChannelsFlag = true;
                }
            }
            applVariantsFlag = true;
            applVariantParamsFlag = true;
            applVariantsSurchargeFlag = true;
        }
        try {
            return defaultApi.lasApplicationV1GetBuildingLoanAppl(
                    applicationId,
                    applicationIdType,
                    GET_BUILDING_LOAN_APPLICATION,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    lovApplVariantTpIds,
                    personIncomeVerificationResultIds,
                    personIncomeVerificationSrcIds,
                    personVerificationResultIds,
                    personVerificationTpIds,
                    primaryOwnerFlag,
                    primaryOwnerIncomeFlag,
                    personVerificationFlag,
                    personIncomeVerificationFlag,
                    applFlag,
                    applVariantsFlag,
                    applVariantParamsFlag,
                    applVariantsSurchargeFlag,
                    signChannelsFlag,
                    applMetadataFlag,
                    oblgtnFlag,
                    oblgtnActiveFlag,
                    docFlag,
                    csaPtMetadFlag,
                    UUID.randomUUID().toString(),
                    null,
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);

        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public List<ApplicationToStateChange> getApplListToStateChange(final String correlationId, final String clContextID) throws ServiceException {
        try {
            return defaultApi.lasApplicationV1GetApplListToStateChange(
                    GET_APPL_LIST_TO_STATE_CHANGE,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    1000,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetMortgageServicingResponse getMortgageServicing(final Long applKey, final String busApplId, final boolean personsFlag, final String correlationId, final String clContextID) throws ServiceException {
        try {
            String applicationId = Utils.getApplicationId(applKey, busApplId);
            String applicationIdType = Utils.getApplicationIdType(applKey);
            return defaultApi.lasApplicationV1GetMortgageServicing(
                    applicationId,
                    applicationIdType,
                    GET_MORTGAGE_SERVICING,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    personsFlag,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setMortgageServicing(final SetMortgageServicingRequest setMortgageServicingRequest, final String correlationId, final String clContextID) throws ServiceException {
        try {
            defaultApi.lasApplicationV1SetMortgageServicing(
                    SET_MORTGAGE_SERVICING,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    setMortgageServicingRequest,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    clContextID);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }


    public void uploadHycRealties(final String busApplId,
                                  final String correlationId,
                                  final String clContextID) throws ServiceException {

        try {
            defaultApi.lasApplicationV1UploadHycRealties(
                    busApplId,
                    UPLOAD_HYC_REALTIES_XAPI,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    UUID.randomUUID().toString(),
                    xFrontendApp,
                    xFrontendService,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS
//                    ,clContextID
            );
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }
}
