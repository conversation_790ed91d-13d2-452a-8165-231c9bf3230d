package cz.equa.camapp.rest.service;

import cz.equa.camapp.model.GeneratedDocumentInfoDTO;
import cz.equa.camapp.model.document.*;
import cz.equa.camapp.rest.base.RestRequestHeaders;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.util.XmlUtils;
import cz.equa.camapp.service.ServiceException;
import cz.rb.dms.document.handler.ApiException;
import cz.rb.dms.document.handler.ApiResponse;
import cz.rb.dms.document.model.*;
import cz.rb.dms.document.model.AddClientSignatureReq;
import cz.rb.dms.document.model.CertificationInner;
import cz.rb.dms.document.model.GetUrlRes;
import cz.rb.dms.document.model.RenderingContent;
import cz.rb.dms.document.model.Signature;
import cz.rb.dms.document.v2.model.*;
import cz.rb.dms.document.v2.model.GetListRes;
import cz.rb.services.entityservice.document._1.EntDocumentRequest;
import cz.rb.services.entityservice.document._1.EntDocumentResponse;
import cz.rb.services.entityservice.document.create_4_0.OpCreate40;
import cz.rb.services.entityservice.document.create_4_0.OpCreate40Res;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import static cz.equa.camapp.rest.model.Document.DELIVERY_CHANNEL_EMAIL;
import static cz.equa.camapp.rest.model.DocumentType.********;


@Component
@Slf4j
public class DmsService extends CommonRestService {
    public static final String DELETE_SCOPE_ALL_VERSIONS = "ALL_VERSIONS";
    public static final String DELETE_SCOPE_VERSION = "VERSION";
    public static final String DELETE_SCOPE_CURRENT_VERSION = "CURRENT_VERSION";

    private static final String GET_URL = "dms-document-v1-getUrl";
    private static final String GET_LIST = "dms-document-v2-getList";
    private static final String RENDER_UPDATE = "dms-document-v2-documentRenderUpdate";
    private static final String DELETE = "dms-document-v1-delete";
    private static final String UPLOAD = "dms-document-v1-upload";
    private static final String RENDER = "dms-document-v1-render";
    private static final String ADD_CLIENT_SIGNATURE = "dms-document-v1-addClientSignature";
    private static final String CREATE = "DMS_EntDocument-Create_4_0";

    private static final String CLCFD_DECLINED = "CLCFD_DECLINED";
    private static final String CLCFD_CANCEL = "CLCFD_CANCEL";
    private static final String CLCFD_DESTROY = "CLCFD_DESTROY";
    public static final String REKO_RSTS = "REKO_RSTS";
    public static final String REKO_EXPIRED = "REKO_EXPIRED";
    public static final String REKO_CANCEL = "REKO_CANCEL";
    protected static final String NO_VERSION_FLAG = "NO_VERSION";
    protected static final String MINOR_FLAG = "MINOR";
    protected static final String STR_ZDZAM = "STR / ZDZAM";
    protected static final String DELIVERY_CHANNEL_ARCHIVE = "ARCHIVE";
    public static final String DOCUMENT_ID_TYPE_DCTMID = "DCTMID";
    public static final String DOCUMENT_ID_TYPE_EXTERNALID = "EXTERNALID";
    public static final String DOCUMENT_ID_TYPE_APPLICATIONID = "APPLICATIONID"; // only for getUrl operation

    public static final String DOCUMENT_EXTERNALID_PREFIX = "CMNL";

    private final cz.rb.dms.document.handler.DefaultApi apiV1;
    private final cz.rb.dms.document.v2.handler.DefaultApi apiV2;
    private final TifImsService tifImsService;
    private final RestRequestHeaders restRequestHeaders;

    @Autowired
    public DmsService(cz.rb.dms.document.handler.DefaultApi defaultApi, cz.rb.dms.document.v2.handler.DefaultApi defaultApiV2, TifImsService tifImsService) {
        this.apiV1 = defaultApi;
        this.apiV2 = defaultApiV2;
        this.tifImsService = tifImsService;

        this.restRequestHeaders = new RestRequestHeaders();
        this.restRequestHeaders.setXApiName("DMS_EntDocument-Create_4_0");
        this.restRequestHeaders.setXApiVersion("100");
        this.restRequestHeaders.setXRequestApp(X_REQUEST_APP);
        this.restRequestHeaders.setXUser(CAM_USER);
    }

    public static String getDocumentIdType(String documentId) {
        if (StringUtils.isBlank(documentId)) {
            return null;
        } else if (documentId.startsWith(DOCUMENT_EXTERNALID_PREFIX)) {
            return DOCUMENT_ID_TYPE_EXTERNALID;
        } else {
            return DOCUMENT_ID_TYPE_DCTMID;
        }
    }

    public String create(String partyId, DocumentType documentType, String documentName, OpCreate40.ObjectOfCreation document) throws ServiceException, cz.rb.tif.ims.handler.ApiException {
        OpCreate40 createRequest = new OpCreate40();
        createRequest.setPartyId(partyId);
        createRequest.setDocumentClass(documentType.name());
        createRequest.setDocumentName(documentName);
        createRequest.setObjectOfCreation(document);

        var body = new EntDocumentRequest()
                .cultureCode("CZ")
                .userType(EXT)
                .channelCode(SYS)
                .userId(X_REQUEST_APP)
                .entityService(new EntDocumentRequest.EntityService()
                        .entity("Document")
                        .operation("opCreate_4_0")
                        .opCreate40(createRequest)
                );
        EntDocumentResponse entDocumentResponse = tifImsService.callTif(restRequestHeaders, body, EntDocumentResponse.class);

        if (entDocumentResponse == null || (entDocumentResponse.getResponse() == null)) {
            throw new ServiceException("EntDocumentResponse or EntDocumentResponse.response is null :" + entDocumentResponse);
        }

        OpCreate40Res response = entDocumentResponse.getResponse().getOpCreate40Res();

        if (response == null || (response.getDocument() == null)) {
            throw new ServiceException("OpCreate response or response.document is null :" + response);
        }
        return response.getDocument().getDocumentId();
    }

    public GetDocumentUrlDto getDocumentUrl(String documentId, String correlationId) throws ServiceException {
        return getDocumentUrl(documentId, documentId.startsWith(DOCUMENT_EXTERNALID_PREFIX) ? DOCUMENT_ID_TYPE_APPLICATIONID : DOCUMENT_ID_TYPE_DCTMID, correlationId);
    }

    public GetDocumentUrlDto getDocumentUrl(String documentId, String documentIdType, String correlationId) throws ServiceException {
        try {
            ApiResponse<GetUrlRes> res = apiV1.dmsDocumentV1GetUrlWithHttpInfo(
                    GET_URL,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    documentId,
                    documentIdType, //Id type which the given document is defined. There are 2 options; DCTMID - requested document belongs to DMS (RBCZ Documentum) and is defined by their id, applicationId - requested document belongs to DMS (RBCZ Documentum) and is defined by own id of the source application. Default filled by DCTMID.
                    1, //Requirement Type values 0 - preview, 1 - print, 2 - export No original, 3 - export Original
                    xFrontendApp,
                    xFrontendService,
                    null, //(versionId). If the field of the version has the value „null“, the response contains the last actual version
                    null // Type of requested rendition (doc, pdf, xls, ...)
            );
            return DmsServiceMapper.INSTANCE.getUrlResToDto(res.getData());
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public DocumentRenderUpdateDto documentRenderUpdateCancel(String documentId, String documentIdType, String correlationId) throws ServiceException {
        return documentRenderUpdateSmState(documentId, documentIdType, correlationId, CLCFD_CANCEL);
    }

    public DocumentRenderUpdateDto documentRenderUpdateDecline(String documentId, String documentIdType, String correlationId) throws ServiceException {
        return documentRenderUpdateSmState(documentId, documentIdType, correlationId, CLCFD_DECLINED);
    }

    public DocumentRenderUpdateDto documentRenderUpdateDestroy(String documentId, String documentIdType, String correlationId) throws ServiceException {
        return documentRenderUpdateSmState(documentId, documentIdType, correlationId, CLCFD_DESTROY);
    }

    public DocumentRenderUpdateDto documentRenderUpdateSmState(String documentId, String documentIdType, String correlationId, String state) throws ServiceException {
        DocumentRenderUpdateReq documentRenderUpdateReq = new DocumentRenderUpdateReq();
        Metadata2 metadata = new Metadata2();
        DocumentDetail2 documentDetail = new DocumentDetail2();
        DocumentSmStateSet documentSmState = new DocumentSmStateSet();
        documentSmState.setDocumentSmState(state);
        documentSmState.setOperationType(OperationTypePrimitive.SET);
        documentDetail.setDocumentSmStateSet(documentSmState);
        DocumentSmStateDescriptionSet documentSmStateDesc = new DocumentSmStateDescriptionSet();
        documentSmStateDesc.setDocumentSmStateDescription(STR_ZDZAM);
        documentSmStateDesc.setOperationType(OperationTypePrimitive.SET);
        documentDetail.setDocumentSmStateDescriptionSet(documentSmStateDesc);
        metadata.setDocumentDetail(documentDetail);
        documentRenderUpdateReq.setMetadata(metadata);

        return callDocumentRenderUpdate(documentId, documentIdType, NO_VERSION_FLAG, documentRenderUpdateReq, correlationId);
    }


    public DocumentRenderUpdateDto documentRenderUpdate(DocumentType documentType, Object document, String originalDocumentId, String correlationId) throws ServiceException {
        DocumentRenderUpdateReq documentRenderUpdateReq = new DocumentRenderUpdateReq();
        documentRenderUpdateReq.setDocumentName(documentType.getDocumentName());
        documentRenderUpdateReq.setDocumentClass(documentType.name());

        RenderingContent2 renderingContent = new RenderingContent2();
        renderingContent.setFormat(RenderingContent2.FormatEnum.fromValue(
                documentType.getFormat().toUpperCase()
        ));
        renderingContent.setProcessing(RenderingContent2.ProcessingEnum.fromValue(
                documentType.getProcessing().toUpperCase()
        ));
        TechnicalMetadata2 technicalData = new TechnicalMetadata2();
        technicalData.setAttachments(null);

        List<Delivery> deliveries = new LinkedList<>();
        Delivery delivery = new Delivery();
        delivery.setAddresses(null);
        delivery.setEmails(null);
        delivery.setFaxes(null);

        delivery.setDeliveryChannel(DELIVERY_CHANNEL_ARCHIVE);
        deliveries.add(delivery);
        technicalData.setDeliveries(deliveries);
        FileFormatProperties fileFormatProperties = new FileFormatProperties();
        fileFormatProperties.setFileType(FileFormatProperties.FileTypeEnum.fromValue(
                documentType.getFormat()
        ));
        technicalData.setFileFormatProperties(fileFormatProperties);
        technicalData.setReportDatetime(OffsetDateTime.now());
        renderingContent.setTechnicalMetadata(technicalData);

        InputData inputData = new InputData();
        inputData.setFileXmlData(XmlUtils.toXmlString(document));
        renderingContent.setInputData(inputData);
        documentRenderUpdateReq.setRenderingContent(renderingContent);
        return callDocumentRenderUpdate(originalDocumentId, DOCUMENT_ID_TYPE_DCTMID, MINOR_FLAG, documentRenderUpdateReq, correlationId);
    }

    public DocumentRenderUpdateDto callDocumentRenderUpdate(String documentId, String documentIdType, String versioningFlag, DocumentRenderUpdateReq documentRenderUpdateReq, String correlationId) throws ServiceException {
        try {
            DocumentRenderUpdateRes res = apiV2.dmsDocumentV2DocumentRenderUpdate(
                    documentId,
                    documentIdType,
                    versioningFlag,
                    RENDER_UPDATE,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    xFrontendApp,
                    xFrontendService,
                    documentRenderUpdateReq
            );
            return DmsServiceMapper.INSTANCE.documentRenderUpdateResToDto(res);
        } catch (cz.rb.dms.document.v2.handler.ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public String render(String partyId, String busApplId, String fullAccountNumberWithBankCode, DocumentType documentType, String documentName, Object document, EmailDTO email, String correlationId) throws ServiceException {
        return render(null, partyId, busApplId, fullAccountNumberWithBankCode, documentType, documentName, document, email, correlationId);
    }

    public String render(String externalId, String partyId, String busApplId, String fullAccountNumberWithBankCode, DocumentType documentType, String documentName, Object document, EmailDTO email, String correlationId) throws ServiceException {
        var renderReq = new cz.rb.dms.document.model.RenderReq();
        if (StringUtils.isNotBlank(externalId)) {
            renderReq.setExternalId(externalId);
        }
        renderReq.setPartyId(partyId);
        renderReq.setDocumentName(documentName);
        renderReq.setAuthor(CommonRestService.CAM_USER);
        renderReq.setDocumentClass(documentType.name());
        renderReq.setRenderingContent(createRenderingContent(documentType, document, email));
        renderReq.setMetadata(createMetadataRenderer(busApplId, documentType.getDocumentSmState(), null, fullAccountNumberWithBankCode));

        try {
            cz.rb.dms.document.model.RenderRes res = apiV1.dmsDocumentV1Render(
                    RENDER,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    null,
                    xFrontendApp,
                    xFrontendService,
                    renderReq
            );

            return res.getExternalId();
        } catch (cz.rb.dms.document.handler.ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public cz.rb.dms.document.model.RenderingContent createRenderingContent(DocumentType documentType, Object document, EmailDTO email) throws ServiceException {
        var content = new cz.rb.dms.document.model.RenderingContent();
        content.setFileType(cz.rb.dms.document.model.RenderingContent.FileTypeEnum.fromValue(
                documentType.getFormat().toUpperCase()
        ));
        content.setFormat(cz.rb.dms.document.model.RenderingContent.FormatEnum.NONE);
        content.setProcessing(RenderingContent.ProcessingEnum.fromValue(documentType.getProcessing().toUpperCase()));

        var inputData = new cz.rb.dms.document.model.RenderingContentInputData();
        inputData.setFileXmlData(createFileData(document));
        inputData.setFileXmlDataUrl(null);
        content.setInputData(inputData);

        var technicalMetadata = new cz.rb.dms.document.model.TechnicalMetadata();
        var deliveries = new LinkedList<cz.rb.dms.document.model.DeliveriesInner>();
        var delivery = new cz.rb.dms.document.model.DeliveriesInner();
        var emailInner = new EmailsInner();
        var receiver = new ReceiversInner();
        var attachment = new AttachmentsInner();

        if (DELIVERY_CHANNEL_EMAIL.equals(documentType.getDeliveryChannel())) {
            if (email == null) {
                throw new IllegalArgumentException("Email is required for delivery channel type EMAIL");
            }

            if (isValidAttachment(email)) {
                attachment.setDocumentName(email.getAttachmentName());
                attachment.setType(AttachmentsInner.TypeEnum.valueOf(email.getAttachmentType().toUpperCase()));
                attachment.setValue(email.getAttachmentValue());
                emailInner.addAttachmentsItem(attachment);
            }

            receiver.setReceiver(email.getReceiver());
            emailInner.addReceiversItem(receiver);
            delivery.addEmailsItem(emailInner);
        }

        delivery.setDeliveryChannel(documentType.getDeliveryChannel());
        deliveries.add(delivery);

        technicalMetadata.setDeliveries(deliveries);
        technicalMetadata.setReportDatetime(OffsetDateTime.now());
        content.setTechnicalMetadata(technicalMetadata);

        return content;
    }

    private boolean isValidAttachment(EmailDTO email) {
        return email.getAttachmentValue() != null
                && email.getAttachmentName() != null
                && email.getAttachmentType() != null;
    }

    private cz.rb.dms.document.model.Metadatarender createMetadataRenderer(String busApplId, String documentSmState, String documentSmStateDescription, String fullAccountNumberWithBankCode) {
        var metadata = new cz.rb.dms.document.model.Metadatarender();
        var relatedMetadata = new cz.rb.dms.document.model.RelatedMetadata();
        var documentDetail = new cz.rb.dms.document.model.Documentdetail();
        var agreementNumbersInner = new cz.rb.dms.document.model.AgreementNumbersInner();

        documentDetail.setRelatedMetadata(relatedMetadata);
        documentDetail.setDocumentSmState(documentSmState);
        documentDetail.setReasonText(documentSmStateDescription);

        if (fullAccountNumberWithBankCode != null) {
            var accountNumbersInner = new cz.rb.dms.document.model.AccountNumbersInner();
            accountNumbersInner.setAccountNumber(fullAccountNumberWithBankCode);
            relatedMetadata.addAccountNumbersItem(accountNumbersInner);
        }

        agreementNumbersInner.setAgreementNumber(busApplId);
        relatedMetadata.setAgreementNumbers(List.of(agreementNumbersInner));

        relatedMetadata.setApplicationId(String.format("ADB_%s", busApplId));
        metadata.setDocumentdetail(documentDetail);

        return metadata;
    }

    public String upload(String partyId, String busApplId, String fullAccountNumberWithBankCode, DocumentType documentType, String documentName, Object document, EmailDTO email, String correlationId) throws ServiceException {
        var uploadReq = new cz.rb.dms.document.model.UploadReq();
        uploadReq.setPartyId(partyId);
        uploadReq.setDocumentName(documentName);
        uploadReq.setAuthor(CommonRestService.CAM_USER);
        uploadReq.setDocumentClass(documentType.getDocumentName());
        uploadReq.setContent(createContent(documentType, document));
        uploadReq.setMetadata(createMetadata(busApplId, documentType.getDocumentSmState(), null, fullAccountNumberWithBankCode));

        try {
            cz.rb.dms.document.model.UploadRes res = apiV1.dmsDocumentV1Upload(
                    UPLOAD,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    null,
                    xFrontendApp,
                    xFrontendService,
                    uploadReq
            );

            return res.getDocumentId();
        } catch (cz.rb.dms.document.handler.ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private cz.rb.dms.document.model.Content createContent(DocumentType documentType, Object document) throws ServiceException {
        var content = new cz.rb.dms.document.model.Content();
        content.setFormat(cz.rb.dms.document.model.Content.FormatEnum.valueOf(documentType.getFormat().toUpperCase()));
        content.setFileData(createFileData(document));
        return content;
    }

    private String createFileData(Object document) throws ServiceException {
        return XmlUtils.toXmlString(document);
    }

    private cz.rb.dms.document.model.Metadata createMetadata(String busApplId, String documentSmState, String documentSmStateDescription, String fullAccountNumberWithBankCode) {
        var metadata = new cz.rb.dms.document.model.Metadata();
        var relatedMetadata = new cz.rb.dms.document.model.RelatedMetadata();
        var documentDetail = new cz.rb.dms.document.model.Documentdetail();
        documentDetail.setRelatedMetadata(relatedMetadata);
        documentDetail.setDocumentSmState(documentSmState);
        documentDetail.setReasonText(documentSmStateDescription);

        var accountNumbersInner = new cz.rb.dms.document.model.AccountNumbersInner();
        accountNumbersInner.setAccountNumber(fullAccountNumberWithBankCode);

        relatedMetadata.addAccountNumbersItem(accountNumbersInner);
        relatedMetadata.setApplicationId(busApplId);

        metadata.setDocumentdetail(documentDetail);

        return metadata;
    }

    public ApiResponseDto delete(GeneratedDocumentInfoDTO docInfo, String deleteScope, String documentVersion, Integer retentionDays, String correlationId) throws ServiceException {
        try {
            ApiResponse<Void> res = apiV1.dmsDocumentV1DeleteWithHttpInfo(
                    DELETE,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    docInfo.getDocumentId(),
                    docInfo.getDocumentId().startsWith(DOCUMENT_EXTERNALID_PREFIX) ? DOCUMENT_ID_TYPE_APPLICATIONID : DOCUMENT_ID_TYPE_DCTMID,
                    deleteScope,
                    xFrontendApp,
                    xFrontendService,
                    documentVersion,
                    retentionDays
            );


            return DmsServiceMapper.INSTANCE.getApiResponseDto(res);
        } catch (cz.rb.dms.document.handler.ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }


    public GetDocumentListDto getDocumentList(List<String> documentIds, String correlationId) throws ServiceException {
        try {
            GetListReq getListReq = new GetListReq();
            getListReq.setDocumentIdList(documentIds);
            GetListRes res = apiV2.dmsDocumentV2GetList(
                    GET_LIST,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    null,
                    null,
                    null,
                    xFrontendApp,
                    xFrontendService,
                    getListReq
            );
            return DmsServiceMapper.INSTANCE.getListResToDto(res);
        } catch (cz.rb.dms.document.v2.handler.ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public GetDocumentListDto getDocumentListByApplicationId(String busApplId, String correlationId) throws ServiceException {
        return getDocumentListByApplicationId(busApplId, Collections.emptyList(), correlationId);
    }

    public GetDocumentListDto getDocumentListByApplicationId(String busApplId, List<String> documentClassList, String correlationId) throws ServiceException {
        try {
            GetListReq getListReq = new GetListReq();
            getListReq.setApplicationId("ADB_" + busApplId);
            getListReq.setDocumentClassList(documentClassList);
            GetListRes res = apiV2.dmsDocumentV2GetList(
                    GET_LIST,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    CommonRestService.SYS,
                    null,
                    null,
                    null,
                    xFrontendApp,
                    xFrontendService,
                    getListReq
            );
            return DmsServiceMapper.INSTANCE.getListResToDto(res);
        } catch (cz.rb.dms.document.v2.handler.ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public boolean addClientApproval(ClientApprovalDoc clientDocument, String clientId, String documentType, String correlationId) {
        try {
            AddClientSignatureReq addClientSignatureReq = new AddClientSignatureReq();
            Signature signature = new Signature();
            addClientSignatureReq.setSignature(signature);
            signature.setSignatureId("2");
            signature.setUserData(clientDocument.getUserData());
            signature.setUserDataVisible(clientDocument.getUserDataVisible());
            signature.setSignatureVisible(true);
            signature.setSignatureNumber(1);
            if (********.name().equalsIgnoreCase(documentType)) {
                signature.setSignaturePage("-9");
            } else {
                signature.setSignaturePage("MAXINT");
            }

            addClientSignatureReq.setAddTimestamp(false);
            addClientSignatureReq.setFormat("pdf");
            addClientSignatureReq.setHashType(AddClientSignatureReq.HashTypeEnum.SHA_256);
            addClientSignatureReq.setLastSignature(true);
            addClientSignatureReq.setSignature(signature);

            CertificationInner certificationInner = new CertificationInner();
            addClientSignatureReq.addCertificationItem(certificationInner);
            certificationInner.setClientId(clientId);
            certificationInner.setCertificationChallenge(clientDocument.getCertificationChallenge());
            String strZonedDateTime = clientDocument.getCertificationChallengeDate();
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(strZonedDateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS[XXXX][XXXXX]"));
            certificationInner.setCertificationChallengeDate(offsetDateTime);
            //certificationInner.setCertificationCode(clientDocument.getCertificationCode());
            certificationInner.setCertificationChallengePurpose(clientDocument.getCertificationChallengePurpose());

            apiV1.dmsDocumentV1AddClientSignature(
                    ADD_CLIENT_SIGNATURE,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CommonRestService.CAM_USER,
                    CommonRestService.EXT,
                    Collections.singletonList(clientDocument.getDocumentId()),
                    xFrontendApp,
                    xFrontendService,
                    addClientSignatureReq
            );
            return true;
        } catch (cz.rb.dms.document.handler.ApiException ex) {
            return false;
        }
    }

    public String getDctmId(String externalId, String correlationId) throws ServiceException {
        return getDocumentList(List.of(externalId), correlationId).getDocumentList().getFirst().getDocument().getDctmId();
    }
}
