openapi: 3.0.1
info:
  title: OpenAPI definition
  description: Api for managing applications (loan/deferred-payment/credit card)
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: Generated server url
  - url: https://dev-api.rb.cz/rbapl/las
    description: PreSIT
  - url: https://tfx1-api.rb.cz/rbapl/las
    description: TFX1
  - url: https://preprod-api.rb.cz/rbapl/las
    description: PrePROD
  - url: https://api.rb.cz/rbapl/las
    description: PROD
security:
  - basicAuth: []
paths:
  /application/v1/applications/{applicationId}/status:
    get:
      summary: Get Application Status service.
      description: |
        <h3>ID operace: las-application-v1-getApplStatus</h3>
        <p>Get current status of Application.</p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>approval/ApplicationService-v2/getApplState</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplStatus%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplStatus%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplStatus%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getApplStatus
      parameters:
        - name: applicationId
          in: path
          description: ApplKey, busApplId or hash of application.
          required: true
          schema:
            type: string
          example: *********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetApplStatusResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Set Application Status service.
      description: |
        <h3>ID operace: las-application-v1-setApplStatus</h3>
        <p>Set current status of Application.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationState/setApplicationState</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BASE</li><li>ADB.SET_APPL_DATE</li><li>ADB.SET_APPL_STAT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setApplStatus%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setApplStatus%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setApplStatus%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setApplStatus
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of application.
          required: true
          schema:
            type: string
          example: *********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey and busApplId.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetApplStatusRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetApplStatusResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/person-verification:
    put:
      summary: Set Person Verification service.
      description: |
        <h3>ID operace: las-application-v1-setPersonVerification</h3>
        <p>Ulož&iacute; ověřen&iacute; osoby k ž&aacute;dosti. Nastav&iacute; ověřen&iacute; k ž&aacute;dosti.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.setPersonVerification</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_PERSON_VERIF</li><li>ADB.SET_PERSON_INC_VERIF</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setPersonVerification%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setPersonVerification%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setPersonVerification%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setPersonVerification
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of the application.
          required: true
          schema:
            type: string
          example: 202212210014
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey and busApplId.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
          example: BUSAPPLID
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetPersonVerificationRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetPersonVerificationResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/contract-sign:
    put:
      summary: Set Contract Sign service.
      description: |
        <h3>ID operace: las-application-v1-setContractSign</h3>
        <p>Nastav&iacute; k ž&aacute;dosti &uacute;daje o podpisu smlouvy (datum, č&iacute;slo smlouvy, identifikace poradců atd.).</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.setPersonVerification</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BASE</li><li>ADB.SET_APPL_CONTR_SIGN</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>V př&iacute;padě že nen&iacute; uvedeno applKey na vstupu provol&aacute; se ADB.GET_APPL_BASE a n&aacute;sledně se provol&aacute; ADB.SET_APPL_CONTR_SIGN.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setContractSign%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setContractSign%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setContractSign%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setContractSign
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of the application.
          required: true
          schema:
            type: string
          example: 704000180
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey and busApplId.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetContractSignRequest'
        required: true
      responses:
        '200':
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applKey}/client-action:
    put:
      summary: Set Client Action service.
      description: |
        <h3>ID operace: las-application-v1-setClientAction</h3>
        <p>Save user/client action as event to application, for example signing, or even just viewing terms and services.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2/setApplicationEvent</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_EVE</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setClientAction%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setClientAction%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setClientAction%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setClientAction
      parameters:
        - name: applKey
          in: path
          description: ApplKey of application.
          required: true
          schema:
            type: integer
            format: int64
          example: *********
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetClientActionRequest'
        required: true
      responses:
        '200':
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{adbInstPtKey}/obligations/interest-rate:
    put:
      summary: Set Obligation Interest Rate service.
      description: |
        <h3>ID operace: las-application-v1-setObligationInterestRate</h3>
        <p>Vystavuje ADB DB API SET_OBLGTN_CHG_INTRS_RX.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.setApplIntrsChng</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_OBLGTN_CHG_INTRS_RX</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pouze provol&aacute; api viz.api list a vr&aacute;t&iacute; data na v&yacute;stup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setObligationInterestRate%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setObligationInterestRate%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setObligationInterestRate%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setObligationInterestRate
      parameters:
        - name: adbInstPtKey
          in: path
          description: Instance Party Key / Instanční klíč žadatele.
          required: true
          schema:
            type: integer
            format: int64
          example: 545000156
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetObligationInterestRateServiceRequest'
        required: true
      responses:
        '200':
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/variants/{applVariantKey}:
    put:
      summary: Set Variant Params Change service.
      description: |
        <h3>ID operace: las-application-v1-setVariantParamsChange</h3>
        <p>Vystavuje ADB DB API SET_APPL_VRNT_PAR_CHG. Pon&iacute;ž&iacute;/pov&yacute;&scaron;&iacute; v cel&eacute; matici parametrů OFR varianty ž&aacute;dosti atribut &quot;Odchylka běžn&eacute; &uacute;rokov&eacute; sazby&quot; (REG_INTRS_RX_DVG) a zadan&yacute; rozd&iacute;l (INTRS_RX_DSCNT).</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.setApplIntrsChng</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_VRNT_PAR_CHG</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pouze provol&aacute; api viz.api list a vr&aacute;t&iacute; data na v&yacute;stup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setVariantParamsChange%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setVariantParamsChange%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setVariantParamsChange%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setVariantParamsChange
      parameters:
        - name: applVariantKey
          in: path
          description: Variant Key / Instanční klíč varianty žádosti (OFR).
          required: true
          schema:
            type: integer
            format: int64
          example: 764000168
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetVariantParamsChangeServiceRequest'
        required: true
      responses:
        '200':
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/overdrafts:
    put:
      summary: Set Overdraft Card Application service.
      description: |
        <h3>ID operace: las-application-v1-setOverdraftAppl</h3>
        <p>Creates or updates existing overdraft card application. If appl id is present, updates application. In case its not, creates new one. For create operation X-Idempotency-Key is recomended.</p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_EXISTS</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.SET_PERSON_SMART</li><li>ADB.SET_PERSON_ID_CARD</li><li>ADB.SET_APPL_CARD</li><li>ADB.GET_APPL_CARD</li><li>ADB.SET_PT_INC</li><li>ADB.SET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT</li><li>ADB.SET_APPL_METAD</li><li>ADB.SET_OBLGTN</li><li>ADB.GET_APPL_BASE</li><li>ADB.GET_PERSON</li><li>mar-opportunity-v1-create</li><li>las-application-v1-getOverdraftAppl</li><li>cus-party-application-v1-update</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setOverdraftAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setOverdraftAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setOverdraftAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setOverdraftAppl
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetOverdraftApplRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetOverdraftApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applKey}/mortgages-merge:
    put:
      summary: Merge mortgage subapplication
      description: |
        <h3>ID operace: las-application-v1-mergeMortgageAppl</h3>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_MORT_MERGE</li>
        <li>IMS.bai-loan-processing-V1-stornoProcess</li>        
        </ul>
        <h3>Popis orchestrace:</h3>
        <ul>
        <li>Provolá ADB API, které provede merge</li>
        <li>Pošle odpověď</li>        
        <li>Provede storno původní žádosti zavoláním IMS API</li>        
        </ul>
      operationId: las-application-v1-mergeMortgageAppl
      parameters:
        - name: applKey
          in: path
          description: Instanční klíč hlavní žádosti, pod kterou bude požadovaná podžádost sloučena
          required: true
          schema:
            type: integer
            format: int64
          example: 764000167
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MergeMortgageRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MergeMortgageResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/mortgages-clone:
    put:
      summary: Clone mortgage application
      description: |
        <h3>ID operace: las-application-v1-cloneMortgageAppl</h3>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_MORT_CLONE</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <ul>
        <li>Provolá ADB API, které provede merge</li>
        <li>Pošle odpověď</li>        
        </ul>
      operationId: las-application-v1-cloneMortgageAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId application.
          required: true
          schema:
            type: string
          example: *********
        - name: applicationIdType
          in: query
          description: Specifies which kind of identifier will be used.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CloneMortgageResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/mortgages-apply-guaranteed-appl:
    put:
      summary: Apply guaranteed mortgage application
      description: |
        <h3>ID operace: las-application-v1-applyGuaranteedMortgageAppl</h3>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_MORT_SWITCH_30/li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <ul>
        <li>Provolá ADB API, které provede aplikaci garantované nabídky</li>
        <li>Pošle odpověď</li>        
        </ul>
      operationId: las-application-v1-applyGuaranteedMortgageAppl
      parameters:
        - name: applicationId
          in: path
          description:  Identifikátor garantované nabídky, která se má uplatnit.
          required: true
          schema:
            type: string
          example: *********
        - name: applicationIdType
          in: query
          description: Specifies which kind of identifier will be used.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
          example: APPLKEY
        - name: targetApplicationId
          in: query
          description: Identifikátor podžádosti, která se má datově aktualizovat z garantované nabídky.
          required: true
          schema:
            type: string
          example: *********
        - name: targetApplicationIdType
          in: query
          description: Specifies which kind of identifier will be used.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
          example: APPLKEY
        - name: validExtFlag
          in: query
          description: "Validity Extension Flag / Prodloužení platnosti."
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApplyGuaranteedMortgageApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/mortgages:
    put:
      summary: Set Mortgage Application service.
      description: |
        <h3>ID operace: las-application-v1-setMortgageAppl</h3>
        <p>Creates or updates existing mortgage application.</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BASE</li><li>ADB.GET_PERSON</li><li>ADB.SET_APPL_METAD</li><li>ADB.SET_APPL_MORT</li><li>ADB.SET_APPL_VRNT</li><li>ADB.SET_HOUSH</li><li>ADB.SET_PERSON_SMART</li><li>ADB.SET_PT_PT_REL</li><li>ADB.SET_PT_INC</li><li>ADB.SET_OBLGTN</li><li>mar-opportunity-v1-create</li><li>las-application-v1-getMortgageAppl</li><li>cus-party-application-v1-update</li><li>ODE.SET_PERSON</li><li>ODE.SET_APPL_MORT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Detailed overview of the orchestration logic is part of the sequence diagram.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setMortgageAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setMortgageAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setMortgageAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setMortgageAppl
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetMortApplRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetMortApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/hyc/{applicationId}:
    put:
      summary: Set Hypoclient Application service.
      description: |
        <h3>ID operace: las-application-v1-setHycApplication</h3>
        <p>Operation gets detail of active mortgage subapplication, translates lov values and sets it to hypoclient.</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>las-application-v1-getMortgageAppl</li><li>las-operation-v1-getLovs</li><li>las-operation-v1-translateLovItems</li><li>hyc-hyfe-application-v1</li><li>ADB.SET_APPL_MORT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Detailed overview of the orchestration logic is part of the sequence diagram.</p>
      operationId: las-application-v1-setHycAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of main application.
          required: true
          schema:
            type: string
          example: **********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey and busApplId.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: BUSAPPLID
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /application/v1/applications/hyc/{applicationId}/collaterals:
    get:
      summary: Get Hypoclient Application Collaterals service.
      description: |
        <h3>ID operace: las-application-v1-getHycCollaterals</h3>
        <p></p>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>las-operation-v1-getLovs</li><li>las-operation-v1-translateLovItems</li><li>hyc-hyfe-application-v1</li><li>ODS.GET_LAND_INFO</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Detailed overview of the orchestration logic is part of the sequence diagram.</p>
      operationId: las-application-v1-getHycCollaterals
      parameters:
        - name: busApplId
          in: path
          description: BusApplId of main application.
          required: true
          schema:
            type: string
          example: **********
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetHycCollateralsResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/loans:
    put:
      summary: Set Loan Application service.
      description: |
        <h3>ID operace: las-application-v1-setLoanAppl</h3>
        <p>Creates or updates existing loan application. If appl id is present, updates application. In case its not, creates new one. For create operation X-Idempotency-Key is recomended.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2/setLoanAppl</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_EXISTS</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.SET_PERSON_SMART</li><li>ADB.SET_PERSON_ID_CARD</li><li>ADB.SET_APPL_LOAN</li><li>ADB.GET_APPL_LOAN</li><li>ADB.SET_PT_INC</li><li>ADB.SET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT</li><li>ADB.SET_APPL_METAD</li><li>ADB.SET_OBLGTN</li><li>ADB.GET_APPL_BASE</li><li>ADB.GET_PERSON</li><li>mar-opportunity-v1-create</li><li>las-application-v1-getLoanAppl</li><li>cus-party-application-v1-update</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setLoanAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setLoanAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setLoanAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setLoanAppl
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetLoanApplRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetLoanApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/credit-cards:
    put:
      summary: Set Credit Card Application service.
      description: |
        <h3>ID operace: las-application-v1-setCreditCardAppl</h3>
        <p>Creates or updates existing credit card application. If appl id is present, updates application. In case its not, creates new one. For create operation X-Idempotency-Key is recomended.</p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_EXISTS</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.SET_PERSON_SMART</li><li>ADB.SET_PERSON_ID_CARD</li><li>ADB.SET_APPL_CARD</li><li>ADB.GET_APPL_CARD</li><li>ADB.SET_PT_INC</li><li>ADB.SET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT</li><li>ADB.SET_APPL_METAD</li><li>ADB.SET_OBLGTN</li><li>ADB.GET_APPL_BASE</li><li>ADB.GET_PERSON</li><li>mar-opportunity-v1-create</li><li>las-application-v1-getLoanAppl</li><li>cus-party-application-v1-update</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setCreditCardAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setCreditCardAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setCreditCardAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setCreditCardAppl
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetCreditCardApplRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetCreditCardApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/building:
    put:
      summary: Set Building Loan Application service.
      description: |
        <h3>ID operace: las-application-v1-setBuildingLoanAppl</h3>
        <p>Creates or updates existing building loan application. If appl id is present, updates application. In case its not, creates new one. Required to use X-Cl-ContextId=RSTS. For create operation X-Idempotency-Key is recomended.</p>
        <h3>Původní BPEL služba:</h3>
        <p>-</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_EXISTS</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.SET_PERSON_SMART</li><li>ADB.SET_PERSON_ID_CARD</li><li>ADB.SET_APPL_BSL</li><li>ADB.GET_APPL_BSL</li><li>ADB.SET_PT_INC</li><li>ADB.SET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT</li><li>ADB.SET_APPL_METAD</li><li>ADB.SET_OBLGTN</li><li>ADB.GET_APPL_BASE</li><li>ADB.GET_PERSON</li><li>mar-opportunity-v1-create</li><li>las-application-v1-getLoanAppl</li><li>cus-party-application-v1-update</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setBuildingLoanAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setBuildingLoanAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setBuildingLoanAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setBuildingLoanAppl
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetBuildingLoanApplRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetBuildingLoanApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applKey}/comment:
    put:
      summary: Send Application Comment Service.
      description: |
        <h3>ID operace: las-application-v1-sendApplComment</h3>
        <p>Saves comment for application and sends it via email.</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_CMT</li><li>
        <li>had-partners-v1.getDealerDetail</li><li>
        <li>cus-party-v1-getCareList</li><li>
        <li>dms-document-v1.render</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-sendApplComment%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-sendApplComment%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-sendApplComment%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-sendApplComment
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendApplCommentRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendApplCommentResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/comments-set:
    put:
      summary: Set Application Comments Service.
      description: |
        <h3>ID operace: las-application-v1-setApplComments</h3>
        <p>Saves comment for application</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_APPL_CMT</li><li>
        <li>had-partners-v1.getDealerDetail</li><li>
        <li>cus-party-v1-getCareList</li><li>
        <li>dms-document-v1.render</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-sendApplComment%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-sendApplComment%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-sendApplComment%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setApplComments
      parameters:
        - name: applicationId
          in: path
          description: ApplKey, busApplId or hash of application.
          required: true
          schema:
            type: string
          example: *********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetApplCommentsRequest'
        required: true
      responses:
        '200':
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{brokerId}/list-for-broker:
    get:
      summary: Get Application List for Broker service.
      description: |
        <h3>ID operace: las-application-v1-getApplListForBroker</h3>
        <p>Returns a collection of applications for a broker.).</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationList-v1.getApplicationListForBroker</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BY_BROKER</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pouze provol&aacute; api viz.api list a vr&aacute;t&iacute; data na v&yacute;stup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListForBroker%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListForBroker%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListForBroker%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getApplListForBroker
      parameters:
        - name: brokerId
          in: path
          description: Broker ID.
          required: true
          schema:
            type: string
          example: 569
        - name: recFrom
          in: query
          description: Rec from.
          required: false
          schema:
            type: integer
            format: int32
          example: 10
        - name: recTo
          in: query
          description: Rec to.
          required: false
          schema:
            type: integer
            format: int32
          example: 20
        - name: sortCriterium
          in: query
          description: 'Sort criterium. Supported SORT types: (1 - číslo žádosti, 2 - hlavní žadatel, 3 - částka, 4 - účel, 5 - status, 6 - vytvořeno)'
          required: false
          schema:
            type: integer
            format: int32
          example: 1
        - name: sortOrder
          in: query
          description: Sort order. Sort mode. (0 - DESC, 1 - ASC)
          required: false
          schema:
            type: integer
            format: int32
          example: 0
        - name: busProdTpFilter
          in: query
          description: List of LOVS LovBusProdTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: busProdTp_lov_id
        - name: applStatFilter
          in: query
          description: List of LOVS LovApplStat.
          required: false
          schema:
            type: array
            items:
              type: string
          example: applStat_lov_id
        - name: applicantName
          in: query
          description: Applicant Name.
          required: false
          schema:
            type: string
          example: Applicant Name example
        - name: applicantSurename
          in: query
          description: Applicant Surename.
          required: false
          schema:
            type: string
          example: Applicant Surename example
        - name: offsetMonths
          in: query
          description: Offset Months.
          required: false
          schema:
            type: integer
            format: int64
          example: 9999
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApplicationForBrokerResult'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/status-history:
    get:
      summary: Get Appl Status History service.
      description: |
        <h3>ID operace: las-application-v1-getApplStatusHistory</h3>
        <p>Gets log of application state changes.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationState/ApplicationStateLog</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT_CHG_LOG</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pomoc&iacute; vstupnich dat nacte historii stavu zadosti z ADB db api (viz.apiList).</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplStatusHistory%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplStatusHistory%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplStatusHistory%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getApplStatusHistory
      parameters:
        - name: applicationId
          in: path
          description: Application id.
          required: true
          schema:
            type: string
          example: 853599005720
        - name: applicationIdType
          in: query
          description: Specifies which kind of identifier will be used.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StateLog'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/ecommerce-rights:
    get:
      summary: Služba pro kontrolu práv Ecommerce.
      description: |
        <h3>ID operace: las-application-v1-checkEcommerceRights</h3>
        <p>Služba poskytuje dva př&iacute;pady použit&iacute;:<br>  1. Zkontroluje, zda existuje v&iacute;ce paraleln&iacute;ch ž&aacute;dost&iacute; pro stejn&yacute; produkt (pokud je vol&aacute;na s applKey [applicationId=APPLKEY] nebo busApplId [applicationId=BUSAPPLID]).<br>  2. Zkontroluje, zda pro konkr&eacute;tn&iacute; objedn&aacute;vku existuje ž&aacute;dost (pokud je vol&aacute;na s č&iacute;slem objedn&aacute;vky [applicationId=ORDNUM] a vyplněn&yacute;m parametrem firstTouchPoint).<br></p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationApproval-v1.checkEcommerceApplRights</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_LOAN</li><li>ADB.GET_APPL_BY_ORD_NUM</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pomoc&iacute; vstupn&iacute;ch dat načteme pr&aacute;va Ecommerce. V př&iacute;padě potřeby využijeme data ž&aacute;dosti o půjčku.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-checkEcommerceRights%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-checkEcommerceRights%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-checkEcommerceRights%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-checkEcommerceRights
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of the application or order number related to the application.
          required: true
          schema:
            type: string
          example: 5072000348
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey and busApplId or order number and first touch point.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - ORDNUM
          example: BUSAPPLID
        - name: firstTouchPoint
          in: query
          description: E-shop's first touch point; REQUIRED if applicationIdType is ORDNUM, otherwise do not use.
          required: false
          schema:
            type: string
          example: FTP
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckEcommerceRightsResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/check-exists:
    get:
      summary: Check Loan Application Exists service.
      description: |
        <h3>ID operace: las-application-v1-checkLoanApplExists</h3>
        <p>Kontrola existence extern&iacute;ho id žadosti (busApplId / hashId).</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.checkLoanApplExist</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_EXISTS</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pouze provol&aacute; api viz.api list a vr&aacute;t&iacute; data na v&yacute;stup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-checkLoanApplExists%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-checkLoanApplExists%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-checkLoanApplExists%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-checkLoanApplExists
      parameters:
        - name: applicationId
          in: path
          description: BusApplId or hash of application.
          required: true
          schema:
            type: string
          example: **********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - BUSAPPLID
              - HASH
          example: BUSAPPLID
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckLoanApplExistResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applicationId}/basic-detail:
    get:
      summary: Get Basic Detail service.
      description: |
        <h3>ID operace: las-application-v1-getBasicDetail</h3>
        <p>Vr&aacute;t&iacute; informace o obecn&eacute; ž&aacute;dosti.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.getBaseAppl</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BASE</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pouze provol&aacute; api viz.api list a vr&aacute;t&iacute; data na v&yacute;stup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getBasicDetail%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getBasicDetail%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getBasicDetail%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getBasicDetail
      parameters:
        - name: applicationId
          in: path
          description: ApplKey, busApplId or hash of application.
          required: true
          schema:
            type: string
          example: *********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: APPLKEY
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBasicDetailServiceResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{applKey}/variants:
    get:
      summary: Get Variants service.
      description: |
        <h3>ID operace: las-application-v1-getVariants</h3>
        <p>Vr&aacute;t&iacute; seznam variant dan&eacute; ž&aacute;dosti. Seznam lze omezit na vybran&eacute; typy variant.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2.getBaseAppl</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_VRNT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pouze provol&aacute; api viz.api list a vr&aacute;t&iacute; data na v&yacute;stup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getVariants%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getVariants%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getVariants%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getVariants
      parameters:
        - name: applKey
          in: path
          description: Application Key / Instanční klíč žádosti.
          required: true
          schema:
            type: integer
            format: int64
          example: *********
        - name: applVrntParFlag
          in: query
          description: Indicates whether Application variant parameters entity is returned or not / Flag říkající, zda vracet matici parametrů k variantě.
          required: false
          schema:
            type: boolean
          example: true
        - name: applVrntTpId
          in: query
          description: Application Variant Type Identifier (Filter) / Typ varianty (LOV APPL_VRNT_TP).
          required: false
          schema:
            type: string
          example: REQ
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetVariantsServiceResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/{adbInstPtKey}/telco-score:
    get:
      summary: Služba pro výpočet Telco Score.
      description: |
        <h3>ID operace: las-application-v1-getTelcoScore</h3>
        <p>Služba vr&aacute;t&iacute; hodnotu Telco Score.</p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_REG_TELCO</li><li>GPE.TelcoService.getScore</li><li>ADB.SET_PT_DSN_TELCO</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pomoc&iacute; vstupn&iacute;ch dat načteme hodnotu sc&oacute;re z ADB nebo z GPE a vr&aacute;t&iacute;me ji. Pomoc&iacute; parametru readOnly ř&iacute;d&iacute;me uložen&iacute; hodnoty do ADB.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getTelcoScore%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getTelcoScore%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getTelcoScore%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getTelcoScore
      parameters:
        - name: adbInstPtKey
          in: path
          description: ADB instance party key.
          required: true
          schema:
            type: integer
            format: int64
          example: 202212210014
        - name: ptDsnKey
          in: query
          description: Decision key.
          required: true
          schema:
            type: integer
            format: int64
          example: 13213131
        - name: phoneNumber
          in: query
          description: Telefonní číslo klienta, s predvolbou statu a bez + na začátku.
          required: false
          schema:
            type: string
          example: 420602111018
        - name: registryMaxCache
          in: query
          description: Maximální stáří záznamu ve dnech.
          required: false
          schema:
            type: integer
            format: int64
          example: 10
        - name: readOnly
          in: query
          description: Povolení zápisu výsledku do ADB (true = nezapisovat, false = zapsat výsledek), implicitní hodnota false.
          required: false
          schema:
            type: boolean
          example: true
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTelcoScoreResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/overdrafts/{applicationId}:
    get:
      summary: Get Overdraft Application service.
      description: |
        <h3>ID operace: las-application-v1-getOverdraftAppl</h3>
        <p>Get the detail of a specific overdraft application. Operation returns the details of a specific overdraft application based on its ID. In the request the consumer selects what sets of details they want to be returned and several filtering conditions.</p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT</li><li>ADB.GET_APPL_OD</li><li>ADB.GET_PT_DSN</li><li>ADB.GET_APPL_DSN</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.GET_PERSON</li><li>ADB.GET_PT_DET</li><li>ADB.GET_PT_COPR_DET</li><li>ADB.GET_PERSON_VERIF</li><li>ADB.GET_PERSON_INC_VERIF</li><li>ADB.GET_PT_INC</li><li>ADB.GET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT_SIGN_CNL</li><li>ADB.GET_APPL_METAD</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getOverdraftAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getOverdraftAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getOverdraftAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getOverdraftAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of application.
          required: true
          schema:
            type: string
          example: 202212210014
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: APPLKEY
        - name: applVariantTpId
          in: query
          description: Application variant type IDs, list of values from LovApplVariantTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[OFR, REQ]'
        - name: personIncomeVerificationResultId
          in: query
          description: Person income verification result IDs, list of values from LovIncVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[TOVERIF, VERIFDONE]'
        - name: personIncomeVerificationSrcId
          in: query
          description: Income verification source IDs, list of values from LovIncVerifSrc.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[PSD, PSD2]'
        - name: personVerificationResultId
          in: query
          description: Person verification results, list of values from LovPersonVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[TOVERIF, DATADONE]'
        - name: personVerificationTpId
          in: query
          description: Person verificartion types, list of values from LovPersonVerifTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[PSD, PSD2]'
        - name: primaryOwnerFlag
          in: query
          description: Retrieve primary owner data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: primaryOwnerIncomeFlag
          in: query
          description: Retrieve primary owner income data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personVerificationFlag
          in: query
          description: Retrieve person verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personIncomeVerificationFlag
          in: query
          description: Retrieve person income verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applFlag
          in: query
          description: Retrieve application data.
          required: false
          schema:
            type: boolean
            default: true
          example: true
        - name: applVariantsFlag
          in: query
          description: Retrieve application variants.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applVariantParamsFlag
          in: query
          description: Retrieve application varitant params.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: signChannelsFlag
          in: query
          description: Retrieve sign channels.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applMetadataFlag
          in: query
          description: Retrieve application metadata.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: hash
          in: query
          description: Hash of application.
          required: false
          schema:
            type: string
          example: 65PY43Hm
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOverdraftApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/mortgages/{applicationId}:
    get:
      summary: Get Mortgage Application service.
      description: |
        <h3>ID operace: las-application-v1-getMortgageAppl</h3>
        <p>Get the detail of a specific mortgage application. Operation returns the details of a specific mortgage application based on its ID. In the request the consumer selects what sets of details they want to be returned and several filtering conditions.</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT</li><li>ADB.GET_APPL_MORT</li><li>ADB.GET_APPL_REJ_RSN</li><li>ADB.GET_APPL_METAD</li><li>ADB.GET_HOUSH</li><li>ADB.GET_APPL_VRNT</li><li>las-parametrization-app-v1</li><li>ADB.GET_APPL_DSN</li><li>ADB.GET_PT_INC</li><li>ADB.GET_OBLGTN</li><li>ADB.GET_PERSON</li><li>ADB.GET_PT_DET</li><li>ADB.GET_PT_DSN</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Detailed overview of the orchestration logic is part of the sequence diagram.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getMortgageAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getMortgageAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getMortgageAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getMortgageAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of main application.
          required: true
          schema:
            type: string
          example: **********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: BUSAPPLID
        - name: applVariantTpId
          in: query
          description: Application variant type IDs, list of values from LovApplVariantTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[OFR, REQ]'
        - name: personsFlag
          in: query
          description: Retrieve persons data.
          required: false
          schema:
            type: boolean
          example: false
        - name: personVerificationFlag
          in: query
          description: Retrieve obligation data.
          required: false
          schema:
            type: boolean
          example: false
        - name: docFlag
          in: query
          description: Retrieve obligation data.
          required: false
          schema:
            type: boolean
          example: false
        - name: householdsFlag
          in: query
          description: Retrieve households data.
          required: false
          schema:
            type: boolean
          example: false
        - name: applFlag
          in: query
          description: Retrieve application and subapplications data.
          required: false
          schema:
            type: boolean
          example: true
        - name: applVariantsFlag
          in: query
          description: Retrieve application variants.
          required: false
          schema:
            type: boolean
          example: false
        - name: applVariantsHycProdTpFlag
          in: query
          description: Retrieve Hypo Client product type from Param Application.
          required: false
          schema:
            type: boolean
          example: false
        - name: applVariantsFeeFlag
          in: query
          description: Retrieve application variant fees.
          required: false
          schema:
            type: boolean
          example: false
        - name: applVariantsInsurFlag
          in: query
          description: Retrieve application variant insurences.
          required: false
          schema:
            type: boolean
          example: false
        - name: applVariantsSurchrgFlag
          in: query
          description: Retrieve application variant surcharges.
          required: false
          schema:
            type: boolean
          example: false
        - name: applMetadataFlag
          in: query
          description: Retrieve application metadata.
          required: false
          schema:
            type: boolean
          example: false
        - name: incomeFlag
          in: query
          description: Retrieve incomes data.
          required: false
          schema:
            type: boolean
          example: false
        - name: oblgtnFlag
          in: query
          description: Retrieve obligation data.
          required: false
          schema:
            type: boolean
          example: false
        - name: collateralFlag
          in: query
          description: Retrieve obligation data.
          required: false
          schema:
            type: boolean
          example: false
        - name: loanSubjectFlag
          in: query
          description: Retreive loan subjects.
          required: false
          schema:
            type: boolean
          example: false
        - name: hash
          in: query
          description: Hash of application.
          required: false
          schema:
            type: string
          example: 77PY43Hm
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMortApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/mortgages/servicing/{applicationId}:
    get:
      summary: Get Mortgage Product Application Servicing Request.
      description: |
        <h3>ID operace: las-application-v1-getMortgageServicing</h3>
        <p></p>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_PERSON</li>
        <li>ADB.GET_APPL_SERV_MORT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Detailed overview of the orchestration logic is part of the sequence diagram.</p>
        <h3>Logy v Kibaně:</h3>
        <ul>
          <li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getMortgageServicing%22'),sort:!())>PreSIT</a></li>
          <li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getMortgageServicing%22'),sort:!())>TFX1</a></li>
          <li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getMortgageServicing%22'),sort:!())>PROD</a></li>
        </ul>
      operationId: las-application-v1-getMortgageServicing
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of main application.
          required: true
          schema:
            type: string
          example: **********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: BUSAPPLID
        - name: personsFlag
          in: query
          description: Retrieve information about all participants in a given product.
          required: false
          schema:
            type: boolean
          example: false
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMortgageServicingResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/mortgages/servicing:
    put:
      summary: Set Mortgage Product Application Servicing Request.
      description: |
        <h3>ID operace: las-application-v1-setMortgageServicing</h3>
        <p>Creates or updates existing mortgage product applications servicing request.</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.SET_PERSON_SMART</li>
        <li>ADB.SET_APPL_SERV_MORT</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Detailed overview of the orchestration logic is part of the sequence diagram.</p>
        <h3>Logy v Kibaně:</h3>
        <ul>
        <li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setMortgageServicing%22'),sort:!())>PreSIT</a></li>
        <li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setMortgageServicing%22'),sort:!())>TFX1</a></li>
        <li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-setMortgageServicing%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-setMortgageServicing
      parameters:
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetMortgageServicingRequest'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetMortgageServicingResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/building/{applicationId}:
    get:
      summary: Get Building Loan Application service.
      description: |
        <h3>ID operace: las-application-v1-getBuildingLoanAppl</h3>
        <p>Get the detail of a specific building loan application. Operation returns the details of a specific loan application based on its ID. In the request the consumer selects what sets of details they want to be returned and several filtering conditions.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2/getBuildingLoanAppl</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT</li><li>ADB.GET_APPL_BSL</li><li>ADB.GET_APPL_REJ_RSN</li><li>ADB.GET_APPL_DATE</li><li>ADB.GET_PT_DSN</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.GET_PERSON</li><li>ADB.GET_PT_DET</li><li>ADB.GET_PT_COPR_DET</li><li>ADB.GET_PERSON_VERIF</li><li>ADB.GET_PERSON_INC_VERIF</li><li>ADB.GET_PT_INC</li><li>ADB.GET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT_SIGN_CNL</li><li>ADB.GET_APPL_METAD</li><li>ADB.GET_OBLGTN</li><li>ADB.GET_CNST</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getBuildingLoanAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getBuildingLoanAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getBuildingLoanAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getBuildingLoanAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of application.
          required: true
          schema:
            type: string
          example: **********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: BUSAPPLID
        - name: applVariantTpId
          in: query
          description: Application variant type IDs, list of values from LovApplVariantTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[OFR, REQ]'
        - name: personIncomeVerificationResultId
          in: query
          description: Person income verification result IDs, list of values from LovIncVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[VERIFDONE, CLOSED]'
        - name: personIncomeVerificationSrcId
          in: query
          description: Income verification source IDs, list of values from LovIncVerifSrc.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[PSD2, EMPLOYER]'
        - name: personVerificationResultId
          in: query
          description: Person verification results, list of values from LovPersonVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[APPROVED, DATADONE]'
        - name: personVerificationTpId
          in: query
          description: Person verification types, list of values from LovPersonVerifTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[TRANS, BRANCH]'
        - name: personsFlag
          in: query
          description: Retrieve persons data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: incomesFlag
          in: query
          description: Retrieve incomes data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personVerificationFlag
          in: query
          description: Retrieve person verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personIncomeVerificationFlag
          in: query
          description: Retrieve person income verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applFlag
          in: query
          description: Retrieve application data.
          required: false
          schema:
            type: boolean
            default: true
          example: true
        - name: applVariantsFlag
          in: query
          description: Retrieve application variants.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applVariantParamsFlag
          in: query
          description: Retrieve application varitant params.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applVariantsSurchrgFlag
          in: query
          description: Retrieve application variant surcharges.
          required: false
          schema:
            type: boolean
          example: false
        - name: signChannelsFlag
          in: query
          description: Retrieve sign channels.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applMetadataFlag
          in: query
          description: Retrieve application metadata.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: oblgtnFlag
          in: query
          description: Retrieve obligations.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: oblgtnActiveFlag
          in: query
          description: Retrieve active obligations.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: csaPtMetadFlag
          in: query
          description: Retrieve csa persons metadata.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: docFlag
          in: query
          description: Retrieve documents.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: hash
          in: query
          description: Hash of application.
          required: false
          schema:
            type: string
          example: 77PY43Hm
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBuildingLoanApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/loans/{applicationId}:
    get:
      summary: Get Loan Application service.
      description: |
        <h3>ID operace: las-application-v1-getLoanAppl</h3>
        <p>Get the detail of a specific loan application. Operation returns the details of a specific loan application based on its ID. In the request the consumer selects what sets of details they want to be returned and several filtering conditions.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2/getLoanAppl</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT</li><li>ADB.GET_APPL_LOAN</li><li>ADB.GET_APPL_REJ_RSN</li><li>ADB.GET_APPL_DATE</li><li>ADB.GET_PT_DSN</li><li>had-interest-change-v1-getUsedInterestChange</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.GET_PERSON</li><li>ADB.GET_PT_DET</li><li>ADB.GET_PT_COPR_DET</li><li>ADB.GET_PERSON_VERIF</li><li>ADB.GET_PERSON_INC_VERIF</li><li>ADB.GET_PT_INC</li><li>ADB.GET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT_SIGN_CNL</li><li>ADB.GET_APPL_METAD</li><li>ADB.GET_OBLGTN</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getLoanAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getLoanAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getLoanAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getLoanAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of application.
          required: true
          schema:
            type: string
          example: **********
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: BUSAPPLID
        - name: applVariantTpId
          in: query
          description: Application variant type IDs, list of values from LovApplVariantTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[OFR, REQ]'
        - name: personIncomeVerificationResultId
          in: query
          description: Person income verification result IDs, list of values from LovIncVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[VERIFDONE, CLOSED]'
        - name: personIncomeVerificationSrcId
          in: query
          description: Income verification source IDs, list of values from LovIncVerifSrc.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[PSD2, EMPLOYER]'
        - name: personVerificationResultId
          in: query
          description: Person verification results, list of values from LovPersonVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[APPROVED, DATADONE]'
        - name: personVerificationTpId
          in: query
          description: Person verification types, list of values from LovPersonVerifTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[TRANS, BRANCH]'
        - name: primaryOwnerFlag
          in: query
          description: Retrieve primary owner data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: primaryOwnerIncomeFlag
          in: query
          description: Retrieve primary owner income data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personVerificationFlag
          in: query
          description: Retrieve person verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personIncomeVerificationFlag
          in: query
          description: Retrieve person income verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applFlag
          in: query
          description: Retrieve application data.
          required: false
          schema:
            type: boolean
            default: true
          example: true
        - name: applVariantsFlag
          in: query
          description: Retrieve application variants.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applVariantParamsFlag
          in: query
          description: Retrieve application varitant params.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: signChannelsFlag
          in: query
          description: Retrieve sign channels.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applMetadataFlag
          in: query
          description: Retrieve application metadata.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: oblgtnFlag
          in: query
          description: Retrieve obligations.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: oblgtnActiveFlag
          in: query
          description: Retrieve active obligations.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: usedInterestChangeFlag
          in: query
          description: Retrieve loan application discount.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: hash
          in: query
          description: Hash of application.
          required: false
          schema:
            type: string
          example: 77PY43Hm
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoanApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/list-to-cancel:
    get:
      summary: Služba pro zrušení žádosti o půjčku.
      description: |
        <h3>ID operace: las-application-v1-getApplListToCancel</h3>
        <p>Vr&aacute;t&iacute; seznam ž&aacute;dost&iacute; k uzavřen&iacute;. Parametry lze použ&iacute;t k filtrov&aacute;n&iacute;.</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v2/getApplListToCancel</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_FOR_CANCEL</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pomoc&iacute; vstupn&iacute;ch dat služby načteme data ž&aacute;dost&iacute;, kter&eacute; chceme zru&scaron;it.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListToCancel%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListToCancel%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListToCancel%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getApplListToCancel
      parameters:
        - name: prodTps
          in: query
          description: Filter by LovBusProdTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: RCL
        - name: prodSubTps
          in: query
          description: Filter by LovBusProdSubTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: RCL_STANDARD
        - name: applTps
          in: query
          description: Filter by LovApplTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: Application type ID example
        - name: states
          in: query
          description: Filter by LovApplStat.
          required: false
          schema:
            type: array
            items:
              type: string
          example: FIN_PROVIDED
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApplicationToCancel'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/list-to-state-change:
    get:
      summary: Služba pro načtení seznamu žadostí na změnu stavu.
      description: |
        <h3>ID operace: las-application-v1-getApplListToStateChange</h3>
        
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_FOR_STATE_CHANGE_30</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListToCancel%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListToCancel%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListToCancel%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getApplListToStateChange
      parameters:
        - name: maxCount
          in: query
          description: Maximal.
          required: false
          schema:
            type: integer
            format: int32
          example: 123
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApplicationToStateChange'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/list-for-brokers:
    get:
      summary: Get Application List for Brokers service.
      description: |
        <h3>ID operace: las-application-v1-getApplListForBrokers</h3>
        <p>Returns a collection of applications and subapplications for a brokers.</p>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BY_BROKER</li>
        <li>parametrization-app/v1/s2s/parametrizations/rml</li>        
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Provolá API (viz seznam orchestrovaných API) a vrátí data na výstup.</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListForBroker%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListForBroker%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getApplListForBroker%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getApplListForBrokers
      parameters:
        - name: brokerId
          in: query
          description: Set of input identificators (brokerIds, accOfficerIds or mortgCenters)
          required: false
          schema:
            type: array
            items:
              type: string
          example: 569
        - name: brokerIdType
          in: query
          description: ID differentiator to distinguish between brokerId, accOfficerId and mortgCentre
          required: false
          schema:
            type: string
            enum:
              - BROKERID
              - ACCOFFICERID
              - MORTGCENTRE
          example: BROKERID
        - name: recFrom
          in: query
          description: Record From / Pořadí záznamu od
          required: false
          schema:
            type: integer
            format: int32
          example: 10
        - name: recTo
          in: query
          description: Record To / Pořadí záznamu do
          required: false
          schema:
            type: integer
            format: int32
          example: 20
        - name: sortCriterium
          in: query
          description: "Sort criterium. Supported SORT types: (1 - číslo žádosti, 2- hlavní žadatel, 3 - částka, 4 - účel, 5 - status, 6 - vytvořeno)"
          required: false
          schema:
            type: integer
            format: int32
          example: 1
        - name: sortOrder
          in: query
          description: Sort order. Sort mode. (0 - DESC, 1 - ASC)
          required: false
          schema:
            type: integer
            format: int32
          example: 0
        - name: busProdTpFilter
          in: query
          description: Filtr na typ produktu (LOV BUS_PROD_TP)
          required: false
          schema:
            type: array
            items:
              type: string
          example: RML
        - name: applStatFilter
          in: query
          description: Filtr na stav žádosti (LOV APPL_STAT). Aplikuje se pouze na hlavní žádosti.
          required: false
          schema:
            type: array
            items:
              type: string
          example: PRDN
        - name: applicantName
          in: query
          description: Filtr na jméno hlavního žadatele
          required: false
          schema:
            type: string
          example: Michal
        - name: applicantSurename
          in: query
          description: Filtr na příjmení hlavního žadatele
          required: false
          schema:
            type: string
          example: Lopata
        - name: busProdSubTpFilter
          in: query
          description: Filtr na podtyp produktu (LOV BUS_PROD_SUB_TP). Aplikuje se pouze na hlavní žádosti.
          required: false
          schema:
            type: array
            items:
              type: string
          example: RML_CLAS
        - name: distCnlId
          in: query
          description: Filtr na identifikaci distribučního kanálu (LOV CNL). Aplikuje se pouze na hlavní žádosti.
          required: false
          schema:
            type: array
            items:
              type: string
          example: BROKER
        - name: busApplId
          in: query
          description: Filtr na business číslo žádosti nebo podžádosti
          required: false
          schema:
            type: string
          example: 5100
        - name: applCreatedDateFrom
          in: query
          description: Filtr na datum založení žádosti OD
          required: false
          schema:
            type: string
            format: date
          example: 2023-10-10
        - name: applCreatedDateTo
          in: query
          description: Filtr na datum založení žádosti DO
          required: false
          schema:
            type: string
            format: date
          example: 2023-10-10
        - name: applExprDateFrom
          in: query
          description: Filtr na datum terminace žádosti OD
          required: false
          schema:
            type: string
            format: date
          example: 2023-10-10
        - name: applExprDateTo
          in: query
          description: Filtr na datum terminace žádosti DO
          required: false
          schema:
            type: string
            format: date
          example: 2023-10-10
        - name: appctPhoneNum
          in: query
          description: Filtr na telefon hlavního žadatele
          required: false
          schema:
            type: string
          example: "+************"
        - name: appctEmail
          in: query
          description: Filtr na emailovou adresu hlavního žadatele
          required: false
          schema:
            type: string
          example: <EMAIL>
        - name: onlyWoAccOfficerFlag
          in: query
          description: Only Without Account Officer Flag (false – všechny žádosti bez ohledu na to, zda mají/nemají přiřazeného bankéře, true – pouze žádosti bez přiřazeného bankéře)
          required: false
          schema:
            type: boolean
          example: true
        - name: onlySelfFlag
          in: query
          description: "Mód hledání: false - podle žádosti - všechny podžádosti všech žádostí, u kterých je alespoň u jedné podžádosti vlastníkem obchodu daný broker (accOfficerId nebo mortgCentre), true - podle podžádosti (garance) - všechny podžádosti u kterých je vlastníkem obchodu daný broker (accOfficerId nebo mortgCentre)"
          required: false
          schema:
            type: boolean
          example: 1
        - name: appctSiebelIdIn
          in: query
          description: Filtr na SBL_ID hlavního žadatele.
          required: false
          schema:
            type: string
          example: *********
        - name: appctRcNumIn
          in: query
          description: Filtr na rodné číslo hlavního žadatele.
          required: false
          schema:
            type: string
          example: 8012244866
        - name: appctBirthDateIn
          in: query
          description: Filtr na datum narození hlavního žadatele.
          required: false
          schema:
            type: string
            format: date
          example: 1980-12-24
        - name: appctBicIdIn
          in: query
          description: Filtr na BIC_ID hlavního žadatele.
          required: false
          schema:
            type: string
          example: 123456789
        - name: subapplDistCnlIdIn
          in: query
          description: Filtr na identifikaci distribučního kanálu (LOV CNL). Aplikuje se pouze na podžádosti.
          required: false
          schema:
            type: array
            items:
              type: string
          example: BROKER
        - name: subapplBusProdSubTpIdIn
          in: query
          description: Filtr na podtyp produktu (LOV BUS_PROD_SUB_TP). Aplikuje se pouze na podžádosti.
          required: false
          schema:
            type: array
            items:
              type: string
          example: RML_CLAS
        - name: subapplApplStatIdIn
          in: query
          description: Filtr na stav žádosti (LOV APPL_STAT). Aplikuje se pouze na podžádosti.
          required: false
          schema:
            type: array
            items:
              type: string
          example: PRDN
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApplicationForBrokersResult'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/deferred-payments/{orderNumber}/status:
    get:
      summary: Get Defferred Payment Status service.
      description: |
        <h3>ID operace: las-application-v1-getDeferredPaymentStatus</h3>
        <p>Get the current status (and optionally a variable symbol) of an application for a deferred payment (Plat&iacute;mPak) based on order number and order originator identification (FTP or partyId).</p>
        <h3>Původní BPEL služba:</h3>
        <p>approval/ApplicationService-v1/getDeferredPaymentState</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_BY_ORD_NUM</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>Pomoc&iacute; vstupn&iacute;ch dat načte stav Plat&iacute;mPak ž&aacute;dost&iacute; přes ADB api (viz.apiList).</p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getDeferredPaymentStatus%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getDeferredPaymentStatus%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getDeferredPaymentStatus%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getDeferredPaymentStatus
      parameters:
        - name: orderNumber
          in: path
          description: E-shop application/order number.
          required: true
          schema:
            type: string
          example: ORD22446688
        - name: orderOriginIdType
          in: query
          description: Specifies which kind of order originator identifier will be used.
          required: true
          schema:
            type: string
            enum:
              - FTP
              - PARTY_ID
          example: FTP
        - name: orderOriginId
          in: query
          description: Value of order originator identifier.
          required: true
          schema:
            type: string
          example: 1930301
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetDeferredPaymentStatusResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/credit-cards/{applicationId}:
    get:
      summary: Get Credit Card Application service.
      description: |
        <h3>ID operace: las-application-v1-getCreditCardAppl</h3>
        <p>Get the detail of a specific credit card application. Operation returns the details of a specific credit card application based on its ID. In the request the consumer selects what sets of details they want to be returned and several filtering conditions.</p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>

        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>ADB.GET_APPL_STAT</li><li>ADB.GET_APPL_CARD</li><li>ADB.GET_PT_DSN</li><li>ADB.GET_APPL_PT_ROLE</li><li>ADB.GET_PERSON</li><li>ADB.GET_PT_DET</li><li>ADB.GET_PT_COPR_DET</li><li>ADB.GET_PERSON_VERIF</li><li>ADB.GET_PERSON_INC_VERIF</li><li>ADB.GET_PT_INC</li><li>ADB.GET_APPL_VRNT</li><li>ADB.GET_APPL_VRNT_SIGN_CNL</li><li>ADB.GET_APPL_METAD</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p></p>
        <h3>Logy v Kibaně:</h3>
        <ul><li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getCreditCardAppl%22'),sort:!())>PreSIT</a></li><li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getCreditCardAppl%22'),sort:!())>TFX1</a></li><li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-getCreditCardAppl%22'),sort:!())>PROD</a></li></ul>
      operationId: las-application-v1-getCreditCardAppl
      parameters:
        - name: applicationId
          in: path
          description: ApplKey or busApplId of application.
          required: true
          schema:
            type: string
          example: 202212210014
        - name: applicationIdType
          in: query
          description: Application id differentiator to distinguish between applKey, busApplId and hash.
          required: true
          schema:
            type: string
            enum:
              - APPLKEY
              - BUSAPPLID
              - HASH
          example: APPLKEY
        - name: applVariantTpId
          in: query
          description: Application variant type IDs, list of values from LovApplVariantTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[OFR, REQ]'
        - name: personIncomeVerificationResultId
          in: query
          description: Person income verification result IDs, list of values from LovIncVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[TOVERIF, VERIFDONE]'
        - name: personIncomeVerificationSrcId
          in: query
          description: Income verification source IDs, list of values from LovIncVerifSrc.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[PSD, PSD2]'
        - name: personVerificationResultId
          in: query
          description: Person verification results, list of values from LovPersonVerifRslt.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[TOVERIF, DATADONE]'
        - name: personVerificationTpId
          in: query
          description: Person verificartion types, list of values from LovPersonVerifTp.
          required: false
          schema:
            type: array
            items:
              type: string
          example: '[PSD, PSD2]'
        - name: primaryOwnerFlag
          in: query
          description: Retrieve primary owner data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: primaryOwnerIncomeFlag
          in: query
          description: Retrieve primary owner income data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personVerificationFlag
          in: query
          description: Retrieve person verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: personIncomeVerificationFlag
          in: query
          description: Retrieve person income verification data.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applFlag
          in: query
          description: Retrieve application data.
          required: false
          schema:
            type: boolean
            default: true
          example: true
        - name: applVariantsFlag
          in: query
          description: Retrieve application variants.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applVariantParamsFlag
          in: query
          description: Retrieve application varitant params.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: signChannelsFlag
          in: query
          description: Retrieve sign channels.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: applMetadataFlag
          in: query
          description: Retrieve application metadata.
          required: false
          schema:
            type: boolean
            default: false
          example: false
        - name: hash
          in: query
          description: Hash of application.
          required: false
          schema:
            type: string
          example: 65PY43Hm
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Idempotency-Key'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
        - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreditCardApplResponse'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /application/v1/applications/hyc/{applicationId}/upload-realties:
    put:
      summary: Upload realties service.
      description: |
        <h3>ID operace: las-application-v1-uploadHycRealties</h3>
        <p>Through the LAS service las-application-v1-getMortgageAppl, information about collateral is obtained and then stored in the ADB via the las-application-v1-setMortgageAppl. </p>
        <h3>Původní BPEL služba:</h3>
        <p>NONE</p>
        <h3>Seznam orchestrovaných API:</h3>
        <ul>
        <li>las-application-v1-getMortgageAppl</li>
        <li>ABD.SET_PERSON</li>
        <li>las-application-v1-setMortgageAppl</li>
        </ul>
        <h3>Popis orchestrace:</h3>
        <p>TBD</p>
        <h3>Logy v Kibaně:</h3>
        <ul>
        <li><a href=https://presit-kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-uploadHycRealties%22'),sort:!())>PreSIT</a></li>
        <li><a href=https://test-kibana.rb.cz/kibana/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-uploadHycRealties%22'),sort:!())>TFX1</a></li>
        <li><a href=https://kibana.rb.cz/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(_source),filters:!(),index:'search-logs',interval:auto,query:(language:kuery,query:'application_id+%3A+%22LAS%22+and+message+%3A+%22las-application-v1-uploadHycRealties%22'),sort:!())>PROD</a></li>
        </ul>
      operationId: las-application-v1-uploadHycRealties
      parameters:
        - name: applicationId
          in: path
          description: BusApplId identifikátor hlavní žádosti o hypotéku.
          required: true
          schema:
            type: string
          example: 853599005720
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Idempotency-Key"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
        - $ref: "#/components/parameters/X-Channel-Code"
      responses:
        "200":
          description: OK
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
components:
  schemas:
    MergeMortgageRequest:
      required:
        - mergeBusApplId
        - mergeSubApplKey
      type: object
      properties:
        mergeSubApplKey:
          type: integer
          description: Instanční klíč podžádosti, která bude slučována
          format: int64
          example: *********
        mergeBusApplId:
          type: string
          description: Business identifikátor hlavní žádosti, která je nadřazená podžádosti v mergeSubApplKey a která bude po sloučení stornována
          example: '**********'
      description: Merge mortgage subapplication request.
    MergeMortgageResponse:
      required:
        -  newSubApplKey
      type: object
      properties:
        newSubApplKey:
          type: integer
          description: Instanční klíč podžádosti, která vznikla sloučením
          format: int64
          example: 704000180
        newBusApplId:
          type: string
          description: Business identifikátor podžádosti, která vznikla sloučením
    ApplyGuaranteedMortgageApplResponse:
      required:
        -  origUnboundGarApplKey
      type: object
      properties:
        origUnboundGarApplKey:
          type: integer
          description: Original Unbound GAR Application Key / Instanční klíč původní odvázané garantované nabídky.
          format: int64
          example: 704000180
        origUnboundGarNewStatId:
          type: string
          description: Original Unbound GAR New Status Identifier / Nový stav původní odvázané garantované.
          example: '**********'
    CloneMortgageResponse:
      type: object
      properties:
        newApplKey:
          type: integer
          description: New Application Key / Instanční klíč nové podžádosti.
          format: int64
          example: 704000180
        newBusApplId:
          type: string
          description: New Business Application Identifier / Nový business identifikátor žádosti.
          example: '**********'
    SetApplStatusRequest:
      required:
        - applStatID
      type: object
      properties:
        applStatID:
          type: string
          description: 'Application Status Identifier / Stav žádosti. LOVS: LovApplStat'
          example: STR
        reason:
          type: string
          description: Reason. For logging purposes, add to application status change log.
      description: Set Appl Status service request element.
    SetApplStatusResponse:
      required:
        - applKey
        - busApplId
        - hash
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key / Instanční klíč hledané žádosti.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business Application Identifier / Generované business číslo hledané žádosti.
          example: '**********'
        hash:
          type: string
          description: Hash identifier / Identifikace hledané žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
      description: Set Appl Status response element.
    Account:
      type: object
      properties:
        accPrefix:
          type: string
          description: Prefix uctu.
        accNumber:
          type: string
          description: Cislo uctu.
        bankCode:
          type: string
          description: 'Kod banky, Lov: BANK_CZ_CODE.'
          example: '5500'
        accName:
          type: string
          description: Nazev uctu klienta.
      description: Account.
    SetPersonVerificationRequest:
      type: object
      properties:
        instPtVerifKey:
          type: integer
          description: Instance Party Verification Key / Instanční klíč ověření osoby.
          format: int64
          example: 22642
        instPtIncVerifKey:
          type: integer
          description: Instance Party Income Verification Key / Instanční klíč ověření příjmu.
          format: int64
          example: 836589
        verifType:
          type: string
          description: 'Verification Type / Typ verifikace osoby, LOV: PERSON_VERIF_TP.'
          example: PSD
        verifResult:
          type: string
          description: 'Result Of Person Verification / Vysledek verifikace osoby, LOV: PERSON_VERIF_RSLT.'
          example: APPROVED
        incVerifSrc:
          type: string
          description: 'Income Verification Source Identifier / Zdroj ověření příjmu, LOV: INC_VERIF_SRC.'
          example: PSD2
        incVerifResult:
          type: string
          description: 'Income Verification Result Identifier / Výsledek ověření příjmu, LOV: INC_VERIF_RSLT.'
          example: DATADONE
        providerCode:
          type: string
          description: Provider Id / Kod banky dle ciselniku BAAPI.
        verifTime:
          type: string
          description: Time Of Verification / Čas verifikace.
          format: date-time
        advisorId:
          type: string
          description: Advisor ID / Identifikator zprostredkovatele.
        refreshAccounts:
          type: boolean
          description: Refresh Accounts Flag / true = smaze stavajici zaznamy a vlozi nove, false = prida nove zaznamy.
          example: false
        accounts:
          type: array
          description: Person Verification Request Account list / Kolekce uctu, tam kde je potreba.
          items:
            $ref: '#/components/schemas/Account'
        psdSubscriptionId:
          type: string
          description: PSD Subscription ID.
        psdUserId:
          type: string
          description: PSD User Id.
      description: Set Person Verification service request element.
    SetPersonVerificationResponse:
      type: object
      properties:
        instPtVerifKey:
          type: integer
          description: Instance Party Verification Key / Instanční klíč ověření osoby.
          format: int64
          example: 22642
        instPtIncVerifKey:
          type: string
          description: Instance Party Income Verification Key / Instanční klíč ověření příjmu.
          example: '836589'
      description: Set Person Verification service response element.
    PersonSourceId:
      required:
        - srcSysId
        - value
      type: object
      properties:
        value:
          type: string
          description: Person id from source system.
          example: '1930301'
        srcSysId:
          type: string
          description: Source system id.
          example: GFO
      description: Wrapper for person id and source system where id is from.
    SetContractSignRequest:
      type: object
      properties:
        contrNum:
          type: string
          description: Contract Number / Číslo smlouvy (napr. z Elbosu).
        contrSignDate:
          type: string
          description: Contract Sign Date / Datum podpisu smlouvy.
          format: date
        fulfillmentCnlId:
          type: string
          description: 'Fulfillment Chanel / Kanál kompletace smlouvy. LOVS: LovCnl'
          example: WEB
        contrSignPosId:
          type: string
          description: 'Contract Signed POS / Místo (pobočka) podpisu smlouvy. LOVS: LovPos'
          example: RBPHPKCB
        contrSignAdvisorId:
          type: string
          description: Contract Signed Advisor ID / identifikátor pracovníka podepisujícího smlouvu za banku.
        contrSignAdvisorName:
          type: string
          description: Contract Signed Advisor Name / Jméno pracovníka podepisujícího smlouvu za banku.
        applComplPosId:
          type: string
          description: 'Application Complete POS / Místo (pobočka) kompletace žádosti. LOVS: LovPos'
          example: RBPHPKCB
        partySrcId:
          $ref: '#/components/schemas/PersonSourceId'
        smsCode:
          type: string
          description: SMS Code / Číslo, které zadal uživatel - viz verifySmsCode.
        objectId:
          type: string
          description: Object ID / DocDmsUrl L714, ktere MB obdrzela v ramci getDocumentList.
        signingTime:
          type: string
          description: Time Of Sign / Cas podpisu smlouvy v MB.
          format: date-time
      description: Set Contract Sign service request element.
    SetClientActionRequest:
      required:
        - eventType
      type: object
      properties:
        eventType:
          type: string
          description: 'LOV: APPL_EVE_TP - even per user/client action on gui'
        description:
          type: string
          description: Description.
        docTp:
          type: string
          description: 'Document Type / Typ dokumentu. LOV: DOC_TP'
          example: L707
        ip:
          type: string
          description: IP Address / Ip adresa uzivatele.
          example: *******
      description: Set Client Action service request element.
    SetObligationInterestRateServiceRequest:
      required:
        - intrsRxDscnt
      type: object
      properties:
        intrsRxDscnt:
          type: number
          description: Interest Rate Discount / Výše slevy. Pokud je 0, tak se výsledná sazba nastaví na originální hodnotu. Kladná hodnota = navýšení sazby, záporná hodnota = ponížení sazby.
        minIntrsRx:
          type: number
          description: Minimum Interest Rate / Minimální výše slevy.
      description: Set Obligation Interest Rate service request element.
    SetVariantParamsChangeServiceRequest:
      required:
        - intrsRxDscnt
      type: object
      properties:
        intrsRxDscnt:
          type: number
          description: Interest Rate Discount / Výše slevy. Rozdílová hodnota sazby. Pokud je 0, tak se výsledná sazba nastaví na originální hodnotu. Kladná hodnota = navýšení sazby, záporná hodnota = ponížení sazby.
        minIntrsRx:
          type: number
          description: Minimum Interest Rate / Minimální výše slevy. Minimální sazba, na kterou lze úrokovou sazbu snížit.
      description: Set Variant Params Change service request element.
    CtAddress:
      type: object
      properties:
        streetName:
          type: string
          description: Street Name.
          example: Hvězdova
        streetNum:
          type: string
          description: Street Number.
          example: '1716'
        cityName:
          type: string
          description: City Name.
          example: Praha 4
        detail:
          type: string
          description: Address Detail / Doplňkový údaj ke kontaktní adrese.
          example: centrála
        zip:
          type: string
          description: ZIP / Adresa - PSČ.
          example: 140 78
        landRegnNum:
          type: string
          description: Address Land Registration Number / Adresa - číslo orientační.
          example: 2b
        cntryId:
          type: string
          description: Country, LovCntry.
          example: CZ
        addrInstAddrKey:
          type: integer
          description: Instance Address Key.
          format: int64
          example: 1490164
        addrSince:
          type: string
          description: Date of valid Instance address.
          format: date
      description: CtAddress. Address data.
    CtApplIdKeyIdChoice:
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key Identifier.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business Application Identifier (external).
          example: '**********'
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
      description: CtApplIdKeyIdChoice. Application identifiers.
    CtApplMetadataEntry:
      required:
        - name
      type: object
      properties:
        name:
          type: string
          description: Application metadata entry name.
        value:
          type: string
          description: Application metadata entry value.
      description: CtApplMetadataEntry. Application metadata. Application metadata entry complex type.
    CtApplicationVariant:
      type: object
      properties:
        applVariantKey:
          type: integer
          description: Variant Key.
          format: int64
          example: 2214874003641
        applVariantTpId:
          type: string
          description: Application variant type ID, LovApplVariantTp.
          example: REQ
        applVariantSignCnlFlag:
          type: boolean
          description: Indicates whether Application Variant Sign Channel is changed or not.
          example: false
        busProdTpId:
          type: string
          description: Business product type ID, LovBusProdTp.
          example: RCL
        busProdSubTpId:
          type: string
          description: Business product sub-type ID, LovBusProdSubTp.
          example: RCL_STANDARD
        finaAmt:
          type: number
          description: Total amount of loan or limit.
          example: 100000
        instlAmt:
          type: number
          description: Installment amount.
          example: 2215
        instlCnt:
          type: integer
          description: Total number of installments.
          format: int64
          example: 60
        maturityDate:
          type: string
          description: Last instalment date / datum poslední splátky
          format: date
          example: '2028-03-30'
        intrsRx:
          type: number
          description: Interest rate contracted.
          example: 0.07
        rpsn:
          type: number
          description: Anual Percentage Rate of charge (RPSN) - contracted / RPSN - Roční procentní sazba nákladů.
          example: 0.0723
        camCode:
          type: string
          description: Campaign Code.
        minAvlblAmt:
          type: number
          description: Minimal available limit.
        minInstlAmt:
          type: number
          description: Minimal installment amount.
        minInstlCnt:
          type: integer
          description: Minimal number of installments.
          format: int64
        maxAvlblAmt:
          type: number
          description: Maximal available limit.
        maxInstlAmt:
          type: number
          description: Maximal installment amount.
        maxInstlCnt:
          type: integer
          description: Maximal number of installments.
          format: int64
        upsellAmt:
          type: number
          description: Up sell amount/ Navýšení.
          example: 0
        maxUpsellAmt:
          type: number
          description: Maximum approved amount.
        ccyId:
          type: string
          description: Currency ID references LOV CCY/ ID měny, LovCcy.
          example: CZK
        totRpmtAmt:
          type: number
          description: Total Saved Amount / Celová častka splatná spotřeb.
          example: 132900
        refiSavedAmt:
          type: number
          description: Total Repayment Amount/ Částka kterou klient ušetřil.
        accNumPrefix:
          type: string
          description: Account number prefix.
        accNum:
          type: string
          description: Account number.
          example: '**********'
        accBankCode:
          type: string
          description: Account bank code. Bank code in CZ, LovBankCzCode.
          example: '5500'
        actSumInstlAmt:
          type: number
          description: Aktuální výše splátek všech konsolidovaných úvěrů klienta. Výpočet Tauru.
        consLoanInstlAmt:
          type: number
          description: Nová výše splátky konsolidovaného úvěru. Výpočet Tauru.
        dayIntrsAmt:
          type: number
          description: Denní úrok.
        intrsDscntFlag:
          type: boolean
          description: Interest rate discount allowed.
          example: false
        equaOblgtnAmt:
          type: number
          description: Suma částek refinancovaných úvěrů v rámci Equa.
        nonEquaOblgtnAmt:
          type: number
          description: Suma částek refinancovaných úvěrů mimo Equa.
        repreDrawDate:
          type: string
          description: 'Representative Drawing Date / Reprezentativní příklad: datum čerpání (první den nadcházejícího kalendářního měsíce měsíce).'
          format: date
          example: '2022-12-16'
        repreMaturityDate:
          type: string
          description: 'Representative Maturity Date / Reprezentativní příklad: datum splatnosti (datum čerpání + 3 měsíce (1. kalendářní den)).'
          format: date
          example: '2023-04-01'
        repreInt:
          type: number
          description: Reprezentativní úrok (příklad).
        repreInt3M:
          type: number
          description: Reprezentativní 3 měsíční úrok (příklad).
        costs3M:
          type: number
          description: Reprezentativní 3 měsíční náklady (příklad).
        delFlag:
          type: boolean
          description: Delete Flag.
          example: true
        declaredPurpose:
          type: string
          description: Secondary loan purpose, LovLoanPurpScnd.
          example: OTHER
        maxExtOblgtnAmt:
          type: number
          description: Maximum approved external obligation amount.
        minIrForFinalization:
          type: number
          description: Minimum Interest Rate For Finalization.
        origScOfrIntrsRx:
          type: number
          description: Scoring Offered Interest Rate Original / Odvozená sazba z SPSS.
        payCpcyForFinal:
          type: number
          description: Payment Capacity For Finalization / Hodnota pro výpočet platební kapacity klienta
        maxAvlblTopup:
          type: number
          description: Max Available Top UP / Maximální dostupné navýšení
        prodIntrsCodeId:
          type: string
          description: Product interest code identifier, LovProdIntrsCode
      description: Application variants. Application variant complex type.
    CtApplVariant:
      allOf:
        - $ref: '#/components/schemas/CtApplicationVariant'
        - type: object
      properties:
        loanPurpId:
          type: string
          description: Loan Purpose ID / Účel úvěru, LovLoanPurp.
          example: GOODS
      description: CtApplVariant. Application variants. Application variant complex type.
    CtIdCard:
      type: object
      properties:
        idCardTpId:
          type: string
          description: ID card type, LovIdCardTp.
          example: 6
        idCardPurpId:
          type: string
          description: ID card purpose, LovIdCardPurp.
          example: 1STID
        idCardAppResultId:
          type: string
          description: ID card approval result, LovIdCardApprRslt.
          example: MISSING
        cardIssuerCntryId:
          type: string
          description: Card Issuer Country, LovCntry.
          example: CZ
        issueDate:
          type: string
          description: Card Issue Date.
          format: date
          example: '2022-12-16'
        issuer:
          type: string
          description: Card Issuer / Vydavatel dokladu.
          example: MÚ Praha 4
        exprDate:
          type: string
          description: Card Expiration Date.
          format: date
          example: '2024-12-16'
        cardId:
          type: string
          description: Card Identifier.
          example: '212238736'
        cardValidityLimitedFlag:
          type: boolean
          description: Card Validity Limited Flag / Doklad s omezenou platností, 1 - omezená platnost.
          example: true
        investigationId:
          type: string
          description: Card Investigation Identifier.
          example: XYZ987
        chngChnlRsn:
          type: string
          description: Card Change Channel Reason.
          example: Married
        unapprRsn:
          type: string
          description: Card Unapprove Reason.
          example: Debt
      description: CtIdCard. ID cards.
    CtPerson:
      type: object
      properties:
        siebelId:
          type: string
          description: Siebel identifier.
          example: '********'
        genderId:
          type: string
          description: Gender, LovGender.
          example: M
        eduStatId:
          type: string
          description: Education status, LovEduStat.
          example: G
        housingStatId:
          type: string
          description: Housing status, LovHousingStat.
          example: SHARE
        citizenship:
          type: string
          description: Country, LovCntry.
          example: CZ
        marStatId:
          type: string
          description: Marital status, LovMarStat.
          example: S
        rcNum:
          type: string
          description: Birth number. Social insurance number of a person.
          example: 801130/8943
        salutId:
          type: string
          description: Salutation for personalized communication with a client. LovSalut.
          example: MR
        titleBefore:
          type: string
          description: Title before name.
          example: Ing.
        firstName:
          type: string
          description: First name of a person.
          example: Petr
        familyName:
          type: string
          description: Last name of a person.
          example: Novák
        birthPlace:
          type: string
          description: Birth place of a person.
          example: Kyjov
        birthName:
          type: string
          description: Birth name of a person.
          example: Novák
        birthDate:
          type: string
          description: "Birth day of a person."
          format: date
          example: '2022-12-16'
        birthCntryId:
          type: string
          description: Birth country, LovCntry.
          example: CZ
        taxDomicile:
          type: string
          description: Tax domicile country, Tax Country Identifier/Země danového rezidenství. LovCntry
          example: AT
        tin:
          type: string
          description: Taxpayer Identification Number/Zahraniční DIC.
          example: '15349308'
        pep:
          type: boolean
          description: Politically Exposed Person.
          example: false
        mortExpSum:
          type: number
          description: Mortgage Expense Sum / Splátky hypotek.
          example: 10000
        householdExpSum:
          type: number
          description: Household Expense Sum / Měsíční výdaje na bydlení a domácnost.
          example: 15000
        othExpSum:
          type: number
          description: Other Expense Sum / Ostatní výdaje.
          example: 5000
        mntdChildCnt:
          type: integer
          format: int64
          description: Number of maintained children / Počet vyživovaných dětí
          example: 1
        crntAddrValidDate:
          type: string
          description: Current Address since / Soucasna adresa od.
          format: date
          example: '1989-12-16'
        email:
          type: string
          description: Email Identifier.
          example: <EMAIL>
        phoneNum:
          type: string
          description: Phone Number.
          example: '420412440000'
        permAddr:
          $ref: '#/components/schemas/CtAddress'
        postalAddr:
          $ref: '#/components/schemas/CtAddress'
        idCards:
          type: array
          description: List of ID cards.
          items:
            $ref: '#/components/schemas/CtIdCard'
      description: CtPerson. Personal data.
    CtPersonRsts:
      allOf:
        - $ref: '#/components/schemas/CtPerson'
        - type: object
          properties:
            adbInstPtKey:
              type: integer
              description: ADB instance party key.
              format: int64
              example: 202212210014
            creatorPersonId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
              example: 1
            applPtRoleTpId:
              type: string
              description: Application Party Role Type Identifier / Typ role osoby k žádosti (LOV APPL_PT_ROLE_TP)
            rstsPartyId:
              type: integer
              description: Rsts party identifier - BIC
              format: int64
              example: 123456789
            rstsPseudoBirthCode:
              type: string
              description: Rsts foreign id
              example: 123456789
            residencyTpId:
              type: string
              description: druh pobytu v ČR
            residencyFrom:
              type: string
              format: date
              description: druh pobytu od
              example: 2022-12-16
            residencyTo:
              type: string
              format: date
              description: druh pobytu do
              example: 2024-12-16
            numHouseholdPerson:
              type: string
              description: počet osob v domácnosti
            numSupportedChildren:
              type: string
              description: počet vyživovaných dětí
            incomes:
              type: array
              description: List of incomes
              items:
                $ref: '#/components/schemas/CtPtIncomeRsts'
            applDataSeg:
              type: array
              description: Application Data Segments (Dle jakého segmentu bude/byl proveden sběr dat pro danou žádost).
              items:
                type: string
    CtPersonRstsGet:
      allOf:
        - $ref: '#/components/schemas/CtPersonGet'
        - type: object
          properties:
            applPtRoleTpId:
              type: string
              description: Application Party Role Type Identifier / Typ role osoby k žádosti (LOV APPL_PT_ROLE_TP)
            rstsPartyId:
              type: string
              description: Rsts party identifier - BIC
              example: 123456789
            siebelId:
              type: string
              description: Siebel identifier.
              example: "********"
            rstsPseudoBirthCode:
              type: string
              description: Rsts foreign id
              example: 123456789
            residencyTpId:
              type: string
              description: druh pobytu v ČR
            residencyFrom:
              type: string
              format: date
              description: druh pobytu od
              example: 2022-12-16
            residencyTo:
              type: string
              format: date
              description: druh pobytu do
              example: 2024-12-16
            numHouseholdPerson:
              type: number
              description: počet osob v domácnosti
            numSupportedChildren:
              type: number
              description: počet vyživovaných dětí
            idCardsReq:
              type: boolean
              description: požadavek na upload dokumentů
            segments:
              type: array
              description: Segment list.
              items:
                type: string
                description: Segment list.
            metadata:
              type: array
              description: CtPtMetad list. Person csa metadata.
              items:
                $ref: '#/components/schemas/CtCsaPtMetad'
    CtCsaPtMetad:
      type: object
      properties:
        name:
          type: string
          description: variable name.
        numValue:
          type: number
          description: variable value number.
        strValue:
          type: string
          description: variable value string.
        dateValue:
          type: string
          description: variable value date.
          format: date
    CtPtIncome:
      type: object
      properties:
        instPtIncKey:
          type: integer
          description: ADB Instance party key.
          format: int64
          example: *********
        occCatgId:
          type: string
          description: Occupation Category Identifier/ Druh povolání/živnosti, LovOccCatg.
          example: W
        emptTpId:
          type: string
          description: Employment Type Identifier/ typ zaměstnání - na dobu určitou/neurčitou, LovEmptTp.
          example: PRMNT
        incTpId:
          type: string
          description: Income Type Identifier / Zdroj příjmu, LovIncTp.
          example: EMPLT
        inc:
          type: number
          description: Income Amount / Výše příjmů.
          example: 33000
        icoNum:
          type: string
          description: ICO of a person or a Employer.
          example: '********'
        dicNum:
          type: string
          description: DIC of a person or a Employer.
          example: CZ********
        busName:
          type: string
          description: Business name of Employer / Zaměstnavatel.
          example: Raiffeisenbank, a.s.
        empSince:
          type: string
          description: Party is employee since this date / Zaměstnán od.
          format: date
          example: '1992-12-16'
        empContrEndDate:
          type: string
          description: Date when employer contract finished / Datum ukončení pracovní činnosti.
          format: date
          example: '2022-12-16'
        dismissalFlag:
          type: boolean
          description: Indicate if the employer contract will be ended / zaměstnání/smlouva bude ukončena ke dni.
          example: false
        riskBusBranchFlag:
          type: boolean
          description: Risk Business Branch Flag / Rizikový obor podnikání.
          example: false
      description: CtPtIncome. Party incomes.
    CtSetOverdraftAppl:
      type: object
      properties:
        applTpId:
          type: string
          description: applTpId
        applStatId:
          type: string
          description: applStatId
        applDate:
          type: string
          description: applDate
          format: date-time
        posId:
          type: string
          description: posId
        firstTouchPoint:
          type: string
          description: firstTouchPoint
        applReason:
          type: string
          description: applReason
        hash:
          type: string
          description: hash
        preApprOfrId:
          type: string
          description: preApprOfrId
        opportunityId:
          type: string
          description: opportunityId
        ccyId:
          type: string
          description: ccyId
        distCnlId:
          type: string
          description: distCnlId
        advisorId:
          type: string
          description: advisorId
        fulfillmentCnlId:
          type: string
          description: fulfillmentCnlId
        wrkPlaceId:
          type: string
          description: wrkPlaceId
        busProdSubTp:
          type: string
          description: busProdSubTp
        browserInfo:
          type: string
          description: browserInfo
        telcoQueryAllowedFlag:
          type: boolean
          description: telcoQueryAllowedFlag
        authMobSearchFlag:
          type: boolean
          description: Authorization phone search flag
        firstTouchPointOwnr:
          type: string
          description: firstTouchPointOwnr
        promoCode:
          type: string
          description: promoCode
        accNumPrefix:
          type: string
          description: accNumPrefix
        accNum:
          type: string
          description: accNum
      description: Overdraft application data. Overdraft application complex type.
    CtSignChannel:
      type: object
      properties:
        applVariantSignCnlKey:
          type: integer
          description: Application variant sign channel key.
          format: int64
          example: ************
        signCnlId:
          type: string
          description: Application variant sign channel ID / Místo/kanál podpisu návrhu žádosti. Signing channel, LovCnl.
          example: WEB
        applVariantSignCnlDelFlag:
          type: boolean
          description: Application variant channel sign delete flag / Priznak odmazani zaznamu.
          example: false
        signPosId:
          type: string
          description: Signing point of sales ID, LovPos.
          example: RBPHPKCB
      description: CtSignChannel. Sign channel complex type.
    CtSignChannels:
      required:
        - signChannels
      type: object
      properties:
        applVariantTpId:
          type: string
          description: Application variant type, LovApplVariantTp.
          example: REQ
        applVariantKey:
          type: integer
          description: Application variant key.
          format: int64
          example: *************
        signChannels:
          type: array
          description: CtSignChannel list. Sign channel complex type.
          items:
            $ref: '#/components/schemas/CtSignChannel'
      description: CtSignChannels. Set sign channels complex type.
    CtBuildingVariantParameter:
      allOf:
        - $ref: '#/components/schemas/CtVariantParameter'
        - type: object
          properties:
            fixPeriod:
              type: string
              description: 'fix period - LV_RX_FIX_PER_TP'
    CtBuildingVariantParameterGet:
      allOf:
        - $ref: '#/components/schemas/CtVariantParameterGet'
        - type: object
          properties:
            fixPeriod:
              type: string
              description: 'fix period - LV_RX_FIX_PER_TP'
    CtVariantParameter:
      type: object
      properties:
        applVariantParKey:
          type: integer
          description: Application Variant Parameter Key.
          format: int64
          example: 12504291000821
        prodParDate:
          type: string
          description: Date / Vztažné datum.
          format: date
          example: '2022-12-16'
        priceVariantId:
          type: string
          description: Price Variant ID / ID cenové varianty.
          example: '566'
        purpId:
          type: string
          description: Purpose ID  / Účel úvěru.
          example: '80'
        priceVariantPriority:
          type: integer
          description: Price Variant Priority / Priorita.
          format: int64
          example: 36
        regIntrsRxId:
          type: string
          description: Regular Interest ID / Id běžné úrokové sazby.
          example: '3271'
        regIntrsRxBaseIndex:
          type: number
          description: Regular Interest Base ID / Index báze běžné úrokové sazby.
          example: 0
        penaltyIntrsRxBaseIndex:
          type: number
          description: Penalty Interest Base ID / Index báze sankční úrokové sazby.
          example: 2026
        regIntrsRx:
          type: number
          description: Regular Interest Base / Hodnota báze běžné úrokové sazby.
          example: 0
        penaltyIntrsRx:
          type: number
          description: Penalty Interest Base / Hodnota báze sankční úrokové sazby.
          example: 10
        regIntrsRxDvg:
          type: number
          description: Regular Interest Rate Value / Odchylka běžné úrokové sazby.
          example: 11.8
        origRegInstrsRxDvg:
          type: number
          description: Original Interest Rate Value / Odchylka běžné úrokové sazby - původní hodnota.
          example: 11.8
        intrsRxDiff:
          type: number
          description: Difference Rate / Diferenční sazba.
          example: 0
        minRegIntrsRx:
          type: number
          description: Min Regular Interest Rate / Minimální sazba běžného úroku.
          example: 0
        penaltyIntrsRxDvg:
          type: number
          description: Penalty Interest Rate Value / Odchylka sankční úrokové sazby.
          example: 0
        minTerm:
          type: integer
          description: Min Duration / Minimální délka.
          format: int64
          example: 55
        maxTerm:
          type: integer
          description: Max Duration / Maximální délka.
          format: int64
          example: 70
        minAmt:
          type: number
          description: Min Value / Min výše.
          example: 99000
        maxAmt:
          type: number
          description: Max Value / Max. výše.
          example: 200000
        elbosParPtStat:
          type: string
          description: Min Statute / Statut osoby od.
        elbosParRiskClassSince:
          type: string
          description: Min Risk Class / Třída risk oo.
        elbosParRiskClassTo:
          type: string
          description: Max Risk Class / Třída risk do.
        payInsurVariantId:
          type: string
          description: Insurance Variant ID / Varianta pojištění. Insurance type, LovInsurTp.
          example: '1'
        payInsurVariantAmt:
          type: number
          description: Insurance Amount / Výše pojištění.
          example: 500
        loanAccFeeAmt:
          type: number
          description: Fee Administration Account / Poplatek za vedení úvěrového účtu.
          example: 20
        provideFeeAmt:
          type: number
          description: Fee Provide Credit Account / Poplatek za poskytnutí.
          example: 100
        electronicStmFeeAmt:
          type: number
          description: Fee Electronic Statement / Poplatek za výpis elektronicky.
          example: 0
        paperStmFeeAmt:
          type: number
          description: Fee Paper Statement / Poplatek za výpis papírově.
          example: 50
        extordInstlFeeAmt:
          type: number
          description: Fee Extra Repayment / Poplatek za mimořádnou splátku.
          example: 1000
        minRefundRx:
          type: number
          description: Min Refund Rate / minimalni splatka.
          example: 333
        maxRefundRx:
          type: number
          description: Max Refund Rate / maximalni splatka.
          example: 33000
        camId:
          type: string
          description: Campaign Identifier / ID kampane.
        camName:
          type: string
          description: Campaign Name / Nazev kampane.
        payInsurVariantDescr:
          type: string
          description: Pay Insur Variant Description.
        promoCode:
          type: string
          description: Promo code.
        minTermRefund:
          type: integer
          description: Minimal Term Refund.
          format: int32
        maxTermRefund:
          type: integer
          description: Maximal Term Refund.
          format: int32
        minAmtRefund:
          type: number
          description: Minimal Amount Refund.
        maxAmtRefund:
          type: number
          description: Maximal Amount Refund.
        minUpsellAmt:
          type: number
          description: Minimal Upsell Amount.
        maxUpsellAmt:
          type: number
          description: Maximal Upsell Amount.
        applVariantParDelFlag:
          type: boolean
          description: Delete Flag.
          example: false
      description: CtVariantParameter. Application variant parameters complex type.
    CtVariantParameters:
      required:
        - variantParameters
      type: object
      properties:
        applVariantTpId:
          type: string
          description: Application variant type, LovApplVariantTp.
          example: REQ
        applVariantKey:
          type: integer
          description: Application variant key.
          format: int64
          example: *************
        variantParameters:
          type: array
          description: CtVariantParameter list. Application variant parameters complex type.
          items:
            $ref: '#/components/schemas/CtVariantParameter'
      description: CtVariantParameters. Application variant parameters complex type.
    SetOverdraftApplRequest:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyIdChoice'
        primaryOwner:
          $ref: '#/components/schemas/CtPerson'
        waitForUnification:
          type: boolean
          description: Wait for unification flag.
          example: false
          default: false
        primaryOwnerIncome:
          type: array
          description: primaryOwnerIncome
          items:
            $ref: '#/components/schemas/CtPtIncome'
        overdraftAppl:
          $ref: '#/components/schemas/CtSetOverdraftAppl'
        applVariants:
          type: array
          description: applVariants
          items:
            $ref: '#/components/schemas/CtApplVariant'
        applVariantParams:
          $ref: '#/components/schemas/CtVariantParameters'
        signChannels:
          $ref: '#/components/schemas/CtSignChannels'
        applMetadata:
          type: array
          description: applMetadata
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
      description: Set Overdraft Appl service request.
    SetOverdraftApplResponse:
      required:
        - applKey
        - busApplId
      type: object
      properties:
        applKey:
          type: integer
          description: ApplKey of created/updated application.
          format: int64
          example: *********
        busApplId:
          type: string
          description: BusApplId of created/updated application.
          example: '**********'
      description: Set Overdraft Appl service response.
    CtApplIdKeyId:
      required:
        - applKey
        - busApplId
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key Identifier.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business Application Identifier.
          example: "**********"
      description: CtApplIdKeyId. Application identifiers.
    CtMortAppl:
      type: object
      description: Mortgage (sub)application data.
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        applTpId:
          type: string
          description: "Application type ID, LovApplTp."
          example: NEW_PROD
        applStatId:
          type: string
          description: "Application state ID, LovApplStat."
          example: OO_INIT
        applDate:
          type: string
          description: Application Date. Client signature date of application.
          format: date-time
        posId:
          type: string
          description: "Point of sales ID, LovPos."
          example: RBPHPKCB
        firstTouchPoint:
          type: string
          description: First Touch Point.
          example: "18000000"
        applReason:
          type: string
          description: Application Reason / Důvod žádosti.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        preApprOfrId:
          type: string
          description: Jednoznačný identifikátor nabídky určený SPSS.
        opportunityId:
          type: string
          description: Identifikátor příležitosti ze Siebelu.
        ccyId:
          type: string
          description: "Currency, LovCcy."
          example: CZK
        distCnlId:
          type: string
          description: "Distribution chanel, LovCnl."
          example: BROKER
        advisorId:
          type: string
          description: Advisor Identifier / Identifikace poradce.
        fulfillmentCnlId:
          type: string
          description: "Fulfillment chanel, LovCnl."
          example: WEB
        wrkPlaceId:
          type: string
          description: Work place ID.
        busProdSubTp:
          type: string
          description: "Business product sub-type, LovBusProdSubTp. Application product\
            \ type taken from one of variants (logic in ADB). Relevant for getApplication\
            \ only, setApplication ignores this element."
          example: REFO
        browserInfo:
          type: string
          description: Browser Info.
        telcoQueryAllowedFlag:
          type: boolean
          description: Povolen dotaz do Telco Score.
        authMobSearchFlag:
          type: boolean
          description: "Authorization Phone Search Flag / Plní se po ověření telefonu\
            \ v DB - pokud se vrátí osoba, plní se true, jinak false."
        firstTouchPointOwnr:
          type: string
          description: First touch point owner / zadavatel žádosti.
        promoCode:
          type: string
          description: Promo code / Kód promo akce.
        applDsnKey:
          type: string
          description: Current Application Decision Key / Instanční klíč aktualniho
            rozhodnuti k zadosti.
        applComplPosId:
          type: string
          description: "Application completion Point of sales ID / POS completace\
            \ žádosti, LovPos."
          example: RBPHOLB
        contrNum:
          type: string
          description: CC internal contract number. Serves for identifiying relship
            between CC end other systems. / Číslo smlouvy.
        contrSignDate:
          type: string
          description: Signature Date - Date when the LAST applicant signed the contract
            / datum podpisu smlouvy posledním žadatelem.
          format: date
          example: 2022-12-16
        contrSignPosId:
          type: string
          description: "Contract signing Point of sales ID, LovPos."
          example: RBPHPKCB
        contrSignAdvisorId:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        contrSignAdvisorName:
          type: string
          description: "\tContract Sign Advisor Name/ Jméno poradce."
        lastChangeAdvisorId:
          type: string
          description: "Last Change Advisor Identifier / Poslední, kdo pracoval se\
            \ žádostí."
        rejectRsn:
          type: string
          description: External Reject Reason Type Descriptor.
        rejectRsnTp:
          type: string
          description: Rejection reason type.
        fiOperCode:
          type: string
          description: FI Operation Code.
        registryResult:
          type: string
          description: "Risk band, LovRiskBand."
        finalRiskClass:
          type: string
          description: "Contract Sign Advisor Identifier/ ID poradce."
        busProdTp:
          type: string
          description: Business Product Type ID / Typ schvalovaného produktu (LOV BUS_PROD_TP)
        applDateTo:
          type: string
          format: date-time
          description: Application Date To / Datum expirace žádosti (vázáno na stav)
        prntApplKey:
          type: integer
          format: int64
          description: Parent Application Key / Klíč nadřízené (hlavní žádosti)
          example: 2214874003641
        prntApplBusApplId:
          type: string
          description: Parent Application Business Application Identifier / Business identifikace nadřízené (hlavní) žádosti
        appctAgeRangeId:
          type: string
          description: Applicant Age Range Identifier / Věkové pásmo hlavního žadatele (LOV AGE_RANGE)
        coappAgeRangeId:
          type: string
          description: Co-Applicant Age Range Identifier / Věkové pásmo spolužadatele (LOV AGE_RANGE)
        firstTouchPointBnkr:
          type: string
          description: First Touch Point Banker / FTP identifikace hypotečního bankéře
        mortCentrePosId:
          type: string
          description: Mortgage Center POS Identifier / POS hypotečního centra (LOV POS)
        extnApplId:
          type: string
          description: External Application Identifier / Číslo žádosti v HypoClientovi
        complCnlId:
          type: string
          description: Channel to complete mortgage application/ Kanál pro dokončení hypoteční žádosti (LOV CNL)
        appctRole:
          type: string
          description: Applicant Role / Role přihlášeného žadatele/zadavatele
        esgCatgAutoFlag:
          type: boolean
          description: "ESG Category auto flag / Možnost kategorie ESG (Aut.)"
          example: false
        esgCatgFlag:
          type: boolean
          description: "ESG Category flag / Splňuje kategorii ESG"
          example: false
        totAvlblPlgAmtHyc:
          type: number
          description: "Total available pledge value calc in HYC / Celková neobsazená zástavní hodnota z HYC"
          example: 123456789
        totPlgAmtHyc:
          type: number
          description: "Total pledge value calc in HYC / Celková zástavní hodnota z HYC"
          example: 123456789
        apprSysId:
          type: string
          description: approval system id
    CtHouseholdSet:
      description: Household details.
      allOf:
        - $ref: '#/components/schemas/CtHousehold'
        - type: object
          properties:
            persons:
              type: array
              description: Pole identifikátorů členů domácnosti
              items:
                $ref: '#/components/schemas/CtSetMortApplPersKeys'
            creatorHousholdId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové domácnosti. Slouží pro spárování domácnosti v požadavku a odpovědi.
              example: 1
            delFlag:
              type: boolean
              description: Delete Flag.
              example: true
    CtMortApplVariant:
      allOf:
        - $ref: '#/components/schemas/CtApplicationVariant'
        - type: object
          properties:
            calcltrTpId:
              type: string
              description: Calculator Type ID identification / Typ kalkulačky (LOV CALCLTR_TP)
            intrsRxOrig:
              type: number
              description: Interest Rate Original / Minimální roční úroková sazba (vyhlašovaná, bez slev/přirážek)
            minUpsellAmt:
              type: number
              description: Minimum Upsell amount/ Minimální navýšení úvěru
            rltPrice:
              type: number
              description: Realty Price / Cena nemovitosti
            minRltPrice:
              type: number
              description: Min Realty Price / Minimální cena nemovitosti
            oblgtnAmt:
              type: number
              description: Obligation Amount / Výše refinancovaného závazku
            maxOblgtnAmt:
              type: number
              description: Max Obligation Amount / Maximální výše refinancovaného závazku
            maturityExt:
              type: number
              description: Maturity Extension / Prodloužení splatnosti refinancované hypotéky (Refi) v měsících
            dispoIncStrsAppcts:
              type: number
              description: Disposable Income Stressed of Applicants / Stresované disponibilní příjmy žadatelů
            appctsCnt:
              type: integer
              description: Applicants Count / Počet žadatelů na žádosti celkem
            ltv:
              type: number
              description: LTV / Aktuální LTV vypočtené ze zadané ceny nemovitosti a výše úvěru
            minLtv:
              type: number
              description: Minimum LTV / Minimální LTV určené podle příjmu a věku žadatele
            maxLtv:
              type: number
              description: Maximum LTV / Maximální LTV určené podle příjmu a věku žadatele
            intrsRxTpId:
              type: string
              description: Interest Rate Type Identifier / Typ úrokové sazby (LOV RX_TP)
            intrsRxFixPerTpId:
              type: string
              description: Interest Rate Fixation Period Type Identifier / Typ fixace úrokové sazby (LOV RX_FIX_PER_TP)
            intnCaFlag:
              type: boolean
              description: Internal Current Account Flag / Příznak, zda má hlavní žadatel otevřený běžný účet u RBCZ
            respMortFlag:
              type: boolean
              description: Responsible Mortgage Flag / Odpovědná hypotéka
              example: false
            instlDay:
              type: integer
              description: Day of month when repayment is due / Den, kdy má být splátka na účtu bankovního domu
              example: 13
            maxAvlblAmtWoLtv:
              type: number
              description: Max available limit without LTV restriction / Maximální výše úvěru bez omezení na LTV
            ddnPer:
              type: integer
              description: Drawdown period in months / Doba čerpání v měsících
              example: 24
            applVariantFees:
              type: array
              description: Seznam poplatků k variantě.
              items:
                $ref: '#/components/schemas/CtMortApplVariantFee'
            applVariantInsurances:
              type: array
              description: Seznam pojištění k variantě.
              items:
                $ref: '#/components/schemas/CtApplVariantIns'
            applVariantSurcharges:
              type: array
              description: Seznam slev a přirážek k variantě.
              items:
                $ref: '#/components/schemas/CtMortApplVariantSur'
            applVariantPurposes:
              type: array
              description: Seznam účelů uvěru k variantě.
              items:
                $ref: '#/components/schemas/CtApplVariantPurposes'
    CtApplVariantPurposes:
      type: object
      description: Variant fee details.
      properties:
        purpOrigLoanPurpId:
          type: string
          description: "Purpose Original Loan Purpose ID refereneces LOV LOAN_PURP/ Originální účel úvěru"
        purpRefinedLoanPurpId:
          type: string
          description: "Purpose Refined Loan Purpose ID refereneces LOV LOAN_PURP/ Zpřesněný účel úvěru"
        purpAmt:
          type: number
          description: "Purpose Amount / Účelová částka"
          example: 1000
    CtBuildingApplVariantPurpose:
      type: object
      description: Variant fee details.
      properties:
        instPurpKey:
          type: integer
          format: int64
          description: Klíč účelu
          example: 2214874003
        purpId:
          type: string
          description: "Purpose Original Loan Purpose ID refereneces LOV LOAN_PURP/ Originální účel úvěru"
        instOblgtnKey:
          type: integer
          format: int64
          description: Instanční klíč závazku (u REFI)
        purpCatgId:
          type: string
          description: "kategorie účelu"
        purpAmt:
          type: number
          description: "Purpose Amount / Účelová částka"
          example: 1000
        photovoltaics:
          type: boolean
          description: využití půjčky, či její části, na fotovoltaiku
          example: false
        photoDoc:
          type: boolean
          description: dokládání účelu fotodokumentací
          example: false
        purpDetail:
          type: string
          maxLength: 2000
          description: "definice účelu"
        purpStatId:
          type: string
          description: stav účelu (LV_PURP_STAT_ID)
        specConds:
          type: array
          description: Specific conditions
          items:
            $ref: '#/components/schemas/CtSpecCond'
        delFlag:
          type: boolean
          description: delete this purpose
    CtMortApplVariantFee:
      type: object
      description: Variant fee details.
      properties:
        feeApplVrntFeeKey:
          type: integer
          format: int64
          description: Application Variant Fee Key / Instanční klíč záznamu
          example: *********
        feeAmt:
          type: number
          description: Fee Amount / Výše poplatku v měně
          example: 1000
        feeTpId:
          type: string
          description: Fee Type Identifier / Typ poplatku (LOV FEE_TP)
        feeDispoFlag:
          type: boolean
          description: Disposable Flag / Příznak, zda poplatek započítat do výpočtu DISPO
          example: true
        feeDstiFlag:
          type: boolean
          description: DSTI Flag / Příznak, zda poplatek započítat do výpočtu DSTI
          example: true
        feeRpsnFlag:
          type: boolean
          description: RPSN Flag / Příznak, zda poplatek započítat do výpočtu RPSN
          example: false
    CtApplVariantIns:
      type: object
      description: Variant insurance details.
      properties:
        insurApplVrntInsurKey:
          type: integer
          format: int64
          description: Application Variant Insurance Key / Instanční klíč záznamu
          example: *********
        insurTpId:
          type: string
          description: Insurance Type IDentifier / Typ pojištění (LOV INSUR_TP)
        insurPremAmt:
          type: number
          description: Insurance Premium Amount / Výše pojistného v měně
        insurPremAmtPct:
          type: number
          description: Insurance Premium Amount Percent / Výše pojistného v procentech
        insurSelFlag:
          type: boolean
          description: Selected Flag / Příznak, zda bude pojištění sjednáno a tedy i započteno do výpočtu slev/přirážek na sazbě (žadatel objednává)
          example: false
        insurDispoFlag:
          type: boolean
          description: Disposable Flag / Příznak, zda pojistné započítat do výpočtu DISPO
          example: false
        insurDstiFlag:
          type: boolean
          description: DSTI Flag / Příznak, zda pojistné započítat do výpočtu DSTI
          example: false
        insurRpsnFlag:
          type: boolean
          description: RPSN Flag / Příznak, zda pojistné započítat do výpočtu RPSN
          example: false
    CtMortApplVariantSur:
      type: object
      description: Variant surcharge details.
      properties:
        surchrgApplVrntSurchrgKey:
          type: integer
          format: int64
          description: Application Variant Surcharge Key / Instanční klíč záznamu
          example: *********
        surchrgTpId:
          type: string
          description: Surcharge Type Identifier / Typ slevy nebo přirážky (LOV SURCHRG_TP)
        surchrgVarncRxPct:
          type: number
          description: Variance Rate Percent / Odchylka od úrokové sazby v %
    CtApplVariantSur:
      type: object
      description: Variant surcharge details.
      properties:
        surchrgApplVrntSurchrgKey:
          type: integer
          format: int64
          description: Application Variant Surcharge Key / Instanční klíč záznamu
          example: *********
        surchrgTpId:
          type: string
          description: Surcharge Type Identifier / Typ slevy nebo přirážky (LOV SURCHRG_TP)
        surchrgVarncRxPct:
          type: number
          description: Variance Rate Percent / Odchylka od úrokové sazby v %
    CtObligationMortSet:
      allOf:
        - $ref: '#/components/schemas/CtObligationMort'
        - type: object
          properties:
            creatorPersonId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
              example: 1
            virtOblgtnId:
              type: string
              description: Virtual Obligation Identifier / Pomocný identifikátor k propojení seznamu kanálů
            creatorOblgtnId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nového závazku. Slouží pro spárování závazku v požadavku a odpovědi.
              example: 1
            delFlag:
              type: boolean
              description: Delete Flag.
              example: true
    CtPersonIncomeVerification:
      type: object
      properties:
        instPtIncVerifKey:
          type: integer
          description: Instance Party Income Key.
          format: int64
          example: 22642
        verifSource:
          type: string
          description: "Income verification source, LovIncVerifSrc."
          example: ACCTRAN
        personIncomeVerificationResultId:
          type: string
          description: "Income verification result, LovIncVerifRslt."
          example: VERIFDONE
        providerCode:
          type: string
          description: Provider Identifier (PSD provider code) / Identifikace banky
            dle číselníku CR_BANK_IB.
        verifTime:
          type: string
          description: Verification Time.
          format: date-time
        advisorId:
          type: string
          description: Advisor Identifier.
        refreshAccounts:
          type: boolean
          description: Refresh Accounts flag.
          example: false
        accounts:
          type: array
          description: CtPersonIncomeVerificationAccount list. Accounts.
          items:
            $ref: '#/components/schemas/CtPersonIncomeVerificationAccount'
        psdSubscriptionId:
          type: string
          description: "Subscription Identifier from Galileo. PSD2 Subscription ID, klíč pomocí kterého se můžeme dostat k PSD záznamům klienta."
        psdUserId:
          type: string
          description: "User Identifier from Galileo. PSD2 userID, ID klienta v rámci providera (pro stejného klienta jiné userId pro KB, jiné userId pro CSAS."
      description: CtPersonIncomeVerification. Person data of verification incoms.
    CtPersonIncomeVerificationAccount:
      type: object
      properties:
        accountPrefix:
          type: string
          description: Bank Contact Account Prefix. Income verification account prefix.
        accountNumber:
          type: string
          description: Bank Contact Account Number. Income verification account number.
        bankCode:
          type: string
          description: "Bank Contact Bank Code (PSD provider code) / Identifikace\
            \ banky dle číselníku. Bank code in CZ, LovBankCzCode."
          example: "5500"
        accountOwnerName:
          type: string
          description: Bank Contact Account Owner Name / jméno majitele účtu. Income
            verification account owner name.
      description: CtPersonIncomeVerificationAccount.
    CtPersonMortSet:
      allOf:
        - $ref: '#/components/schemas/CtPersonMort'
        - type: object
          properties:
            rsdntFlag:
              type: boolean
              description: Resident / Rezident
              example: false
            stayTpId:
              type: string
              description: "Stay Type ID / Druh pobytu "
              example: "********"
            documents:
              type: array
              description: "Documents."
              items:
                $ref: '#/components/schemas/CtPersonMortDoc'
            groupDocuments:
              type: array
              description: "Group documents."
              items:
                $ref: '#/components/schemas/CtPersonMortGroupDoc'
            creatorPersonId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
              example: 1
            delFlag:
              type: boolean
              description: 'Delete flag.'
              example: false
    CtPersonVerification:
      type: object
      properties:
        instPtVerifKey:
          type: integer
          description: Identifikator (klic) zaznamu overeni klienta.
          format: int64
        verifSource:
          type: string
          description: Verification source.
        providerCode:
          type: string
          description: Provider code.
        verifTime:
          type: string
          description: Date/time of verification.
          format: date-time
        advisorId:
          type: string
          description: Advisor identification.
        personVerificationResultId:
          type: string
          description: "Person verification result, LovPersonVerifRslt."
          example: TOVERIF
        refreshAccounts:
          type: boolean
          description: Refresh accounts.
          example: false
        accounts:
          type: array
          description: CtPersonVerificationAccount list. Accounts.
          items:
            $ref: '#/components/schemas/CtPersonVerificationAccount'
        psdSubscriptionId:
          type: string
          description: "Subscription Identifier from Galileo. PSD2 Subscription ID, klíč pomocí kterého se můžeme dostat k PSD záznamům klienta."
        psdUserId:
          type: string
          description: "User Identifier from Galileo. PSD2 userID, ID klienta v rámci providera (pro stejného klienta jiné userId pro KB, jiné userId pro CSAS."
      description: CtPersonVerification. Data of person verification.
    CtPersonVerificationAccount:
      type: object
      properties:
        accountPrefix:
          type: string
          description: Bank Contact Account Prefix. Verification account prefix.
        accountNumber:
          type: string
          description: Bank Contact Account Number. Verification account number.
        bankCode:
          type: string
          description: "Bank Contact Bank Code (PSD provider code) / Identifikace\
            \ banky dle číselníku. Bank code in CZ, LovBankCzCode."
          example: "5500"
        accountOwnerName:
          type: string
          description: Bank Contact Account Owner Name / jméno majitele účtu. Verification
            account owner name.
      description: CtPersonVerificationAccount. Data of person verification account.
    CtPtCorpDetail:
      type: object
      properties:
        ptAcntgTpId:
          type: string
          description: "Party Accounting type Identifier / Typ účetnictví, LovPtAcntgTp."
          example: DBL
        entpInc:
          type: number
          description: Income Amount from Entrepreneur / Příchozí částka od podnikatele.
        taxYear:
          type: integer
          description: Year of taxing / Roční taxa.
          format: int32
        dtiAmt:
          type: number
          description: Dept to income difference amount.
        dprAmt:
          type: number
          description: Depreciation Amount / Výše odpisů.
        taxFlatRx:
          type: number
          description: Flat rate/ Paušál.
        equityAmt:
          type: number
          description: Equity Amount / Vlastní kapitál.
        prftLossAmt:
          type: number
          description: Profit Loss Amount / hospodářský výsledek.
        prevRvnAmt:
          type: number
          description: Previous year's revenue / Tržby za předchozí zdaňovací období.
        rvnAmt:
          type: number
          description: Revenue Amount / Trzby.
        ast:
          type: number
          description: Assets / Aktiva celkem.
      description: CtPtCorpDetail. Corporation detail of party.
    CtPtIncomeMortSet:
      allOf:
        - $ref: '#/components/schemas/CtPtIncomeMort'
        - type: object
          properties:
            creatorPersonId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
              example: 1
            creatorIncomeId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nového příjmu. Slouží pro spárování příjmu v požadavku a odpovědi.
              example: 1
            delFlag:
              type: boolean
              description: Delete Flag.
              example: true
    CtSetMortAppl:
      description: Mortgage (sub)application data.
      allOf:
        - $ref: '#/components/schemas/CtMortAppl'
        - type: object
          properties:
            ptRoleRefreshFlag:
              type: boolean
              description: Party Role Refresh Flag / 0 = merge se stávajícím seznamem, 1 = zaslaný seznam nahradí stávající
            persons:
              type: array
              description: Seznam žadatelů/spolužadatelů
              items:
                $ref: '#/components/schemas/CtSetMortApplPers'
            households:
              type: array
              description: Seznam domácností (pouze u podžádostí)
              items:
                $ref: '#/components/schemas/CtHouseholdSet'
            applVariants:
              type: array
              description: Seznam variant (pouze u podžádostí)
              items:
                $ref: '#/components/schemas/CtMortApplVariant'
            applMetadata:
              type: array
              description: Seznam metadat žádosti
              items:
                $ref: '#/components/schemas/CtApplMetadataEntry'
            incomes:
              type: array
              description: Seznam přijímů (pouze u podžádostí)
              items:
                $ref: '#/components/schemas/CtPtIncomeMortSet'
            applObligations:
              type: array
              description: CtObligation list. Application obligations complex type.
              items:
                $ref: '#/components/schemas/CtObligationMortSet'
            pledges:
              type: array
              description: "Application Pledges."
              items:
                $ref: '#/components/schemas/CtApplPledgesMortSet'
            realtyInsurances:
              type: array
              description: "Realty Insurances decisions."
              items:
                $ref: '#/components/schemas/CtApplRealtyInsurancesMortSet'
            loanSubjects:
              type: array
              description: "Loan Subjects"
              items:
                $ref: '#/components/schemas/CtLoanSubjectsMortSet'
            creatorApplId:
              type: string
              description: "Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové žádosti. Slouží pro spárování žádosti v požadavku a odpovědi."
              example: 1
    CtMortApplPers:
      type: object
      description: ID a role osoby.
      properties:
        adbInstPtKey:
          type: integer
          description: ADB instance party key.
          format: int64
          example: 202212210014
        applPtRoleTpId:
          type: string
          description: Application Party Role Type Identifier / Typ role osoby k žádosti (LOV APPL_PT_ROLE_TP)
        relationToApplicant:
          type: string
          description: Party Party Type Identifier / Typ vztahu spolužadatele k hlavnímu žadateli (LOV PT_PT_REL_TP)
    CtSetMortApplPers:
      description: ID a role osoby.
      allOf:
        - $ref: '#/components/schemas/CtMortApplPers'
        - type: object
          properties:
            creatorPersonId:
              type: string
              description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
              example: 1
    CtSetMortApplPersKeys:
      type: object
      properties:
        adbInstPtKey:
          type: integer
          format: int64
          description: Instance Party Key / Instanční klíč člena domácnosti
          example: *********
        creatorPersonId:
          type: string
          description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
          example: 1
    CtSetBuildingApplPersKeys:
      type: object
      properties:
        adbInstPtKey:
          type: integer
          format: int64
          description: Instance Party Key
          example: *********
        creatorPersonId:
          type: string
          description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové osoby. Slouží pro spárování osoby v požadavku a odpovědi.
          example: 1
    SetMortApplRequest:
      type: object
      description: Set Mortgage Appl service request.
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtPersonMortSet'
        mortgageAppls:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtSetMortAppl'
    CtHouseholdSetResp:
      type: object
      description: Household identificators.
      properties:
        houshKey:
          type: integer
          format: int64
          description: Household key.
          example: *********
        houshOrd:
          type: integer
          description: Household Order / Pořadí domácnosti (unikátní identifikátor v rámci žádosti)
          example: 1
        creatorHousholdId:
          type: string
          description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové domácnosti. Slouží pro spárování domácnosti v požadavku a odpovědi.
          example: 1
    CtIncomesSetResp:
      type: object
      description: Income identificators.
      properties:
        instPtIncKey:
          type: integer
          format: int64
          description: Instance Party Income Key / Instanční klíč daného příjmu
          example: *********
        creatorIncomeId:
          type: string
          description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nového příjmu. Slouží pro spárování příjmu v požadavku a odpovědi.
          example: 1
    CtOblgtnsSetResp:
      type: object
      description: Obligation identificators.
      properties:
        instOblgtnKey:
          type: integer
          format: int64
          description: Instance Obligation Key / Instanční klíč závazku
          example: *********
        creatorOblgtnId:
          type: string
          description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nového závazku. Slouží pro spárování závazku v požadavku a odpovědi.
          example: 1
    CtSetMortApplResp:
      description: Mortgage (sub)application
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        creatorApplId:
          type: string
          description: Dočasný identifikátor vyplněný konzumentem pouze při zakládání nové žádosti. Slouží pro spárování žádosti v požadavku a odpovědi.
          example: 1
        households:
          type: array
          description: Seznam domácností (pouze u podžádostí)
          items:
            $ref: '#/components/schemas/CtHouseholdSetResp'
        incomes:
          type: array
          description: Seznam příjmů (pouze u podžádostí)
          items:
            $ref: '#/components/schemas/CtIncomesSetResp'
        applObligations:
          type: array
          description: Seznam závazků (pouze u podžádostí)
          items:
            $ref: '#/components/schemas/CtOblgtnsSetResp'
    SetMortApplResponse:
      type: object
      description: Set Mortgage Appl service response.
      properties:
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtSetMortApplPersKeys'
        mortgageAppls:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtSetMortApplResp'
    CtObligation:
      type: object
      properties:
        instOblgtnKey:
          type: integer
          description: Instance obligation key.
          format: int64
        virtOblgtnId:
          type: string
          description: Virtual obligation id (only for SET operation).
        adbInstPtKey:
          type: integer
          description: ADB instance party key.
          format: int64
        ofFirstInstlDate:
          type: string
          description: Office date of first instalment / Datum první splátky konsolidovaného závazku.
          format: date
          example: '2022-12-16'
        ofFinInstnId:
          type: string
          description: Office financial institution identifier / Finační instituce, LovFinInstn.
          example: CITIBNK
        ofContrNum:
          type: string
          description: Office contract number of obligation / Číslo smlouvy konsolidovaného úvěru.
        ofTotLoanAmt:
          type: number
          description: Office total amount of cosolidated contract / Výše schváleného úvěru závazku.
        scTotLoanAmt:
          type: number
          description: Scoring total amount of cosolidated contract / Výše schváleného úvěru závazku, puvodni uverovy limit zjisteny ve scoringu.
        ofTotInstlCnt:
          type: integer
          description: Office total count of instalment / Celkový počet splátek závazku.
          format: int32
        ofIntrsRx:
          type: number
          description: Office interest rate / Urok.
        scIntrsRx:
          type: number
          description: Scoring interest rate / Urok zjisteny ve scoringu.
        ofInstl:
          type: number
          description: Office instalment / Splátka.
        scInstl:
          type: number
          description: Scoring instalment / Vyska splatky zjistena ve scoringu.
        ofOblgtnProdTpId:
          type: string
          description: Office obligation product type ID / ucel zavazku, zadano do P4, LovOblgtnProdTp.
          example: RCL
        scOblgtnProdTpId:
          type: string
          description: Scoring obligation product type ID / Ucel zavazku, zjisteny ve scoringu, LovOblgtnProdTp.
          example: ROD
        ofMaturityDate:
          type: string
          description: Office origin product maturity date.
          format: date
          example: '2022-12-16'
        ofAmt:
          type: number
          description: Office origin obligation amount.
        scAmt:
          type: number
          description: Scoring origin obligation amount / Zbývající jistina/zůstatek pro splátkové, limit pro KTK a KK zjisteny ve scoringu. Hlavní údaj pro finalizaci u konsolidace.
        finInstnGrpCode:
          type: string
          description: Identifikace fin. instituce pro seřazení závazků dle poskytovatele.
        equaContrNum:
          type: string
          description: Identifikace Equa produktu pro interní refinancování.
        ofrIntrsRx:
          type: number
          description: Definuje na úrovni závazku nabízenou sazbu pro REFI/CON.
        origScOfrIntrsRx:
          type: number
          description: Scoring Offer Interest Rate Original / původní úroková sazba nastavená RISKem.
        origContractRequired:
          type: boolean
          description: Definuje na úrovni závazku požadavek na doložení původní smlouvy pro REFI/CONS.
          example: false
        scPrimaryOblgtn:
          type: boolean
          description: Definuje na úrovni závazku kandidáty na primární závazek pro REFI/CONS.
          example: false
        ofAccNum:
          type: string
          description: Previous bank account number.
        ofAccNumPrefix:
          type: string
          description: Office account number prefix / předčíslí účtu.
        ccyId:
          type: string
          description: Currency / měna, LovCcy.
          example: CZK
        ofCrntAccFeeAmt:
          type: number
          description: Office current account fee amount / Poplatek za BU.
        ofContrFeeAmt:
          type: number
          description: Office contract fee amount/ Poplatek za vedení produktu.
        ofPrinc:
          type: number
          description: Office principal / Jistina.
        ofInstlCnt:
          type: integer
          description: Office instalment count / Zbývající počet splátek.
          format: int32
        ofOblgtnLoanPurpId:
          type: string
          description: Obligation loan purpose / Účel původního úvěru, LovOblgtnLoanPurp.
          example: CAR
        ofBankId:
          type: string
          description: Office bank / Původní bankovní dům.
        ofVarSymbol:
          type: string
          description: Office variable symbol / Variabilní symbol.
        ofConstSymbol:
          type: string
          description: Office constant symbol / Konstantní symbol.
        ofSpecSymbol:
          type: string
          description: Office specific symbol / Specifický symbol.
        ofOblgtnDocTpId:
          type: string
          description: Obligation document type ID, LovOblgtnDocTp.
          example: geAll
        ofComment:
          type: string
          description: Comment.
        scCcbContractId:
          type: string
          description: CCB ID úvěru zjistene ve scoringu.
        clFinInstnId:
          type: string
          description: Financial institution / Id banky/instituce, která poskytla původní úvěr, zadané klientem na první kalkulačce, LovFinInstn.
          example: OTHER
        scFinInstnCode:
          type: string
          description: Kód banky z registrů, slouží k poznání úvěrů ze stejné banky a rozlišení banka/nebanka.
        clOrder:
          type: integer
          description: Poradi zavazku tak jak jej zadal klient, clOrder=1 znamena primarni zavazek.
          format: int32
          example: 1
        scOrder:
          type: integer
          description: Poradi zavazku tak jak jej ulozil scoring.
          format: int32
        oblgtnSelected:
          type: boolean
          description: Client selected flag / Příznak, zda si klient ve finalizaci vybral závazek ke konsolidaci.
          example: false
        scIsApplicable:
          type: boolean
          description: Priznak ze je zavazek vhodny k refinancovani.
          example: false
        scIsMandatory:
          type: boolean
          description: Priznak ze je zavazek povinny k refinancovani.
          example: false
        scIntContrNum:
          type: string
          description: Internal product / contract number.
        scIntSourceSystem:
          type: string
          description: Internal product / source system.
        scIntSourceSystemId:
          type: string
          description: Internal product / source system ID.
        primaryOblgtn:
          type: boolean
          description: Priznak primarniho zavazku - 1/true, nebo 0/false/nevyplneno.
          example: false
        finRepayedAmt:
          type: number
          description: Částka stanovená elbosem pro splacení závazku.
        totRepaymentFlag:
          type: boolean
          description: Uplne splaceni.
          example: false
        oblgtnAmtAfterRepayment:
          type: number
          description: Zbývající pohledávka za klientem.
        delFlag:
          type: boolean
          description: Delete Flag.
          example: false
        scgRcmdFlag:
          type: boolean
          description: Scoring Recommended Flag / Doporučený závazek
      description: CtObligation. Application obligations complex type.
    CtBuildingObligation:
      allOf:
        - $ref: '#/components/schemas/CtObligation'
        - type: object
          properties:
            scContrSignDate:
              type: string
              format: date
              description: datum uzavření smlouvy
            ofContrSignDate:
              type: string
              format: date
              description: datum uzavření smlouvy
            ofContrName:
              type: string
              description: označení smlouvy
            ofContrNumReg:
              type: string
              description: registrační číslo úvěrové smlouvy
    CtObligationSignChannel:
      type: object
      properties:
        signInstOblgtnKey:
          type: integer
          description: Obligation reference key.
          format: int64
        signVirtOblgtnId:
          type: string
          description: Virtual obligation id which sign channel is related to.
        signChannelId:
          type: string
          description: Signing chanel, LovCnl.
          example: WEB
        signPosId:
          type: string
          description: Signature point of sales ID, LovPos.
          example: RBPHPKCB
      description: CtObligationSignChannel. Obligation sign channels complex type.
    CtSetLoanAppl:
      type: object
      properties:
        applTpId:
          type: string
          description: Application type ID, LovApplTp.
          example: NEW_PROD
        applStatId:
          type: string
          description: Application state ID, LovApplStat.
          example: OO_INIT
        applDate:
          type: string
          description: Application Date. Client signature date of application.
          format: date-time
        posId:
          type: string
          description: Point of sales ID, LovPos.
          example: RBPHPKCB
        firstTouchPoint:
          type: string
          description: First Touch Point.
          example: '18000000'
        applReason:
          type: string
          description: Application Reason / Důvod žádosti.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        preApprOfrId:
          type: string
          description: Jednoznačný identifikátor nabídky určený SPSS.
        opportunityId:
          type: string
          description: Identifikátor příležitosti ze Siebelu.
        fullApplFlag:
          type: boolean
          description: Příznak, zda musel klient v rámci predschvalene nabídky vyplnit celou žádost (true) nebo vyplnil jenom data týkající se žádosti a přeskočil osobní údaje a sociodemografika (false).
          example: false
        ccyId:
          type: string
          description: Currency, LovCcy.
          example: CZK
        distCnlId:
          type: string
          description: Distribution chanel, LovCnl.
          example: BROKER
        advisorId:
          type: string
          description: Advisor Identifier / Identifikace poradce.
        advisorName:
          type: string
          description: Advisor Name / Jméno poradce.
        fulfillmentCnlId:
          type: string
          description: Fulfillment chanel, LovCnl.
          example: WEB
        wrkPlaceId:
          type: string
          description: Work place ID.
        busProdSubTp:
          type: string
          description: Business product sub-type, LovBusProdSubTp. Application product type taken from one of variants (logic in ADB). Relevant for getApplication only, setApplication ignores this element.
          example: REFO
        browserInfo:
          type: string
          description: Browser Info.
        receivedTraderName:
          type: string
          description: Trader Name / Název obchodníka, který bychom měli obdržet v rámci iniciace žádosti (bude využíváno zejm. u žádostí přes platební bránu pro identifikaci skutečného obchodníka).
        pushNotifPermissionFlag:
          type: boolean
          description: Push Notification Permission Flag / Nový atribut plněný jen pro žádosti o CL z MB. (true = povolena, false = zakázána)
          example: false
        activationId:
          type: string
          description: ID aktivace aplikace pro dohledání TOKENu - pro mobilni bankovnictvi.
        contAccessReqFlag:
          type: boolean
          description: Contacts Access Request Flag (false = nebyla zobrazena obrazovka pro vyžádání telefonního seznamu, true = byla zobrazena), aktualizuje se při zobrazení obrazovky.
          example: false
        contAccessAlwFlag:
          type: boolean
          description: Contacts Access Allowed Flag (false = nebyl udělen souhlas, true = byl udělen souhlas), plní se při potvrzení/odmítnutí souhlasu (ať už navržené obrazovky či souhlasu na úrovni aplikace).
          example: false
        contAccessScreenId:
          type: string
          description: Contact Access Screen Identifier / Kód obrazovky mobilní apky pro účely AB testingu a analýz, plní se při zobrazení obrazovky.
        dscntIntrs:
          type: number
          description: Discount Interest / Nabídnutá sleva na sazbě (1%=0,01)), plní se při zobrazení obrazovky.
        telcoQueryAllowedFlag:
          type: boolean
          description: Povolen dotaz do Telco Score.
        authMobSearchFlag:
          type: boolean
          description: Authorization Phone Search Flag / Plní se po ověření telefonu v DB - pokud se vrátí osoba, plní se true, jinak false.
        orderNr:
          type: string
          description: Order Number / Císlo objednávky.
        url:
          type: string
          description: Application Data Segment / Dle jakého segmentu byl proveden sběr dat pro danou žádost.
        segments:
          type: array
          description: Segment list.
          items:
            type: string
            description: Segment list.
        firstTouchPointOwnr:
          type: string
          description: First touch point owner / zadavatel žádosti.
        promoCode:
          type: string
          description: Promo code / Kód promo akce.
        accNumPrefix:
          type: string
          description: Account number prefix.
        accNum:
          type: string
          description: Account number.
        accBankCode:
          type: string
          description: Account bank code. Bank code in CZ, LovBankCzCode.
          example: '5500'
        postpndDrawFlag:
          type: boolean
          description: Postponed Drawing Flag / Odložené čerpání.
          example: 'false'
      description: CtSetLoanAppl extends CtApplication. Application data. Set loan application complex type.
    CtSetBuildingLoanAppl:
      type: object
      description: Building application data.
      properties:
        applTpId:
          type: string
          description: Application type ID, LovApplTp.
          example: NEW_PROD
        applStatId:
          type: string
          description: Application state ID, LovApplStat.
          example: OO_INIT
        applDate:
          type: string
          description: Application Date. Client signature date of application.
          format: date-time
        posId:
          type: string
          description: Point of sales ID, LovPos.
          example: RBPHPKCB
        firstTouchPoint:
          type: string
          description: First Touch Point.
          example: '18000000'
        applReason:
          type: string
          description: Application Reason / Důvod žádosti.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        preApprOfrId:
          type: string
          description: Jednoznačný identifikátor nabídky určený SPSS.
        opportunityId:
          type: string
          description: Identifikátor příležitosti ze Siebelu.
        ccyId:
          type: string
          description: Currency, LovCcy.
          example: CZK
        distCnlId:
          type: string
          description: Distribution chanel, LovCnl.
          example: BROKER
        fulfillmentCnlId:
          type: string
          description: Fulfillment chanel, LovCnl.
          example: WEB
        wrkPlaceId:
          type: string
          description: Work place ID.
        busProdSubTp:
          type: string
          description: Business product sub-type, LovBusProdSubTp. Application product type taken from one of variants (logic in ADB). Relevant for getApplication only, setApplication ignores this element.
          example: REFO
        browserInfo:
          type: string
          description: Browser Info.
        telcoQueryAllowedFlag:
          type: boolean
          description: Povolen dotaz do Telco Score.
        authMobSearchFlag:
          type: boolean
          description: Authorization Phone Search Flag / Plní se po ověření telefonu v DB - pokud se vrátí osoba, plní se true, jinak false.
        firstTouchPointOwnr:
          type: string
          description: First touch point owner / zadavatel žádosti.
        promoCode:
          type: string
          description: Promo code / Kód promo akce.
        consents:
          type: array
          description: Consent list
          items:
            $ref: '#/components/schemas/CtConsent'
        quester:
          $ref: '#/components/schemas/CtQuesterSet'
    CtBuildingApplVariant:
      type: object
      description: building loan variants.
      properties:
        applVariantKey:
          type: integer
          description: Variant Key.
          format: int64
          example: 2214874003641
        applVariantTpId:
          type: string
          description: Application variant type ID, LovApplVariantTp.
          example: REQ
        applVariantSignCnlFlag:
          type: boolean
          description: Indicates whether Application Variant Sign Channel is changed or not.
          example: false
        busProdTpId:
          type: string
          description: Business product type ID, LovBusProdTp.
          example: RCL
        busProdSubTpId:
          type: string
          description: Business product sub-type ID, LovBusProdSubTp.
          example: RCL_STANDARD
        finaAmt:
          type: number
          description: Total amount of loan or limit.
          example: 100000
        instlAmt:
          type: number
          description: Installment amount.
          example: 2215
        instlCnt:
          type: integer
          description: Total number of installments.
          format: int64
          example: 60
        maturityDate:
          type: string
          description: Last instalment date / datum poslední splátky
          format: date
          example: '2028-03-30'
        intrsRx:
          type: number
          description: Interest rate contracted.
          example: 0.07
        rpsn:
          type: number
          description: Anual Percentage Rate of charge (RPSN) - contracted / RPSN - Roční procentní sazba nákladů.
          example: 0.0723
        camCode:
          type: string
          description: Campaign Code.
        minAvlblAmt:
          type: number
          description: Minimal available limit.
        minInstlAmt:
          type: number
          description: Minimal installment amount.
        minInstlCnt:
          type: integer
          description: Minimal number of installments.
          format: int64
        maxAvlblAmt:
          type: number
          description: Maximal available limit.
        maxInstlAmt:
          type: number
          description: Maximal installment amount.
        maxInstlCnt:
          type: integer
          description: Maximal number of installments.
          format: int64
        upsellAmt:
          type: number
          description: Up sell amount/ Navýšení.
          example: 0
        maxUpsellAmt:
          type: number
          description: Maximum approved amount.
        ccyId:
          type: string
          description: Currency ID references LOV CCY/ ID měny, LovCcy.
          example: CZK
        totRpmtAmt:
          type: number
          description: Total Saved Amount / Celová častka splatná spotřeb.
          example: 132900
        refiSavedAmt:
          type: number
          description: Total Repayment Amount/ Částka kterou klient ušetřil.
        accNumPrefix:
          type: string
          description: Account number prefix.
        accNum:
          type: string
          description: Account number.
          example: '**********'
        accBankCode:
          type: string
          description: Account bank code. Bank code in CZ, LovBankCzCode.
          example: '5500'
        actSumInstlAmt:
          type: number
          description: Aktuální výše splátek všech konsolidovaných úvěrů klienta. Výpočet Tauru.
        consLoanInstlAmt:
          type: number
          description: Nová výše splátky konsolidovaného úvěru. Výpočet Tauru.
        dayIntrsAmt:
          type: number
          description: Denní úrok.
        intrsDscntFlag:
          type: boolean
          description: Interest rate discount allowed.
          example: false
        intnOblgtnAmt:
          type: number
          description: Suma částek interních refinancovaných úvěrů.
        extnOblgtnAmt:
          type: number
          description: Suma částek externích refinancovaných úvěrů.
        repreDrawDate:
          type: string
          description: 'Representative Drawing Date / Reprezentativní příklad: datum čerpání (první den nadcházejícího kalendářního měsíce měsíce).'
          format: date
          example: '2022-12-16'
        repreMaturityDate:
          type: string
          description: 'Representative Maturity Date / Reprezentativní příklad: datum splatnosti (datum čerpání + 3 měsíce (1. kalendářní den)).'
          format: date
          example: '2023-04-01'
        repreInt:
          type: number
          description: Reprezentativní úrok (příklad).
        repreInt3M:
          type: number
          description: Reprezentativní 3 měsíční úrok (příklad).
        costs3M:
          type: number
          description: Reprezentativní 3 měsíční náklady (příklad).
        delFlag:
          type: boolean
          description: Delete Flag.
          example: true
        declaredPurpose:
          type: string
          description: Secondary loan purpose, LovLoanPurpScnd.
          example: OTHER
        maxExtOblgtnAmt:
          type: number
          description: Maximum approved external obligation amount.
        minIrForFinalization:
          type: number
          description: Minimum Interest Rate For Finalization.
        origScOfrIntrsRx:
          type: number
          description: Scoring Offered Interest Rate Original / Odvozená sazba z SPSS.
        payCpcyForFinal:
          type: number
          description: Payment Capacity For Finalization / Hodnota pro výpočet platební kapacity klienta
        maxAvlblTopup:
          type: number
          description: Max Available Top UP / Maximální dostupné navýšení
        prodIntrsCodeId:
          type: string
          description: Product interest code identifier, LovProdIntrsCode
        loanPurposes:
          type: array
          description: Seznam účelů uvěru k variantě.
          items:
            $ref: '#/components/schemas/CtBuildingApplVariantPurpose'
        loanCategories:
          type: array
          description: Loan categories
          items:
            $ref: '#/components/schemas/CtBuildingLoanCategory'
        variantParameters:
          type: array
          description: CtVariantParameter list. Application variant parameters complex type.
          items:
            $ref: '#/components/schemas/CtBuildingVariantParameter'
        signChannels:
          type: array
          description: CtSignChannel list. Sign channel complex type.
          items:
            $ref: '#/components/schemas/CtSignChannel'
        insurances:
          type: array
          description: Seznam pojištění k variantě.
          items:
            $ref: '#/components/schemas/CtApplVariantIns'
        surcharges:
          type: array
          description: Seznam slev a přirážek k variantě.
          items:
            $ref: '#/components/schemas/CtApplVariantSur'
        fixPeriod:
          type: string
          description: interest rate fix period
        closeRelCoFin:
          type: boolean
          description: Příznak zapojení osoby blízké
        ecoInvestTpId:
          type: string
          description: eco investment type (LV_ECO_INVEST_TP)
        ecoInvestShare:
          type: number
          description: eco investment share
    SetLoanApplRequest:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyIdChoice'
        primaryOwner:
          $ref: '#/components/schemas/CtPerson'
        waitForUnification:
          type: boolean
          description: Wait for unification flag.
          example: false
          default: false
        primaryOwnerIncome:
          type: array
          description: CtPtIncome list. Party incomes.
          items:
            $ref: '#/components/schemas/CtPtIncome'
        loanAppl:
          $ref: '#/components/schemas/CtSetLoanAppl'
        applVariants:
          type: array
          description: CtApplVariant list. Application variants. Application variant complex type.
          items:
            $ref: '#/components/schemas/CtApplVariant'
        applVariantParams:
          $ref: '#/components/schemas/CtVariantParameters'
        signChannels:
          $ref: '#/components/schemas/CtSignChannels'
        applObligations:
          type: array
          description: CtObligation. Application obligations complex type.
          items:
            $ref: '#/components/schemas/CtObligation'
        obligationSignChannels:
          type: array
          description: CtObligationSignChannel. Obligation sign channels complex type.
          items:
            $ref: '#/components/schemas/CtObligationSignChannel'
        applMetadata:
          type: array
          description: CtApplMetadataEntry. Application metadata. Application metadata entry complex type.
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
      description: Set Loan Appl service request.
    SetLoanApplResponse:
      required:
        - applKey
        - busApplId
      type: object
      properties:
        applKey:
          type: integer
          description: ApplKey of created/updated application.
          format: int64
          example: *********
        busApplId:
          type: string
          description: BusApplId of created/updated application.
          example: '**********'
      description: Set Loan Appl service response.
    CtBuildingLoanCategory:
      type: object
      properties:
        purpCatgId:
          type: string
          description: Loan category (LV_CATG_LOAN_PURP)
        primFlag:
          type: boolean
          description: flag primární kategorie
    SetBuildingLoanApplRequest:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyIdChoice'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtPersonRsts'
        buildingLoanAppl:
          $ref: '#/components/schemas/CtSetBuildingLoanAppl'
        applVariants:
          type: array
          description: CtApplVariant list. Application variants. Application variant complex type.
          items:
            $ref: '#/components/schemas/CtBuildingApplVariant'
        applObligations:
          type: array
          description: CtObligation. Application obligations complex type.
          items:
            $ref: '#/components/schemas/CtBuildingObligation'
        obligationSignChannels:
          type: array
          description: CtObligationSignChannel. Obligation sign channels complex type.
          items:
            $ref: '#/components/schemas/CtObligationSignChannel'
        applMetadata:
          type: array
          description: CtApplMetadataEntry. Application metadata. Application metadata entry complex type.
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
        docs:
          type: array
          items:
            $ref: '#/components/schemas/CtDocs'
      description: Set Building Loan Appl service request.
    SetBuildingLoanApplResponse:
      required:
        - applKey
      type: object
      properties:
        applKey:
          type: integer
          description: ApplKey of created/updated application.
          format: int64
          example: *********
        busApplId:
          type: string
          description: BusApplId of created/updated application.
          example: '**********'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtSetBuildingApplPersKeys'
      description: Set Building Loan Appl service response.
    SendApplCommentRequest:
      required:
        - applKey
        - busApplId
        - siebelPtId
        - firstTouchPoint
        - familyName
        - amount
        - cmt
      type: object
      properties:
        applKey:
          type: string
          description: ApplKey of created/updated application.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business application identificator
          format: int64
        siebelPtId:
          type: string
          description: Siebel party identifier
          example: '**********'
        firstTouchPoint:
          type: string
          description: First touch point
        firstName:
          type: string
          description: First name
        middleName:
          type: string
          description: Middle name
        familyName:
          type: string
          description: Family name
        titleBefore:
          type: string
          description: Title before name
        titleBehind:
          type: string
          description: Title behind name
        amount:
          type: number
          description: Title behind name
        cmt:
          type: string
          description: Comment
      description: Snd application comment request.
    SendApplCommentResponse:
      required:
        - email
      type: object
      properties:
        email:
          type: string
          description: email where the comment was sent.
          example: <EMAIL>
      description: Send appl comment service response.
    SetApplCommentsRequest:
      required:
        - refreshFlag
        - cmt
      type: object
      properties:
        refreshFlag:
          type: boolean
          description: Party Role Refresh Flag / 0 = merge se stávajícím seznamem, 1 = zaslaný seznam nahradí stávající
        cmts:
          type: array
          description: Pole komentářů
          items:
            type: string
            example: komentar
      description: Set application comments request.
    CreditCardAppl:
      type: object
      properties:
        applTpId:
          type: string
          description: Application type ID, LovApplTp.
          example: NEW_PROD
        applStatId:
          type: string
          description: Application state ID, LovApplStat.
          example: OO_INIT
        applDate:
          type: string
          description: Application Date. Client signature date of application.
          format: date-time
        posId:
          type: string
          description: Point of sales ID, LovPos.
          example: RBPHPKCB
        firstTouchPoint:
          type: string
          description: First Touch Point.
          example: '18000000'
        applReason:
          type: string
          description: Application Reason / Důvod žádosti.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        preApprOfrId:
          type: string
          description: Jednoznačný identifikátor nabídky určený SPSS.
        opportunityId:
          type: string
          description: Identifikátor příležitosti ze Siebelu.
        ccyId:
          type: string
          description: Currency, LovCcy.
          example: CZK
        distCnlId:
          type: string
          description: Distribution chanel, LovCnl.
          example: BROKER
        advisorId:
          type: string
          description: Advisor Identifier / Identifikace poradce.
        advisorName:
          type: string
          description: Advisor Name / Jméno poradce.
        fulfillmentCnlId:
          type: string
          description: Fulfillment chanel, LovCnl.
          example: WEB
        wrkPlaceId:
          type: string
          description: Work place ID.
        busProdSubTp:
          type: string
          description: Business product sub-type, LovBusProdSubTp. Application product type taken from one of variants (logic in ADB). Relevant for getApplication only, setApplication ignores this element.
          example: REFO
        browserInfo:
          type: string
          description: Browser info.
        telcoQueryAllowedFlag:
          type: boolean
          description: Povolen dotaz do Telco Score.
          example: false
        authMobSearchFlag:
          type: boolean
          description: Authorization phone search flag / Plní se po ověření telefonu v DB - pokud se vrátí osoba, plní se true, jinak false.
        segments:
          type: array
          description: Segment list.
          items:
            type: string
            description: Segment list.
        firstTouchPointOwnr:
          type: string
          description: First touch point owner / zadavatel žádosti.
        promoCode:
          type: string
          description: Promo code / Kód promo akce.
        cobrandProgram:
          type: integer
          description: Cobrand Program / Kód programu cobrandové karty.
          format: int64
        cardDistributionType:
          type: string
          description: Distribution type key / Způsob doručení karty (LOV CARD_DIST_TP).
        cardDistributionInternalBranchId:
          type: string
          description: Distribution internal branch identifier / Interní pobočka, na kterou má být karta zaslána (LOV CARD_DIST_INTN_BRN).
        cardDistributionExternalBranchId:
          type: string
          description: Distribution external branch identifier /  Externí pobočka, na kterou má být karta zaslána (LOV POS).
        cardDistributionExpressFlag:
          type: boolean
          description: Distribution express flag / Požadavek na expresní vydání karty.
          example: false
        cobrandCardPhoneNumbers:
          type: array
          description: Telefonní čísla svázané s cobrandovou kartou.
          items:
            type: string
            description: Telefonní čísla svázané s cobrandovou kartou.
        rejectRsnId:
          type: string
          description: External rejection reason type, LovExtnRejRsnTp.
          example: REJ_INVALID_ID
        rejectRsn:
          type: string
          description: rejectRsn
        rejectRsnTp:
          type: string
          description: rejectRsnTp
        registryResult:
          type: string
          description: Risk band, LovRiskBand.
        finalRiskClass:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        validFromDate:
          type: string
          description: Valid From Date.
          format: date-time
        validToDate:
          type: string
          description: Valid To Date.
          format: date-time
        contrSignDate:
          type: string
          description: Signature Date - Date when the LAST applicant signed the contract / datum podpisu smlouvy posledním žadatelem.
          format: date
          example: '2022-12-16'
        contrNum:
          type: string
          description: CC internal contract number. Serves for identifiying relship between CC end other systems. / Číslo smlouvy.
        incVerifPSD2Flag:
          type: boolean
          description: Indicate posibility of income verification by PSD2 channel / Indikuje možnost ověřování přijmu přes PSD2.
          example: false
        incVerifAccStmFlag:
          type: boolean
          description: Indicate posibility of income verification by the account statement / Indikuje možnost ověřování přijmu pomocí výpisu z účtu.
          example: false
        incVerifStmUploadFlag:
          type: boolean
          description: Indicate posibility of income verification by upload of account statement / Indikuje možnost ověřování přijmu přímým nahráním výpisu z účtu.
          example: false
        incVerifCallFlag:
          type: boolean
          description: Indicate posibility of income verification by phone call to employer / Indikuje možnost ověřování přijmu pomocí telefonického ověření u zaměstnavatele.
          example: true
        validTo:
          type: string
          description: Expirace současné karty
          format: date
          example: '2022-12-16'
        maskedNumber:
          type: string
          description: Maskované číslo karty
        currentAmount:
          type: number
          description: Současný limit karty
        embossedName:
          type: string
          description: Jméno na kartě
        rpc5key:
          $ref: '#/components/schemas/Rpc5Key'
        cardIdentificationType:
          type: string
          description: 'Typ identifikátoru karty; enumeration=cardHash, coreId1, coreId2, Pozn: pro kreditní karty nyní vždy cardHash'
        cardIdentificationCode:
          type: string
          description: Identifikátor karty
        intrsRxATM:
          type: number
          description: ATM úroková míra na kartě
        cmt:
          type: string
          description: comment / komentar
      description: CreditCardAppl extends CtApplication. Credit card application data. Get credit card application complex type.
    Rpc5Key:
      type: object
      properties:
        issuer:
          type: string
        branch:
          type: string
        product:
          type: string
        accountNumber:
          type: string
        correlative:
          type: string
        cardSequenceNumber:
          type: string
      description: Identifikátor podkladového karetního účtu
    SetCreditCardApplRequest:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyIdChoice'
        primaryOwner:
          $ref: '#/components/schemas/CtPerson'
        waitForUnification:
          type: boolean
          description: Wait for unification flag.
          example: false
          default: false
        primaryOwnerIncome:
          type: array
          description: CtPtIncome list. Party incomes.
          items:
            $ref: '#/components/schemas/CtPtIncome'
        creditCardAppl:
          $ref: '#/components/schemas/CreditCardAppl'
        applVariants:
          type: array
          description: CtApplVariant list. Credit card application variants. Credit card application variant complex type.
          items:
            $ref: '#/components/schemas/CtApplVariant'
        applVariantParams:
          $ref: '#/components/schemas/CtVariantParameters'
        signChannels:
          $ref: '#/components/schemas/CtSignChannels'
        applMetadata:
          type: array
          description: CtApplMetadataEntry. Application metadata. Application metadata entry complex type.
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
      description: Set Credit Card Appl service request.
    SetCreditCardApplResponse:
      required:
        - applKey
        - busApplId
      type: object
      properties:
        applKey:
          type: integer
          description: ApplKey of created/updated application.
          format: int64
          example: *********
        busApplId:
          type: string
          description: BusApplId of created/updated application.
          example: '**********'
      description: Set Credit Card Appl service response.
    ApplicationForBroker:
      required:
        - applDateTime
        - applStatId
        - applTpId
        - applicantFamilyName
        - applicantName
        - brokerId
        - busApplId
        - busProdSubTpId
        - busProdTpId
        - finaAmt
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key / Klíč žádosti.
          format: int64
          example: *********
        applTpId:
          type: string
          description: 'Application Type Identifier / Typ žádosti. LOVS: LovApplTp'
          example: NEW_PROD
        applStatId:
          type: string
          description: 'Application Status Identifier / Stav žádosti. LOVS: LovApplStat'
          example: PZLEAD
        brokerId:
          type: string
          description: Broker Identifier / Identifikace prodejce.
        busApplId:
          type: string
          description: Business Application Identifier / Business číslo žádosti.
          example: '**********'
        busProdTpId:
          type: string
          description: Business Product Identifier / Typ schvalovaného produktu. LOVS LovBusProdTp
          example: ROD
        busProdSubTpId:
          type: string
          description: Business Product Sub Type Identifier / Podtyp schvalovaného produktu. LOVS LovBusProdSubTp
          example: CCA_CZK_PO
        applDateTime:
          type: string
          description: Application Time / Datum a čas založení žádosti.
          format: date-time
        contrSignDate:
          type: string
          description: Contract Sign Date / Datum podpisu.
          format: date
        finaAmt:
          type: number
          description: Total amount of loan or limit / Celková výše pujčky nebo limitu.
        instPtKey:
          type: integer
          description: Instance Party Key / Klíč instance hlavního žadatele.
          format: int64
          example: 836634
        applicantName:
          type: string
          description: Applicant First Name / Jméno hlavního žadatele.
        applicantFamilyName:
          type: string
          description: Applicant Family Name / Příjmení hlavního žadatele.
      description: Get Application For Broker service response element. (approval.applicationlistsservice_v1.getapplicationlistforbroker.CtApplication)
    ApplicationForBrokerResult:
      type: object
      properties:
        applications:
          type: array
          description: List of applications for broker.
          items:
            $ref: '#/components/schemas/ApplicationForBroker'
        recordCount:
          type: integer
          description: Number of records in the result / Počet záznamů výsledku.
          format: int64
      description: Get Application For Broker service response.
    GetApplStatusResponse:
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key Identifier.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business Application Identifier (external).
          example: '**********'
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        applStatus:
          type: string
          description: Application status. LOV APPL_STAT.
          example: PZLEAD
        changeApplStatusDate:
          type: string
          description: Last change time.
          format: date-time
          example: '2022-12-16T10:40:17.2393401+01:00'
      description: Get Appl Status service response element.
    StateLog:
      required:
        - state
      type: object
      properties:
        state:
          type: string
          description: 'LOVS: LovApplStat'
        changeDateTime:
          type: string
          description: Change date time / Datum a čas změny stavu.
          format: date-time
          example: '2022-02-12T15:44:44.123Z'
        prevState:
          type: string
          description: 'Previous State, LOVS: LovApplStat.'
          example: AO_CHCKD
      description: Get Appl Status History State service response element. State Log Complex Type (approval.applicationstate.CtStateLog).
    CheckEcommerceRightsResponse:
      type: object
      properties:
        result:
          type: boolean
          description: Result (true = has rights/exists, false = has not rights/doesn't exist).
          example: true
      description: Check Ecommerce Rights service response element. SOA/GetECommerceApplRithgs response.
    CheckLoanApplExistResponse:
      required:
        - applKey
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key Identifier.
          format: int64
          example: *********
      description: Check Loan Appl Exist service response element.
    GetBasicDetailServiceResponse:
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key / Klíč žádosti.
          format: int64
          example: *********
        busApplId:
          type: string
          description: "\tBusiness Application Identifier / Business číslo žádosti."
          example: '**********'
        hashId:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        applCatgId:
          type: string
          description: 'Application Category Identifier. LOVS: LovApplCatg'
          example: LOAN
        applTpId:
          type: string
          description: 'Application Type Identifier / Typ žádosti. LOVS: LovApplTp'
          example: NEW_PROD
        applStatId:
          type: string
          description: 'Application Status Identifier / Stav žádosti. LOVS: LovApplStat'
          example: PZLEAD
        applTechStatId:
          type: string
          description: 'Application Technical Status Identifier. LOVS: LovApplTechStat'
          example: INVS_CALC
        applDate:
          type: string
          description: Client Signature Date Of Application.
          format: date-time
        busProdTpId:
          type: string
          description: Business Product Identifier / Typ schvalovaného produktu. LOVS LovBusProdTp
          example: ROD
        busProdSubTpId:
          type: string
          description: Business Product Sub Type Identifier / Podtyp schvalovaného produktu. LOVS LovBusProdSubTp
          example: CCA_CZK_PO
        firstTouchPoint:
          type: string
          description: First Touch Point.
        firstTouchPointOwnr:
          type: string
          description: First Touch Point Owner / Zadavatel zadosti.
        advrId:
          type: string
          description: Advisor Identifier / Identifikace poradce.
        distCnlId:
          type: string
          description: 'Distribution Chanel Identification. LOVS: LovCnl'
          example: WEB
        fulfillmentCnlId:
          type: string
          description: 'Fulfilment channel. LOVS: LovCnl'
          example: WEB
        posId:
          type: string
          description: 'POS identification. LOVS: LovPos'
          example: RBPHPKCB
        applComplPosId:
          type: string
          description: 'Application Complete POS ID / POS completace zadosti. LOVS: LovPos'
          example: RBPHPKCB
        contrNum:
          type: string
          description: CC Internal Contract Number. Serves for identifiying relship between CC end other systems.
        contrSignDate:
          type: string
          description: Signature Date - Date when the LAST applicant signed the contract / Datum podpisu smlouvy poslednim zadatelem.
          format: date
        contrSignPosId:
          type: string
          description: 'Contract Sign POS ID / POS Pobocka podepsani kontraktu. LOVS: LovPos'
          example: RBPHPKCB
        contrSignAdvrId:
          type: string
          description: Contract Sign Advisor Identifier / ID poradce.
        contrSignAdvrName:
          type: string
          description: Contract Sign Advisor Name / Jmeno poradce.
        wrkPlaceId:
          type: string
          description: Work Place Identifier.
        propId:
          type: string
          description: Identifikator nabidky, ze ktere zadost vznikla.
        applRsn:
          type: string
          description: Application Reason (duvod zadosti).
        browserInfo:
          type: string
          description: Browser Info.
        telcoQueryAlwFlag:
          type: boolean
          description: Povolen dotaz do Telco Score?
          example: false
        authPhoneSearchFlag:
          type: boolean
          description: Authorization Phone Search Flag / Plni se po overeni telefonu v DB - pokud se vrati osoba, plni se true, jinak false.
          example: false
        promoCode:
          type: string
          description: Promo Code / Kod promo akce.
        oprtyId:
          type: string
          description: Opportunity Identifier / Identifikace prilezitosti.
        instPtKey:
          type: integer
          description: Instance Party Key / Klíč instance hlavního žadatele.
          format: int64
          example: 836634
        partyId:
          type: string
          description: Siebel Party ID.
          example: '20145007'
        validToDate:
          type: string
          description: Datum expirace zadosti
          format: date-time
          example: '2023-05-24T18:27:09.236Z'
        rstsPartyId:
          type: string
          description: BIC id
        contrNumBs:
          type: string
          description: cibis contract number
      description: Get Basic Detail Service response element.
    ApplVariant:
      type: object
      properties:
        avVrntKey:
          type: integer
          description: Application Variant Key / Instanční klíč varianty žádosti.
          format: int64
        avVrntTpId:
          type: string
          description: Variant type identification / Typ varianty žádosti (LOV APPL_VRNT_PT).
        avBusProdTpId:
          type: string
          description: Business product identification / Typ produktu na variantě (LOV BUS_PROD_TP).
        avBusProdSubTpId:
          type: string
          description: Business Product Sub Type Identifier / Typ podproduktu na variantě (LOV NUS_PROD_SUB_TP).
        avFinaAmt:
          type: number
          description: Total amount of loan or limit / Limit.
        avInstlAmt:
          type: number
          description: Installment amount / Výše splátky.
        avInstlCnt:
          type: integer
          description: Total number of installments / Počet splátek.
          format: int64
        avMaturityDate:
          type: string
          description: Last instalment date / datum poslední splátky.
          format: date
        avIntrsRx:
          type: number
          description: Interest rate contracted / Úroková sazba.
        avRpsn:
          type: number
          description: RPSN - Roční procentní sazba nákladů - Anual Percentage Rate of charge (RPSN) - contracted.
        avCamCode:
          type: string
          description: Campaign Code.
        avMinAvlblAmt:
          type: number
          description: Minimal available limit.
        avMinInstlAmt:
          type: number
          description: Minimal installment amount.
        avMinInstlCnt:
          type: integer
          description: Minimal number of installments.
          format: int64
        avMaxAvlblAmt:
          type: number
          description: Maximal available limit.
        avMaxInstlAmt:
          type: number
          description: Maximal installment amount.
        avMaxInstlCnt:
          type: integer
          description: Maximal number of installments.
          format: int64
        avUpsellAmt:
          type: number
          description: Up sell amount / Navýšení.
        avCcyId:
          type: string
          description: Currency ID / ID měny.
        avTotRpmtAmt:
          type: number
          description: Total Repayment Amount / Celová častka splatná spotřeb.
        avRefiSavedAmt:
          type: number
          description: Refi Saved Amount/ Částka kterou klient ušetřil.
        avLoanPurpId:
          type: string
          description: Loan Purpose ID / Účel úvěru. LOV LOAN_PURP
          example: GOODS
        avLoanPurpDecrId:
          type: string
          description: Loan Purpose Declared ID / Deklarovaný účel úvěru. LOV LOAN_PURP
          example: GOODS
        avActSumInstlAmt:
          type: number
          description: Aktuální výše splátek všech konsolidovaných úvěrů klienta. Výpočet Tauru.
        avConsLoanInstlAmt:
          type: number
          description: Nová výše splátky konsolidovaného úvěru. Výpočet Tauru.
        avPurpAmtOblgtn:
          type: number
          description: Účelová část půjčky - počítaná suma ze závazků.
        avUpsellAmtOblgtn:
          type: number
          description: Neúčelová část půjčky - počítaná suma ze závazků.
        avMaxUpsellAmtOblgtn:
          type: number
          description: Maximální možný nabízený upsell - počítaná suma ze závazků.
        avDayIntrsAmt:
          type: number
          description: Denní úrok.
        avRepIntrsAmt:
          type: number
          description: Reprezentativní úrok (příklad).
        avRep3mIntrsAmt:
          type: number
          description: Reprezentativní 3 měsíční úrok (příklad).
        avRep3mCostsAmt:
          type: number
          description: Reprezentativní 3 měsíční náklady (příklad).
        avRepDrawDate:
          type: string
          description: 'Representative Drawing Date / Reprezentativní příklad: datum čerpání (první den nadcházejícího kalendářního měsíce měsíce).'
          format: date
        avRepMaturityDate:
          type: string
          description: 'Representative Maturity Date / Reprezentativní příklad: datum splatnosti (datum čerpání + 3 měsíce (1. kalendářní den)).'
          format: date
        avApprovedFee:
          type: number
          description: Approved Fee / schválená výše poplatku za správu rozesplátkované platby.
        avSumEquaOblgtnAmout:
          type: number
          description: Sum EQUA Obligation Amout / Požadovaná suma EQUA závazků k refinancování.
        avSumNonEquaOblgtnAmout:
          type: number
          description: Sum Non EQUA Obligation Amout / Požadovaná suma závazků k refinancování od jiných subjektů.
        avOfrBusProdSubtpId:
          type: string
          description: Offer Business Product Subtype Identifier / Vyjadřuje, kterého subtypu produktu se daná OFR varianta týče.
        avIntrsDscntAlwFlag:
          type: boolean
          description: Interest Discount Allowed Flag / Povolena úprava úrokové sazby (snížení).
        avMaxUpsellAmt:
          type: number
          description: Maximum Upsell Amount / Horní hranice RISKem povoleného neúčelového čerpání.
        avAccNumPrefix:
          type: string
          description: Account Number Prefix / Předčíslí účtu pro čerpání/splácení.
        avAccNum:
          type: string
          description: Account Number / Číslo účtu pro čerpání/splácení.
        avBankCode:
          type: string
          description: Bank Code / Kód banky účtu pro čerpání/splácení.
        avMaxExtnOblgtnAmt:
          type: number
          description: Maximum external obligations amount / Maximální výše externích závazků.
        avMinIntrsRxForFinal:
          type: number
          description: Minimum Interest Rate For Finalization / Minimální výše finální úrokové sazby.
        avScgOfrIntrsRxOrig:
          type: number
          description: Scoring Offered Interest Rate Original / Odvozená sazba z SPSS.
        avPropId:
          type: string
          description: Proposal Identifier / Identifikátor předschválené nabídky.
        avDelFlag:
          type: boolean
          description: Variant Delete Flag.
          example: false
      description: Get Variants service request / Varianty žádosti (GetApplVrnt20o11 RS_1).
    GetVariantsServiceResponse:
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key / Klíč žádosti.
          format: int64
          example: *********
        applVariants:
          type: array
          description: Application Variant List / Varianty žádosti.
          items:
            $ref: '#/components/schemas/ApplVariant'
        applVariantParams:
          type: array
          description: Variant Parametr List / Parametry variant.
          items:
            $ref: '#/components/schemas/VariantParameter'
      description: Get Variants service response element.
    VariantParameter:
      type: object
      properties:
        applVrntParKey:
          type: integer
          description: Application Variant Parameter Key.
          format: int64
        applVrntKey:
          type: integer
          description: Variant Key.
          format: int64
        prodParDate:
          type: string
          description: Vztažné datum.
          format: date
        priceVrntId:
          type: string
          description: ID cenové varianty.
        purpId:
          type: string
          description: Účel úvěru.
        priceVrntPriority:
          type: number
          description: Priorita.
        regIntrsRxId:
          type: string
          description: Id běžné úrokové sazby.
        regIntrsRxBaseIndex:
          type: number
          description: Index báze běžné úrokové sazby.
        penaltyIntrsRxBaseIndex:
          type: number
          description: Index báze sankční úrokové sazby.
        regIntrsRx:
          type: number
          description: Hodnota báze běžné úrokové sazby.
        penaltyIntrsRx:
          type: number
          description: Hodnota báze sankční úrokové sazby.
        regIntrsRxDvg:
          type: number
          description: Odchylka běžné úrokové sazby.
        origRegIntrsRxDvg:
          type: number
          description: Odchylka běžné úrokové sazby - původní hodnota.
        intrsRxDiff:
          type: number
          description: Diferenční sazba.
        minRegIntrsRx:
          type: number
          description: minimální sazba běžného úroku.
        penaltyIntrsRxDvg:
          type: number
          description: Odchylka sankční úrokové sazby.
        minTerm:
          type: number
          description: Minimální délka.
        maxTerm:
          type: number
          description: Maximální délka.
        minAmt:
          type: number
          description: Min výše.
        maxAmt:
          type: number
          description: Max. výše.
        elbosParPtStat:
          type: string
          description: Statut osoby od.
        elbosParRiskClassSince:
          type: string
          description: Třída risk od.
        elbosParRiskClassTo:
          type: string
          description: Třída risk do.
        payInsurVrntId:
          type: string
          description: Varianta pojištění.
        payInsurVrntAmt:
          type: number
          description: Výše pojištění.
        loanAccFeeAmt:
          type: number
          description: Poplatek za vedení úvěrového účtu.
        pvdFeeAmt:
          type: number
          description: Poplatek za poskytnutí.
        electronicStmFeeAmt:
          type: number
          description: Poplatek za výpis elektronicky.
        paperStmFeeAmt:
          type: number
          description: Poplatek za výpis papírově.
        extordInstlFeeAmt:
          type: number
          description: Poplatek za mimořádnou splátku.
        minRefundRx:
          type: number
          description: Min Refund Rate / Minimalni splatka.
        maxRefundRx:
          type: number
          description: Max Refund Rate / Maximalni splatka.
        camId:
          type: string
          description: Campaign Identifier / ID kampane.
        camName:
          type: string
          description: Campaign Name / Nazev kampane.
        payInsurVrntDescr:
          type: string
          description: Pay Insurance Variant Description / Popis typu pojisteni.
        promoCode:
          type: string
          description: Promo Code/ Kód promo akce.
        minTermRefund:
          type: number
          description: Minimal Term Refund.
        maxTermRefund:
          type: number
          description: Maximal Term Refund.
        minAmtRefund:
          type: number
          description: Minimal Amount Refund.
        maxAmtRefund:
          type: number
          description: Maximal Amount Refund.
        minUpsellAmt:
          type: number
          description: Minimal Upsell Amount.
        maxUpsellAmt:
          type: number
          description: Maximal Upsell Amount.
      description: Get Variants Parametr service request / Parametry variant (GetApplVrnt20o12 RS_2).
    GetTelcoScoreResponse:
      required:
        - score
        - timestamp
      type: object
      properties:
        score:
          type: number
          description: Získane skóre 0-1000, nedohledaný klient má skóre 0.
        additionalInfo:
          type: string
          description: Doplňující informace.
        timestamp:
          type: string
          description: Timestamp záznamu.
          format: date-time
      description: Get Telco Score service response element.
    CtDocGet:
      type: object
      properties:
        instKey:
          type: number
          description: instanční klíč
        instKeyTp:
          type: string
          description: entita klíče
        docId:
          type: string
          description: ID dokumentu v DMS
        docTpId:
          type: string
          description: typ dokumentu (LV_DOC_TP)
        docSubTpId:
          type: string
          description: subtyp dokumentu (LV_DOC_SUB_TP)
        docStatId:
          type: string
          description: status dokumentu (LV_DOC_STAT)
    CtDocs:
      type: object
      properties:
        instKey:
          type: number
          description: instanční klíč
        instKeyTp:
          type: string
          enum:
            - PURP
            - INCVERIF
            - PARTY
            - PTVERIF
          description: entita klíče
        docs:
          type: array
          items:
            $ref: '#/components/schemas/CtDoc'
    CtDoc:
      type: object
      properties:
        docId:
          type: string
          description: ID dokumentu v DMS
        docTpId:
          type: string
          description: typ dokumentu (LV_DOC_TP)
        docSubTpId:
          type: string
          description: subtyp dokumentu (LV_DOC_SUB_TP)
        docStatId:
          type: string
          description: status dokumentu (LV_DOC_STAT)
        delFlag:
          type: boolean
          description: delete flag - odstraneni dokumentu
    CtGetOverdraftAppl:
      type: object
      properties:
        applTpId:
          type: string
          description: applTpId
        applStatId:
          type: string
          description: applStatId
        applDate:
          type: string
          description: applDate
          format: date-time
        posId:
          type: string
          description: posId
        firstTouchPoint:
          type: string
          description: firstTouchPoint
        applReason:
          type: string
          description: applReason
        hash:
          type: string
          description: hash
        preApprOfrId:
          type: string
          description: preApprOfrId
        opportunityId:
          type: string
          description: opportunityId
        ccyId:
          type: string
          description: ccyId
        distCnlId:
          type: string
          description: distCnlId
        advisorId:
          type: string
          description: advisorId
        fulfillmentCnlId:
          type: string
          description: fulfillmentCnlId
        wrkPlaceId:
          type: string
          description: wrkPlaceId
        busProdSubTp:
          type: string
          description: busProdSubTp
        browserInfo:
          type: string
          description: browserInfo
        telcoQueryAllowedFlag:
          type: boolean
          description: telcoQueryAllowedFlag
        authMobSearchFlag:
          type: boolean
          description: Authorization phone search flag
        firstTouchPointOwnr:
          type: string
          description: firstTouchPointOwnr
        promoCode:
          type: string
          description: promoCode
        accNumPrefix:
          type: string
          description: accNumPrefix
        accNum:
          type: string
          description: accNum
        applComplPosId:
          type: string
          description: applComplPosId
        contrNum:
          type: string
          description: contrNum
        contrSignDate:
          type: string
          description: contrSignDate
          format: date
        contrSignPosId:
          type: string
          description: contrSignPosId
        contrSignAdvisorId:
          type: string
          description: contrSignAdvisorId
        contrSignAdvisorName:
          type: string
          description: contrSignAdvisorName
        validFromDate:
          type: string
          description: validFromDate
          format: date-time
        validToDate:
          type: string
          description: validToDate
          format: date-time
        rejectRsnId:
          type: string
          description: rejectRsnId
        rejectRsn:
          type: string
          description: rejectRsn
        rejectRsnTp:
          type: string
          description: rejectRsnTp
        fiOperCode:
          type: string
          description: fiOperCode
        registryResult:
          type: string
          description: Risk band, LovRiskBand.
        finalRiskClass:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        incVerifPSD2Flag:
          type: boolean
          description: Indicate posibility of income verification by PSD2 channel / Indikuje možnost ověřování přijmu přes PSD2.
          example: false
        incVerifAccStmFlag:
          type: boolean
          description: Indicate posibility of income verification by the account statement / Indikuje možnost ověřování přijmu pomocí výpisu z účtu.
          example: false
        incVerifStmUploadFlag:
          type: boolean
          description: Indicate posibility of income verification by upload of account statement / Indikuje možnost ověřování přijmu přímým nahráním výpisu z účtu.
          example: false
        incVerifCallFlag:
          type: boolean
          description: Indicate posibility of income verification by phone call to employer / Indikuje možnost ověřování přijmu pomocí telefonického ověření u zaměstnavatele.
          example: true
        cmt:
          type: string
          description: comment / komentar
      description: Overdraft application data. Overdraft application complex type.
    CtPersonGet:
      type: object
      properties:
        siebelId:
          type: string
          description: Siebel identifier.
          example: "********"
        genderId:
          type: string
          description: "Gender, LovGender."
          example: M
        eduStatId:
          type: string
          description: "Education status, LovEduStat."
          example: G
        housingStatId:
          type: string
          description: "Housing status, LovHousingStat."
          example: SHARE
        citizenship:
          type: string
          description: "Country, LovCntry."
          example: CZ
        marStatId:
          type: string
          description: "Marital status, LovMarStat."
          example: S
        rcNum:
          type: string
          description: Birth number. Social insurance number of a person.
          example: 801130/8943
        salutId:
          type: string
          description: Salutation for personalized communication with a client. LovSalut.
          example: MR
        titleBefore:
          type: string
          description: Title before name.
          example: Ing.
        firstName:
          type: string
          description: First name of a person.
          example: Petr
        familyName:
          type: string
          description: Last name of a person.
          example: Novák
        birthPlace:
          type: string
          description: Birth place of a person.
          example: Kyjov
        birthName:
          type: string
          description: Birth name of a person.
          example: Novák
        birthDate:
          type: string
          description: "\tBirth day of a person."
          format: date
          example: 2022-12-16
        birthCntryId:
          type: string
          description: "Birth country, LovCntry."
          example: CZ
        taxDomicile:
          type: string
          description: "Tax domicile country, Tax Country Identifier/Země danového\
            \ rezidenství. LovCntry"
          example: AT
        tin:
          type: string
          description: Taxpayer Identification Number/Zahraniční DIC.
          example: "15349308"
        pep:
          type: boolean
          description: Politically Exposed Person.
          example: false
        mortExpSum:
          type: number
          description: Mortgage Expense Sum / Splátky hypotek.
          example: 10000
        householdExpSum:
          type: number
          description: Household Expense Sum / Měsíční výdaje na bydlení a domácnost.
          example: 15000
        othExpSum:
          type: number
          description: Other Expense Sum / Ostatní výdaje.
          example: 5000
        mntdChildCnt:
          type: integer
          format: int64
          description: Number of maintained children / Počet vyživovaných dětí
          example: 1
        crntAddrValidDate:
          type: string
          description: Current Address since / Soucasna adresa od.
          format: date
          example: 1989-12-16
        email:
          type: string
          description: Email Identifier.
          example: <EMAIL>
        phoneNum:
          type: string
          description: Phone Number.
          example: "420412440000"
        permAddr:
          $ref: '#/components/schemas/CtAddress'
        postalAddr:
          $ref: '#/components/schemas/CtAddress'
        idCards:
          type: array
          description: List of ID cards.
          items:
            $ref: '#/components/schemas/CtIdCard'
        adbInstPtKey:
          type: integer
          description: ADB instance party key.
          format: int64
          example: 202212210014
        ptTpId:
          type: string
          description: "Party type, LovPtTp."
          example: FO
        cbId:
          type: string
          description: Credit buro identifier.
          example: ABC123
        verifications:
          type: array
          description: CtPersonVerification list. Data of person verification.
          items:
            $ref: '#/components/schemas/CtPersonVerification'
        incomeVerifications:
          type: array
          description: CtPersonIncomeVerification list. Person data of verification
            incoms.
          items:
            $ref: '#/components/schemas/CtPersonIncomeVerification'
        ptCorpDetail:
          $ref: '#/components/schemas/CtPtCorpDetail'
        busName:
          type: string
          description: Business name.
          example: "Raiffeisenbank, a.s."
      description: CtPersonGet. Personal data for get request.
    CtSignChannelGet:
      type: object
      properties:
        applVariantSignCnlKey:
          type: integer
          description: Application variant sign channel key.
          format: int64
          example: ************
        signCnlId:
          type: string
          description: Application variant sign channel ID / Místo/kanál podpisu návrhu žádosti. Signing channel, LovCnl.
          example: WEB
        applVariantSignCnlDelFlag:
          type: boolean
          description: Application variant channel sign delete flag / Priznak odmazani zaznamu.
          example: false
        signPosId:
          type: string
          description: Signing point of sales ID, LovPos.
          example: RBPHPKCB
        applVariantKey:
          type: integer
          description: Application variant key.
          format: int64
          example: *************
      description: CtSignChannelGet extends CtSignChannel. Get sign channel complex type.
    CtVariantParameterGet:
      type: object
      properties:
        applVariantParKey:
          type: integer
          description: Application Variant Parameter Key.
          format: int64
          example: 12504291000821
        prodParDate:
          type: string
          description: Date / Vztažné datum.
          format: date
          example: '2022-12-16'
        priceVariantId:
          type: string
          description: Price Variant ID / ID cenové varianty.
          example: '566'
        purpId:
          type: string
          description: Purpose ID  / Účel úvěru.
          example: '80'
        priceVariantPriority:
          type: integer
          description: Price Variant Priority / Priorita.
          format: int64
          example: 36
        regIntrsRxId:
          type: string
          description: Regular Interest ID / Id běžné úrokové sazby.
          example: '3271'
        regIntrsRxBaseIndex:
          type: number
          description: Regular Interest Base ID / Index báze běžné úrokové sazby.
          example: 0
        penaltyIntrsRxBaseIndex:
          type: number
          description: Penalty Interest Base ID / Index báze sankční úrokové sazby.
          example: 2026
        regIntrsRx:
          type: number
          description: Regular Interest Base / Hodnota báze běžné úrokové sazby.
          example: 0
        penaltyIntrsRx:
          type: number
          description: Penalty Interest Base / Hodnota báze sankční úrokové sazby.
          example: 10
        regIntrsRxDvg:
          type: number
          description: Regular Interest Rate Value / Odchylka běžné úrokové sazby.
          example: 11.8
        origRegInstrsRxDvg:
          type: number
          description: Original Interest Rate Value / Odchylka běžné úrokové sazby - původní hodnota.
          example: 11.8
        intrsRxDiff:
          type: number
          description: Difference Rate / Diferenční sazba.
          example: 0
        minRegIntrsRx:
          type: number
          description: Min Regular Interest Rate / Minimální sazba běžného úroku.
          example: 0
        penaltyIntrsRxDvg:
          type: number
          description: Penalty Interest Rate Value / Odchylka sankční úrokové sazby.
          example: 0
        minTerm:
          type: integer
          description: Min Duration / Minimální délka.
          format: int64
          example: 55
        maxTerm:
          type: integer
          description: Max Duration / Maximální délka.
          format: int64
          example: 70
        minAmt:
          type: number
          description: Min Value / Min výše.
          example: 99000
        maxAmt:
          type: number
          description: Max Value / Max. výše.
          example: 200000
        elbosParPtStat:
          type: string
          description: Min Statute / Statut osoby od.
        elbosParRiskClassSince:
          type: string
          description: Min Risk Class / Třída risk oo.
        elbosParRiskClassTo:
          type: string
          description: Max Risk Class / Třída risk do.
        payInsurVariantId:
          type: string
          description: Insurance Variant ID / Varianta pojištění. Insurance type, LovInsurTp.
          example: '1'
        payInsurVariantAmt:
          type: number
          description: Insurance Amount / Výše pojištění.
          example: 500
        loanAccFeeAmt:
          type: number
          description: Fee Administration Account / Poplatek za vedení úvěrového účtu.
          example: 20
        provideFeeAmt:
          type: number
          description: Fee Provide Credit Account / Poplatek za poskytnutí.
          example: 100
        electronicStmFeeAmt:
          type: number
          description: Fee Electronic Statement / Poplatek za výpis elektronicky.
          example: 0
        paperStmFeeAmt:
          type: number
          description: Fee Paper Statement / Poplatek za výpis papírově.
          example: 50
        extordInstlFeeAmt:
          type: number
          description: Fee Extra Repayment / Poplatek za mimořádnou splátku.
          example: 1000
        minRefundRx:
          type: number
          description: Min Refund Rate / minimalni splatka.
          example: 333
        maxRefundRx:
          type: number
          description: Max Refund Rate / maximalni splatka.
          example: 33000
        camId:
          type: string
          description: Campaign Identifier / ID kampane.
        camName:
          type: string
          description: Campaign Name / Nazev kampane.
        payInsurVariantDescr:
          type: string
          description: Pay Insur Variant Description.
        promoCode:
          type: string
          description: Promo code.
        minTermRefund:
          type: integer
          description: Minimal Term Refund.
          format: int32
        maxTermRefund:
          type: integer
          description: Maximal Term Refund.
          format: int32
        minAmtRefund:
          type: number
          description: Minimal Amount Refund.
        maxAmtRefund:
          type: number
          description: Maximal Amount Refund.
        minUpsellAmt:
          type: number
          description: Minimal Upsell Amount.
        maxUpsellAmt:
          type: number
          description: Maximal Upsell Amount.
        applVariantParDelFlag:
          type: boolean
          description: Delete Flag.
          example: false
        applVariantKey:
          type: integer
          description: Application variant key.
          format: int64
          example: *************
      description: CtVariantParameterGet extends CtVariantParameter. Get application variant parameters complex type.
    GetOverdraftApplResponse:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        primaryOwner:
          $ref: '#/components/schemas/CtPersonGet'
        incomes:
          type: array
          description: incomes
          items:
            $ref: '#/components/schemas/CtPtIncome'
        overdraftAppl:
          $ref: '#/components/schemas/CtGetOverdraftAppl'
        applVariants:
          type: array
          description: applVariants
          items:
            $ref: '#/components/schemas/CtApplVariant'
        applVariantParams:
          type: array
          description: applVariantParams
          items:
            $ref: '#/components/schemas/CtVariantParameterGet'
        signChannels:
          type: array
          description: signChannels
          items:
            $ref: '#/components/schemas/CtSignChannelGet'
        applMetadata:
          type: array
          description: applMetadata
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
      description: Get Overdraft Appl service response.
    CtGetMortAppl:
      type: object
      description: Mortgage (sub)application data.
      allOf:
        - $ref: '#/components/schemas/CtMortAppl'
        - type: object
          properties:
            persons:
              type: array
              description: Seznam žadatelů/spolužadatelů
              items:
                $ref: '#/components/schemas/CtGetMortApplPers'
            chieldAppls:
              type: array
              description: Seznam podžádostí (pouze u hlavní žádosti)
              items:
                $ref: '#/components/schemas/CtGetMortApplChild'
            households:
              type: array
              description: Seznam domácností (pouze u podžádostí)
              items:
                $ref: '#/components/schemas/CtHouseholdGet'
            applVariants:
              type: array
              description: Seznam variant (pouze u podžádostí)
              items:
                $ref: '#/components/schemas/CtMortApplVariantGet'
            applMetadata:
              type: array
              description: Seznam metadat žádosti
              items:
                $ref: '#/components/schemas/CtApplMetadataEntry'
            incomes:
              type: array
              description: Seznam přijímů (pouze u podžádostí)
              items:
                $ref: '#/components/schemas/CtPtIncomeMort'
            applObligations:
              type: array
              description: CtObligation list. Application obligations complex type.
              items:
                $ref: '#/components/schemas/CtObligationMortGet'
            applDecisions:
              type: array
              description: CtApplDecisions list. Application decisions complex type.
              items:
                $ref: '#/components/schemas/CtApplDecisionsMortGet'
            pledges:
              type: array
              description: "Application Pledges complex type."
              items:
                $ref: '#/components/schemas/CtApplPledgesMortGet'
            realtyInsurances:
              type: array
              description: "Realty Insurances decisions complex type."
              items:
                $ref: '#/components/schemas/CtApplRealtyInsurancesMortGet'
            loanSubjects:
              type: array
              description: "Loan subjects complex type."
              items:
                $ref: '#/components/schemas/CtLoanSubjectsMortGet'

    CtGetMortApplChild:
      type: object
      description: ID a stav podžádosti.
      properties:
        chieldApplKey:
          type: integer
          format: int64
          description: Chield Application Key / Klíč podžádosti
          example: 2214874003641
        chieldApplBusApplId:
          type: string
          description: Chield Application Business Application Identifier / Businessový identifikátor podžádosti
        chieldApplStatId:
          type: string
          description: Chield Application Status Identifier / Stav podžádosti (LOV APPL_APPL_STAT)
        chieldApplPrimFlag:
          type: boolean
          description: 'Chield Application Primary Flag / Žádost v daném vztahu je na primární straně'
          example: false
        chieldApplApplRelTpId:
          type: string
          description: 'Chield Application Application Relationship Identifier / Typ vztahu mezi žádostmi'
          example: '202212210014'
    CtGetMortApplPers:
      description: ID a role osoby.
      allOf:
        - $ref: '#/components/schemas/CtMortApplPers'
        - type: object
          properties:
            extnRejRsnTpDescr:
              type: string
              description: Externí důvody zamítnutí
            rejectRsnTpIds:
              type: string
              description: Seznam typů rozhodnutí (LOV RSN_TP) oddělených čárkou
            scgExpsSum:
              type: number
              description: Scoring Exposure Sum / Celková úvěrová angažovanost klienta (spotřebáky + hypotéky + OVD(limit) + KK(limit) a to jak interní tak i externí)
              example: 100000
            scgMortInstlSum:
              type: number
              description: Scoring Mortgage Instalment Sum / Splátky hypoték interních i externích (hlavní žadatel se počítá 100%, spolužadatel 50%)
              example: 100000
            scgInstlSum:
              type: number
              description: Sum of installments / Celkova vyse splatek
              example: 100000
            scgFinalRiskClassId:
              type: string
              description: Final applicant risk class / Vysledna risk trida (LOV RISK_CLASS)
            scgAppctHighestIncFlag:
              type: boolean
              description: CG Applicant with the highest Income Flag / Žadatel s nejvyšším příjmem.
            regRiskClassId:
              type: string
              description: Risk Class Identifier / Identifikator Risk tridy
            regRiskBandId:
              type: string
              description: Risk Band Identifier / Identifikator Risk skupiny (LOV RISK_BAND)
            apprProcessPartId:
              type: string
              description: Approval Process Part Identifier / Fáze procesu schvalování (LOV APPR_PROCESS_PART)
            dsnTpId:
              type: string
              description: Decision Type Identifier / Identifikator typu rozhodnuti (LOV DSN_TP)
    CtHouseholdGet:
      description: Household details.
      allOf:
        - $ref: '#/components/schemas/CtHousehold'
        - type: object
          properties:
            instPtKeys:
              type: array
              description: Instance Party Key / Pole instančních klíčů členů domácnosti
              items:
                type: integer
                format: int64
                example: 202212210014
    CtHousehold:
      type: object
      description: Household details.
      properties:
        houshKey:
          type: integer
          format: int64
          description: Household key.
          example: *********
        houshOrd:
          type: integer
          description: Household Order / Pořadí domácnosti (unikátní identifikátor v rámci žádosti)
          example: 1
        mntdChildCnt:
          type: integer
          description: Number of maintained children / Počet vyživovaných dětí
          example: 1
        appctsCnt:
          type: integer
          description: Number of applicants / Počet žadatelů v domácnosti
          example: 2
        appctsAge:
          type: integer
          description: Applicants age / Referenční věk žadatelů c domácnosti pro výpočet LTV
          example: 50
        netIncSumDecr:
          type: number
          description: Net Monthly Income Sum Declared / Celkový deklarovaný čistý měsíční příjem žadatelů v domácnosti
          example: 100000
        expSumDecr:
          type: number
          description: Monthly Expenses Sum Declared / Celkové deklarované měsíční výdaje žadatelů v domácnosti
          example: 100000
        expSumEstm:
          type: number
          description: Monthly Expenses Sum Estimated / Celkové odhadované měsíční výdaje žadatelů v domácnosti
          example: 100000
        instlLoanSumDecr:
          type: number
          description: Monthly Loan Instalments Sum Declared / Celková deklarovaná výše měsíčních splátek všech úvěrů domácnosti
          example: 5000
        livingExpSumDecr:
          type: number
          description: Living Expenses Sum Declared / Celkové deklarované nezbytné životní výdaje (bydlení - energie+voda, životní výdaje, zdravotní péče)
          example: 5000
        housExpSumDecr:
          type: number
          description: Housing Expenses Sum Declared / Celkové deklarované výdaje na bydlení (energie, voda)
          example: 5000
        rntlExpSumDecr:
          type: number
          description: Rental Expenses Sum Declared / Celkové deklarované výdaje za nájemné - pouze pokud je bude platit i nadále
          example: 5000
        alimonyExpSumDecr:
          type: number
          description: Alimony Expenses Sum Declared / Celkové deklarované výdaje na výživné
          example: 5000
    CtMortApplVariantComment:
      type: object
      description: Komentář risku určený poradcům
      properties:
        cmtApplVrntKey:
          type: integer
          format: int64
          description: Comment Application Variant Key
          example: *********
        cmtOrd:
          type: integer
          description: Comment Order / Pořadí poznámky (kvůli zobrazení)
          example: 1
        cmt:
          type: string
          description: Comment / Komentář, poznámka, vzkaz poradci.
        cmtInstPtKey:
          type: integer
          description: Comment Instance Party Key / Doporučení se týká žadatele
    CtMortApplVariantGet:
      allOf:
        - $ref: '#/components/schemas/CtMortApplVariant'
        - type: object
          properties:
            applVariantComments:
              type: array
              description: Komentáře risku určené poradcům
              items:
                $ref: '#/components/schemas/CtMortApplVariantComment'
            hycProdTpId:
              type: string
              description: Hypo Client product type id
            hycProdTpName:
              type: string
              description: Hypo Client product type name
            busProdSubTpDesignName:
              type: string
              description: Hypo Client product subtype name
            intrsRxFixDate:
              type: string
              description: "Interest Rate Fixation Date / Datum fixace (úrokové sazby)"
              format: date
              example: 2022-12-16
    CtObligationMort:
      type: object
      properties:
        instOblgtnKey:
          type: integer
          description: Instance obligation key.
          format: int64
        adbInstPtKey:
          type: integer
          description: ADB instance party key.
          format: int64
        ofFirstInstlDate:
          type: string
          description: Office date of first instalment / Datum první splátky konsolidovaného
            závazku.
          format: date
          example: 2022-12-16
        ofFinInstnId:
          type: string
          description: "Office financial institution identifier / Finační instituce,\
            \ LovFinInstn."
          example: CITIBNK
        ofContrNum:
          type: string
          description: Office contract number of obligation / Číslo smlouvy konsolidovaného
            úvěru.
        ofTotLoanAmt:
          type: number
          description: Office total amount of cosolidated contract / Výše schváleného
            úvěru závazku.
        scTotLoanAmt:
          type: number
          description: "Scoring total amount of cosolidated contract / Výše schvá\
            leného úvěru závazku, puvodni uverovy limit zjisteny ve scoringu."
        ofTotInstlCnt:
          type: integer
          description: Office total count of instalment / Celkový počet splátek závazku.
          format: int32
        ofIntrsRx:
          type: number
          description: Office interest rate / Urok.
        scIntrsRx:
          type: number
          description: Scoring interest rate / Urok zjisteny ve scoringu.
        ofInstl:
          type: number
          description: Office instalment / Splátka.
        scInstl:
          type: number
          description: Scoring instalment / Vyska splatky zjistena ve scoringu.
        ofOblgtnProdTpId:
          type: string
          description: "Office obligation product type ID / ucel zavazku, zadano do\
            \ P4, LovOblgtnProdTp."
          example: RCL
        scOblgtnProdTpId:
          type: string
          description: "Scoring obligation product type ID / Ucel zavazku, zjisteny\
            \ ve scoringu, LovOblgtnProdTp."
          example: ROD
        ofMaturityDate:
          type: string
          description: Office origin product maturity date.
          format: date
          example: 2022-12-16
        ofAmt:
          type: number
          description: Office origin obligation amount.
        scAmt:
          type: number
          description: "Scoring origin obligation amount / Zbývající jistina/zůstatek\
            \ pro splátkové, limit pro KTK a KK zjisteny ve scoringu. Hlavní údaj\
            \ pro finalizaci u konsolidace."
        finInstnGrpCode:
          type: string
          description: Identifikace fin. instituce pro seřazení závazků dle poskytovatele.
        equaContrNum:
          type: string
          description: Identifikace Equa produktu pro interní refinancování.
        ofrIntrsRx:
          type: number
          description: Definuje na úrovni závazku nabízenou sazbu pro REFI/CON.
        origScOfrIntrsRx:
          type: number
          description: Scoring Offer Interest Rate Original / původní úroková sazba
            nastavená RISKem.
        origContractRequired:
          type: boolean
          description: Definuje na úrovni závazku požadavek na doložení původní smlouvy
            pro REFI/CONS.
          example: false
        scPrimaryOblgtn:
          type: boolean
          description: Definuje na úrovni závazku kandidáty na primární závazek pro
            REFI/CONS.
          example: false
        ofAccNum:
          type: string
          description: Previous bank account number.
        ofAccNumPrefix:
          type: string
          description: Office account number prefix / předčíslí účtu.
        ccyId:
          type: string
          description: "Currency / měna, LovCcy."
          example: CZK
        ofCrntAccFeeAmt:
          type: number
          description: Office current account fee amount / Poplatek za BU.
        ofContrFeeAmt:
          type: number
          description: Office contract fee amount/ Poplatek za vedení produktu.
        ofPrinc:
          type: number
          description: Office principal / Jistina.
        ofInstlCnt:
          type: integer
          description: Office instalment count / Zbývající počet splátek.
          format: int32
        ofOblgtnLoanPurpId:
          type: string
          description: "Obligation loan purpose / Účel původního úvěru, LovOblgtnLoanPurp."
          example: CAR
        ofBankId:
          type: string
          description: Office bank / Původní bankovní dům.
        ofVarSymbol:
          type: string
          description: Office variable symbol / Variabilní symbol.
        ofConstSymbol:
          type: string
          description: Office constant symbol / Konstantní symbol.
        ofSpecSymbol:
          type: string
          description: Office specific symbol / Specifický symbol.
        ofOblgtnDocTpId:
          type: string
          description: "Obligation document type ID, LovOblgtnDocTp."
          example: geAll
        ofComment:
          type: string
          description: Comment.
        scCcbContractId:
          type: string
          description: CCB ID úvěru zjistene ve scoringu.
        clFinInstnId:
          type: string
          description: "Financial institution / Id banky/instituce, která poskytla\
            \ původní úvěr, zadané klientem na první kalkulačce, LovFinInstn."
          example: OTHER
        scFinInstnCode:
          type: string
          description: "Kód banky z registrů, slouží k poznání úvěrů ze stejné banky\
            \ a rozlišení banka/nebanka."
        clOrder:
          type: integer
          description: "Poradi zavazku tak jak jej zadal klient, clOrder=1 znamena\
            \ primarni zavazek."
          format: int32
          example: 1
        scOrder:
          type: integer
          description: Poradi zavazku tak jak jej ulozil scoring.
          format: int32
        oblgtnSelected:
          type: boolean
          description: "Client selected flag / Příznak, zda si klient ve finalizaci\
            \ vybral závazek ke konsolidaci."
          example: false
        scIsApplicable:
          type: boolean
          description: Priznak ze je zavazek vhodny k refinancovani.
          example: false
        scIsMandatory:
          type: boolean
          description: Priznak ze je zavazek povinny k refinancovani.
          example: false
        scIntContrNum:
          type: string
          description: Internal product / contract number.
        scIntSourceSystem:
          type: string
          description: Internal product / source system.
        scIntSourceSystemId:
          type: string
          description: Internal product / source system ID.
        primaryOblgtn:
          type: boolean
          description: "Priznak primarniho zavazku - 1/true, nebo 0/false/nevyplneno."
          example: false
        finRepayedAmt:
          type: number
          description: Částka stanovená elbosem pro splacení závazku.
        totRepaymentFlag:
          type: boolean
          description: Uplne splaceni.
          example: false
        oblgtnAmtAfterRepayment:
          type: number
          description: Zbývající pohledávka za klientem.
        clOblgtnProdTpId:
          type: string
          description: Client Obligation Product type ID / Typ původního úvěrovaného produktu uvedený klientem (LOV OBLGTN_PROD_TP)
        clTotLoanAmt:
          type: number
          description: Client Total amount of cosolidated contract/ Výše schváleného úvěru závazku
        clAmt:
          type: number
          description: Client Origin obligation amount / Výše závazku
        clInstl:
          type: number
          description: Client Instalment / Splátka
        clRefiMortFlag:
          type: boolean
          description: Client selected refinance obligation from mortgage flag/ Příznak že si klient vybral splacení závazku z hypotéky
          example: false
        scCalcInclFlag:
          type: boolean
          description: Scoring Calculated Included Flag / Závazek je u příslušné osoby započten do výpočtu max limitu dle DSTI, Dispo nebo DTI (0 - není započten, 1 - je započten)
          example: false
        scRcmdFlag:
          type: boolean
          description: Scoring Recommended Flag / Doporučený závazek
          example: false
      description: CtObligation. Application obligations complex type
    CtObligationMortGet:
      allOf:
        - $ref: '#/components/schemas/CtObligationMort'
        - type: object
          properties:
            regSrcFlag:
              type: boolean
              description: Register Source Flag /Zdroj informací je z registru (BRKI)
              example: false
            extnRegProdTp:
              type: string
              description: External Register Product Type Identifier / Kód produktu v registru BRKI
              example: "abcd"
    CtPersonMortGet:
      allOf:
        - $ref: '#/components/schemas/CtPersonMort'
        - type: object
          properties:
            euFlag:
              type: boolean
              description: Citizenship EU Flag / Země občanství je v EU
              example: false
            rsdntFlag:
              type: boolean
              description: Resident / Rezident
              example: false
            icoNum:
              type: string
              description: "ICO of a person or a Employer."
              example: "********"
            stayTpId:
              type: string
              description: "Stay Type ID / Druh pobytu "
              example: "********"
            documents:
              type: array
              description: "Documents."
              items:
                $ref: '#/components/schemas/CtPersonMortDocGet'
            groupDocuments:
              type: array
              description: "Group documents."
              items:
                $ref: '#/components/schemas/CtPersonMortGroupDocGet'
    CtPersonMortDoc:
      type: object
      properties:
        docTpId:
          type: string
          example: ""
          description: "Document Type Identifier / Typ dokumentu (LOV DOC_TP)"
        docId:
          type: string
          example: ""
          description: "Document Identification / Identifikátor dokumentu (v úložišti dokumentů)"
        docStatId:
          type: string
          example: ""
          description: "Document status (LOV DOC_STAT)"
    CtPersonMortDocGet:
      type: object
      properties:
        docTpId:
          type: string
          example: ""
          description: "Document Type Identifier / Typ dokumentu (LOV DOC_TP)"
        docId:
          type: string
          example: ""
          description: "Document Identification / Identifikátor dokumentu (v úložišti dokumentů)"
        docStatId:
          type: string
          example: ""
          description: "Document status (LOV DOC_STAT)"
        instKey:
          type: integer
          format: int64
          example: "123456789"
          description: "Instance Key / Instanční klíč entity dle typu vazby dokumentu."
        docBindId:
          type: string
          example: "54fgd54fd6"
          description: "Document Bind Identifier / Vazba dokumentu k dané entitě (LOV DOC_BIND)."
    CtPersonMortGroupDocGet:
      allOf:
        - $ref: '#/components/schemas/CtPersonMortGroupDoc'
        - type: object
          properties:
            grpDocTpRelation:
              type: array
              description: "Group Documents Type Relations."
              items:
                $ref: '#/components/schemas/CtPersonMortGroupDocRel'
    CtPersonMortGroupDocRel:
      type: object
      properties:
        grpDocTpId:
          type: string
          example: ""
          description: ""
        grpDocStayTpId:
          type: string
          example: ""
          description: ""
        grpDocTpFirstDocFlag:
          type: boolean
          example: true
          description: ""
        grpDocTpScndDocReqdFlag:
          type: boolean
          example: true
          description: ""
        grpDocTpDocdId:
          type: string
          description: ""
    CtPersonMortGroupDoc:
      type: object
      properties:
        grpDocId:
          type: string
          description: "Document Identification / Identifikátor dokumentu (v úložišti dokumentů)"
        grpDocStatId:
          type: string
          description: "Document status (LOV DOC_STAT)"
    CtPtIncomeRsts:
      type: object
      properties:
        instPtIncKey:
          type: integer
          description: Instance Party Income Key
          format: int64
          example: *********
        adbInstPtKey:
          type: integer
          description: ADB Instance party key.
          format: int64
          example: *********
        occCatgId:
          type: string
          description: "Occupation Category Identifier/ Druh povolání/živnosti, LovOccCatg."
          example: W
        naceCodeId:
          type: string
          description: "Obor podnikani zamestnavatele"
        emptTpId:
          type: string
          description: "Employment Type Identifier/ typ zaměstnání - na dobu určitou/neurč\
            itou, LovEmptTp."
          example: PRMNT
        businessTpId:
          type: string
          description: "Business type Identifier/ typ podnikani - fop/majitel sro"
        incTpId:
          type: string
          description: "Income Type Identifier / Zdroj příjmu, LovIncTp."
          example: EMPLT
        inc:
          type: number
          description: Income Amount / Výše příjmů přepočítaná na CZK
          example: 33000
        icoNum:
          type: string
          description: ICO of a person or a Employer.
          example: "********"
        dicNum:
          type: string
          description: DIC of a person or a Employer.
          example: CZ********
        busName:
          type: string
          description: Business name of Employer / Zaměstnavatel.
          example: "Raiffeisenbank, a.s."
        empSince:
          type: string
          description: Party is employee since this date / Zaměstnán od.
          format: date
          example: 1992-12-16
        empContrEndDate:
          type: string
          description: Date when employer contract finished / Datum ukončení pracovní
            činnosti.
          format: date
          example: 2022-12-16
        dismissalFlag:
          type: boolean
          description: Indicate if the employer contract will be ended / zaměstnání/smlouva
            bude ukončena ke dni.
          example: false
        riskBusBranchFlag:
          type: boolean
          description: Risk Business Branch Flag / Rizikový obor podnikání.
          example: false
        ccyId:
          type: string
          description: Currency Identifier / Měna, ve které je přijímán daný příjem (LOV CCY)
        exchangeRate:
          type: number
          description: exchange rate czk to ccyId
        invVerifSrcId:
          type: string
          description: Income Verification Source Identifier / Jakým způsobem hodlá žadatel doložit zdroj příjmu (LOV INC_VERIF_SRC)
        futureIncFlag:
          type: boolean
          description: Future Income Flag / Budoucí příjem
        taxExpTpId:
          type: string
          description: "Rate Tax Expense Type Identifier / Typ uplatněných výdajů"
        realExpAmt:
          type: number
          description: "Real Expenses Amount / Reálné výdaje."
          example: 123456
        decrDprAmt:
          type: number
          description: "Declaration Depreciation Amount / Deklarované odpisy spojené s příjmem z pronájmu (v CZK)"
          example: 123456
        instPtIncVerifKey:
          type: integer
          description: Instance Party Income Verification Key
        incVerifSrcId:
          type: string
          description: Income Verification Source Identifier (LOV INC_VERIF_SRC)
        incVerifRsltId:
          type: string
          description: Income Verification Result Identifier (LOV INC_VERIF_RSLT)
        delvCnlId:
          type: string
          description: Delivery Channel Identifier / Kanál pro doručení dokladů pro ověření příjmu (LOV CNL)
        netIncVerif:
          type: number
          description: Net Income Verified / Ověřená výše příjmu
        netIncFinal:
          type: number
          description: Net Income Final / Finální čistý měsíční příjem
        verifTime:
          type: string
          description: Verification Time.
          format: date-time
        scgSectorCatgId:
          type: string
          description: Scoring Sector Category Identifier / Kategorizovaný sektor pro hypoteční scoringový model (LOV SECTOR_CATG)
        publSectorActFlag:
          type: boolean
          description: Public Sector Activity Flag / Příznak činnosti ve veřejném sektoru
      description: Party incomes.
    CtPtIncomeMort:
      type: object
      properties:
        instPtIncKey:
          type: integer
          description: Instance Party Income Key
          format: int64
          example: *********
        adbInstPtKey:
          type: integer
          description: ADB Instance party key.
          format: int64
          example: *********
        occCatgId:
          type: string
          description: "Occupation Category Identifier/ Druh povolání/živnosti, LovOccCatg."
          example: W
        emptTpId:
          type: string
          description: "Employment Type Identifier/ typ zaměstnání - na dobu určitou/neurč\
            itou, LovEmptTp."
          example: PRMNT
        incTpId:
          type: string
          description: "Income Type Identifier / Zdroj příjmu, LovIncTp."
          example: EMPLT
        inc:
          type: number
          description: Income Amount / Výše příjmů přepočítaná na CZK
          example: 33000
        icoNum:
          type: string
          description: ICO of a person or a Employer.
          example: "********"
        dicNum:
          type: string
          description: DIC of a person or a Employer.
          example: CZ********
        busName:
          type: string
          description: Business name of Employer / Zaměstnavatel.
          example: "Raiffeisenbank, a.s."
        empSince:
          type: string
          description: Party is employee since this date / Zaměstnán od.
          format: date
          example: 1992-12-16
        empContrEndDate:
          type: string
          description: Date when employer contract finished / Datum ukončení pracovní
            činnosti.
          format: date
          example: 2022-12-16
        dismissalFlag:
          type: boolean
          description: Indicate if the employer contract will be ended / zaměstnání/smlouva
            bude ukončena ke dni.
          example: false
        riskBusBranchFlag:
          type: boolean
          description: Risk Business Branch Flag / Rizikový obor podnikání.
          example: false
        ccyId:
          type: string
          description: Currency Identifier / Měna, ve které je přijímán daný příjem (LOV CCY)
        invVerifSrcId:
          type: string
          description: Income Verification Source Identifier / Jakým způsobem hodlá žadatel doložit zdroj příjmu (LOV INC_VERIF_SRC)
        futureIncFlag:
          type: boolean
          description: Future Income Flag / Budoucí příjem
        taxExpTpId:
          type: string
          description: "Rate Tax Expense Type Identifier / Typ uplatněných výdajů"
        realExpAmt:
          type: number
          description: "Real Expenses Amount / Reálné výdaje."
          example: 123456
        decrDprAmt:
          type: number
          description: "Declaration Depreciation Amount / Deklarované odpisy spojené s příjmem z pronájmu (v CZK)"
          example: 123456
        instPtIncVerifKey:
          type: integer
          description: Instance Party Income Verification Key
        incVerifSrcId:
          type: string
          description: Income Verification Source Identifier (LOV INC_VERIF_SRC)
        incVerifRsltId:
          type: string
          description: Income Verification Result Identifier (LOV INC_VERIF_RSLT)
        delvCnlId:
          type: string
          description: Delivery Channel Identifier / Kanál pro doručení dokladů pro ověření příjmu (LOV CNL)
        netIncVerif:
          type: number
          description: Net Income Verified / Ověřená výše příjmu
        netIncFinal:
          type: number
          description: Net Income Final / Finální čistý měsíční příjem
        verifTime:
          type: string
          description: Verification Time.
          format: date-time
        scgSectorCatgId:
          type: string
          description: Scoring Sector Category Identifier / Kategorizovaný sektor pro hypoteční scoringový model (LOV SECTOR_CATG)
        publSectorActFlag:
          type: boolean
          description: Public Sector Activity Flag / Příznak činnosti ve veřejném sektoru
        ptIncVerifDocs:
          type: array
          description: Verification documents
          items:
            $ref: '#/components/schemas/CtPtIncVerifDocs'
      description: Party incomes.
    CtPtIncVerifDocs:
      type: object
      properties:
        docTpId:
          type: string
          description: "Document Type Identifier / Typ dokumentu (LOV DOC_TP)"
        docId:
          type: string
          description: "Document Identification / Identifikátor dokumentu (v úložišti dokumentů)"
    CtPurposeDoc:
      type: object
      properties:
        docTpId:
          type: string
          description: "Document Type Identifier / Typ dokumentu (LOV DOC_TP)"
        docSubTpId:
          type: string
          description: "Document subType Identifier / Typ dokumentu (LOV DOC_SUB_TP)"
        docId:
          type: string
          description: "Document Identification / Identifikátor dokumentu (v úložišti dokumentů)"
        docStatId:
          type: string
          description: "Document status (LOV DOC_STAT)"
        delFlag:
          type: boolean
          description: "Document delete Flag"
          example: false
    CtSpecCond:
      type: object
      properties:
        condId:
          type: string
          description: "Id specifické podmínky"
        condDetail:
          type: string
          maxLength: 2000
          description: "Text specifické podmínky"
        condStatId:
          type: string
          description: "Document status (LV SPEC_COND_STAT)"
    CtConsent:
      type: object
      properties:
        consentTpId:
          type: string
          description: consent type (LV_CNST_TP)
        consentSubTpId:
          type: string
          description: consent subtype (LV_CNST_SUB_TP)
        timestamp:
          type: string
          description: consent timestamp
          format: date-time
        status:
          type: string
          description: consent status (LV_CNST_STAT)
    GetMortApplResponse:
      type: object
      description: Get mortgage Appl service response.
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtPersonMortGet'
        mortgageAppls:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtGetMortAppl'
    GetHycCollateralsResponse:
      type: object
      description: Get Collaterals from HypoClient Service Response.
      properties:
        busApplId:
          type: string
          description: "Business Application Identifier / Business číslo žádosti (busApplId identifikátor hlavní žádosti o hypotéku)."
          example: '**********'
        esgCatgAutoFlag:
          type: boolean
          description: "ESG Category auto flag / Možnost kategorie ESG (Aut.)"
          example: false
        esgCatgFlag:
          type: boolean
          description: "ESG Category flag / Splňuje kategorii ESG"
          example: false
        totAvlblPlgAmtHyc:
          type: number
          description: "Total available pledge value calc in HYC / Celková neobsazená zástavní hodnota z HYC"
          example: 123456789
        totPlgAmtHyc:
          type: number
          description: "Total pledge value calc in HYC / Celková zástavní hodnota z HYC"
          example: 123456789
        pledges:
          type: array
          description: "Application Pledges complex type."
          items:
            $ref: '#/components/schemas/CtApplPledgesMortGet'
        realtyInsurances:
          type: array
          description: "Realty Insurances decisions complex type."
          items:
            $ref: '#/components/schemas/CtApplRealtyInsurancesMortGet'
    CtPersonMort:
      allOf:
        - $ref: '#/components/schemas/CtPersonGet'
        - type: object
          properties:
            ptStatId:
              type: string
              example: false
            appctAge:
              type: integer
              description: Applicant Age / Věk žadatele v čase podání žádosti
              example: 50
            applDataSeg:
              type: array
              description: Application Data Segments (Dle jakého segmentu bude/byl proveden sběr dat pro danou žádost).
              items:
                type: string
    CtGetLoanAppl:
      type: object
      properties:
        applTpId:
          type: string
          description: Application type ID, LovApplTp.
          example: NEW_PROD
        applStatId:
          type: string
          description: Application state ID, LovApplStat.
          example: OO_INIT
        applDate:
          type: string
          description: Application Date. Client signature date of application.
          format: date-time
        posId:
          type: string
          description: Point of sales ID, LovPos.
          example: RBPHPKCB
        firstTouchPoint:
          type: string
          description: First Touch Point.
          example: '18000000'
        applReason:
          type: string
          description: Application Reason / Důvod žádosti.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        preApprOfrId:
          type: string
          description: Jednoznačný identifikátor nabídky určený SPSS.
        opportunityId:
          type: string
          description: Identifikátor příležitosti ze Siebelu.
        fullApplFlag:
          type: boolean
          description: Příznak, zda musel klient v rámci predschvalene nabídky vyplnit celou žádost (true) nebo vyplnil jenom data týkající se žádosti a přeskočil osobní údaje a sociodemografika (false).
          example: false
        ccyId:
          type: string
          description: Currency, LovCcy.
          example: CZK
        distCnlId:
          type: string
          description: Distribution chanel, LovCnl.
          example: BROKER
        advisorId:
          type: string
          description: Advisor Identifier / Identifikace poradce.
        advisorName:
          type: string
          description: Advisor Name / Jméno poradce.
        fulfillmentCnlId:
          type: string
          description: Fulfillment chanel, LovCnl.
          example: WEB
        wrkPlaceId:
          type: string
          description: Work place ID.
        busProdSubTp:
          type: string
          description: Business product sub-type, LovBusProdSubTp. Application product type taken from one of variants (logic in ADB). Relevant for getApplication only, setApplication ignores this element.
          example: REFO
        browserInfo:
          type: string
          description: Browser Info.
        receivedTraderName:
          type: string
          description: Trader Name / Název obchodníka, který bychom měli obdržet v rámci iniciace žádosti (bude využíváno zejm. u žádostí přes platební bránu pro identifikaci skutečného obchodníka).
        pushNotifPermissionFlag:
          type: boolean
          description: Push Notification Permission Flag / Nový atribut plněný jen pro žádosti o CL z MB. (true = povolena, false = zakázána)
          example: false
        activationId:
          type: string
          description: ID aktivace aplikace pro dohledání TOKENu - pro mobilni bankovnictvi.
        contAccessReqFlag:
          type: boolean
          description: Contacts Access Request Flag (false = nebyla zobrazena obrazovka pro vyžádání telefonního seznamu, true = byla zobrazena), aktualizuje se při zobrazení obrazovky.
          example: false
        contAccessAlwFlag:
          type: boolean
          description: Contacts Access Allowed Flag (false = nebyl udělen souhlas, true = byl udělen souhlas), plní se při potvrzení/odmítnutí souhlasu (ať už navržené obrazovky či souhlasu na úrovni aplikace).
          example: false
        contAccessScreenId:
          type: string
          description: Contact Access Screen Identifier / Kód obrazovky mobilní apky pro účely AB testingu a analýz, plní se při zobrazení obrazovky.
        dscntIntrs:
          type: number
          description: Discount Interest / Nabídnutá sleva na sazbě (1%=0,01)), plní se při zobrazení obrazovky.
        telcoQueryAllowedFlag:
          type: boolean
          description: Povolen dotaz do Telco Score.
        authMobSearchFlag:
          type: boolean
          description: Authorization Phone Search Flag / Plní se po ověření telefonu v DB - pokud se vrátí osoba, plní se true, jinak false.
        orderNr:
          type: string
          description: Order Number / Císlo objednávky.
        url:
          type: string
          description: Application Data Segment / Dle jakého segmentu byl proveden sběr dat pro danou žádost.
        segments:
          type: array
          description: Segment list.
          items:
            type: string
            description: Segment list.
        firstTouchPointOwnr:
          type: string
          description: First touch point owner / zadavatel žádosti.
        promoCode:
          type: string
          description: Promo code / Kód promo akce.
        accNumPrefix:
          type: string
          description: Account number prefix.
        accNum:
          type: string
          description: Account number.
        accBankCode:
          type: string
          description: Account bank code. Bank code in CZ, LovBankCzCode.
          example: '5500'
        applDsnKey:
          type: string
          description: Current Application Decision Key / Instanční klíč aktualniho rozhodnuti k zadosti.
        applComplPosId:
          type: string
          description: Application completion Point of sales ID / POS completace žádosti, LovPos.
          example: RBPHOLB
        contrNum:
          type: string
          description: CC internal contract number. Serves for identifiying relship between CC end other systems. / Číslo smlouvy.
        contrSignDate:
          type: string
          description: Signature Date - Date when the LAST applicant signed the contract / datum podpisu smlouvy posledním žadatelem.
          format: date
          example: '2022-12-16'
        contrSignPosId:
          type: string
          description: Contract signing Point of sales ID, LovPos.
          example: RBPHPKCB
        contrSignAdvisorId:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        contrSignAdvisorName:
          type: string
          description: "\tContract Sign Advisor Name/ Jméno poradce."
        lastChangeAdvisorId:
          type: string
          description: Last Change Advisor Identifier / Poslední, kdo pracoval se žádostí.
        validFromDate:
          type: string
          description: Valid From Date.
          format: date-time
        validToDate:
          type: string
          description: Valid To Date.
          format: date-time
        moneyTransferDate:
          type: string
          description: Money Transfer Date.
          format: date-time
          example: '2022-12-27T10:40:17.2393401+01:00'
        rejectRsnId:
          type: string
          description: External rejection reason type, LovExtnRejRsnTp.
          example: REJ_INVALID_ID
        rejectRsn:
          type: string
          description: External Reject Reason Type Descriptor.
        rejectRsnTp:
          type: string
          description: Rejection reason type.
        personApplModelId:
          type: string
          description: SCG Person Application Model Identifier / Identifikator skoringové karty, kterou byl daný klient vyhodnocován.
        computedSalary:
          type: number
          description: Salary / Příjem.
        fiOperCode:
          type: string
          description: FI Operation Code.
        incVerifPSD2Flag:
          type: boolean
          description: Indicate posibility of income verification by PSD2 channel / Indikuje možnost ověřování přijmu přes PSD2.
          example: false
        incVerifAccStmFlag:
          type: boolean
          description: Indicate posibility of income verification by the account statement / Indikuje možnost ověřování přijmu pomocí výpisu z účtu.
          example: false
        incVerifStmUploadFlag:
          type: boolean
          description: Indicate posibility of income verification by upload of account statement / Indikuje možnost ověřování přijmu přímým nahráním výpisu z účtu.
          example: false
        incVerifCallFlag:
          type: boolean
          description: Indicate posibility of income verification by phone call to employer / Indikuje možnost ověřování přijmu pomocí telefonického ověření u zaměstnavatele.
          example: true
        incVerifSrcId:
          type: string
          description: Income verification source, LovIncVerifSrc.
          example: STATEMENT
        incVerifPSD2Discount:
          type: number
          description: Interest rate discount when verification is via PSD2 channel.
        contrCond:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        registryResult:
          type: string
          description: Risk band, LovRiskBand.
        finalRiskClass:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        postpndDrawFlag:
          type: boolean
          description: Postponed Drawing Flag / Odložené čerpání.
          example: false
        cmt:
          type: string
          description: comment / komentar
      description: CtGetLoanAppl extends CtSetLoanAppl. Loan application data. Get loan application complex type.
    CtGetBuildingLoanAppl:
      type: object
      properties:
        applTpId:
          type: string
          description: Application type ID, LovApplTp.
          example: NEW_PROD
        applStatId:
          type: string
          description: Application state ID, LovApplStat.
          example: OO_INIT
        applDate:
          type: string
          description: Application Date. Client signature date of application.
          format: date-time
        posId:
          type: string
          description: Point of sales ID, LovPos.
          example: RBPHPKCB
        firstTouchPoint:
          type: string
          description: First Touch Point.
          example: '18000000'
        applReason:
          type: string
          description: Application Reason / Důvod žádosti.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        preApprOfrId:
          type: string
          description: Jednoznačný identifikátor nabídky určený SPSS.
        opportunityId:
          type: string
          description: Identifikátor příležitosti ze Siebelu.
        ccyId:
          type: string
          description: Currency, LovCcy.
          example: CZK
        distCnlId:
          type: string
          description: Distribution chanel, LovCnl.
          example: BROKER
        fulfillmentCnl:
          type: string
          description: Fulfillment chanel, LovCnl.
          example: WEB
        wrkPlaceId:
          type: string
          description: Work place ID.
        busProdSubTp:
          type: string
          description: Business product sub-type, LovBusProdSubTp. Application product type taken from one of variants (logic in ADB). Relevant for getApplication only, setApplication ignores this element.
          example: REFO
        browserInfo:
          type: string
          description: Browser Info.
        telcoQueryAllowedFlag:
          type: boolean
          description: Povolen dotaz do Telco Score.
        authMobSearchFlag:
          type: boolean
          description: Authorization Phone Search Flag / Plní se po ověření telefonu v DB - pokud se vrátí osoba, plní se true, jinak false.
        firstTouchPointOwnr:
          type: string
          description: First touch point owner / zadavatel žádosti.
        promoCode:
          type: string
          description: Promo code / Kód promo akce.
        applDsnKey:
          type: string
          description: Current Application Decision Key / Instanční klíč aktualniho rozhodnuti k zadosti.
        applComplPosId:
          type: string
          description: Application completion Point of sales ID / POS completace žádosti, LovPos.
          example: RBPHOLB
        contrNum:
          type: string
          description: Cislo uverove smlouvy.
        contrNumBs:
          type: string
          description: Číslo sporici smlouvy.
        contrNumReg:
          type: string
          description: Registrační číslo úvěrové smlouvy.
        contrNumCr:
          type: string
          description: Cislo uveroveho kontraktu.
        contrSignDate:
          type: string
          description: Signature Date - Date when the LAST applicant signed the contract / datum podpisu smlouvy posledním žadatelem.
          format: date
          example: '2022-12-16'
        contrSignPosId:
          type: string
          description: Contract signing Point of sales ID, LovPos.
          example: RBPHPKCB
        contrSignAdvisorId:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        contrSignAdvisorName:
          type: string
          description: "\tContract Sign Advisor Name/ Jméno poradce."
        lastChangeAdvisorId:
          type: string
          description: Last Change Advisor Identifier / Poslední, kdo pracoval se žádostí.
        validFromDate:
          type: string
          description: Valid From Date.
          format: date-time
        validToDate:
          type: string
          description: Valid To Date.
          format: date-time
        moneyTransferDate:
          type: string
          description: Money Transfer Date.
          format: date-time
          example: '2022-12-27T10:40:17.2393401+01:00'
        rejectRsnId:
          type: string
          description: External rejection reason type, LovExtnRejRsnTp.
          example: REJ_INVALID_ID
        rejectRsn:
          type: string
          description: External Reject Reason Type Descriptor.
        rejectRsnTp:
          type: string
          description: Rejection reason type.
        personApplModelId:
          type: string
          description: SCG Person Application Model Identifier / Identifikator skoringové karty, kterou byl daný klient vyhodnocován.
        computedSalary:
          type: number
          description: Salary / Příjem.
        incVerifPSD2Flag:
          type: boolean
          description: Indicate posibility of income verification by PSD2 channel / Indikuje možnost ověřování přijmu přes PSD2.
          example: false
        incVerifAccStmFlag:
          type: boolean
          description: Indicate posibility of income verification by the account statement / Indikuje možnost ověřování přijmu pomocí výpisu z účtu.
          example: false
        incVerifStmUploadFlag:
          type: boolean
          description: Indicate posibility of income verification by upload of account statement / Indikuje možnost ověřování přijmu přímým nahráním výpisu z účtu.
          example: false
        incVerifCallFlag:
          type: boolean
          description: Indicate posibility of income verification by phone call to employer / Indikuje možnost ověřování přijmu pomocí telefonického ověření u zaměstnavatele.
          example: true
        incVerifSrcId:
          type: string
          description: Income verification source, LovIncVerifSrc.
          example: STATEMENT
        incVerifPSD2Discount:
          type: number
          description: Interest rate discount when verification is via PSD2 channel.
        contrCond:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        registryResult:
          type: string
          description: Risk band, LovRiskBand.
        finalRiskClass:
          type: string
          description: Contract Sign Advisor Identifier/ ID poradce.
        consents:
          type: array
          description: Consent list
          items:
            $ref: '#/components/schemas/CtConsent'
        quester:
          $ref: '#/components/schemas/CtQuesterGet'
      description: CtGetBuildingLoanAppl. Building loan application data. Get loan application complex type.
    CtQuesterQuestGet:
      type: object
      properties:
        catgQuestId:
          type: string
          example: 'someString'
          description: 'Category Question Identifier / Kategorie otázek (LOV CATG_QUEST)'
        questId:
          type: string
          example: 'someString'
          description: 'Question Identifier / Otázka (LOV QUEST)'
        questStatId:
          type: string
          example: 'someString'
          description: 'Question Status Key / Stav odpovědi na otázku (LOV QUEST_STAT)'
        questRsp:
          type: string
          example: 'someString'
          description: 'Response / Odpověď na otázku'
        questSbmDate:
          type: string
          format: date-time
          example: '2022-12-27T10:40:17.2393401+01:00'
          description: 'Submit date / Čas zodpovězení otázky'
      description: CtQuesterQuest. Building loan application quester data.
    CtQuesterQuestSet:
      type: object
      properties:
        questId:
          type: string
          example: 'someString'
          description: 'Question Identifier / Otázka (LOV QUEST)'
        questStatId:
          type: string
          example: 'someString'
          description: 'Question Status Key / Stav odpovědi na otázku (LOV QUEST_STAT)'
        questRsp:
          type: string
          example: 'someString'
          description: 'Response / Odpověď na otázku'
        questSbmDate:
          type: string
          format: date-time
          example: '2022-12-27T10:40:17.2393401+01:00'
          description: 'Submit date / Čas zodpovězení otázky'
      description: CtQuesterQuest. Building loan application quester data.
    CtQuesterSet:
      type: object
      properties:
        sbmDate:
          type: string
          format: date-time
          example: '2022-12-27T10:40:17.2393401+01:00'
          description: 'Submit date / Čas vyplnění dotazníku'
        refreshFlag:
          type: boolean
          example: 'true'
          description: 'Refresh Flag / Řídící flag pro smazání všech odpovědí u dané žádosti a nahrazení novým seznamem (0/null - merge seznamů, 1 - smaže uložený seznam a nahradí zaslaným)'
        quests:
          type: array
          items:
            $ref: '#/components/schemas/CtQuesterQuestSet'
      description: CtQuesterQuest. Building loan application quester data.
    CtQuesterGet:
      type: object
      properties:
        sbmDate:
          type: string
          format: date-time
          example: '2022-12-27T10:40:17.2393401+01:00'
          description: 'Submit date / Čas vyplnění dotazníku'
        quests:
          type: array
          items:
            $ref: '#/components/schemas/CtQuesterQuestGet'
    GetLoanApplResponse:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        primaryOwner:
          $ref: '#/components/schemas/CtPersonGet'
        incomes:
          type: array
          description: CtPtIncome list. Party incomes.
          items:
            $ref: '#/components/schemas/CtPtIncome'
        loanAppl:
          $ref: '#/components/schemas/CtGetLoanAppl'
        applVariants:
          type: array
          description: CtApplVariant list. Application variants. Application variant complex type.
          items:
            $ref: '#/components/schemas/CtApplVariant'
        applVariantParams:
          type: array
          description: CtVariantParameterGet list. Extends CtVariantParameter. Get application variant parameters complex type.
          items:
            $ref: '#/components/schemas/CtVariantParameterGet'
        signChannels:
          type: array
          description: CtSignChannelGet list. Extends CtSignChannel. Get sign channel complex type.
          items:
            $ref: '#/components/schemas/CtSignChannelGet'
        applObligations:
          type: array
          description: CtObligation list. Application obligations complex type.
          items:
            $ref: '#/components/schemas/CtObligation'
        obligationSignChannels:
          type: array
          description: CtObligationSignChannel list. Obligation sign channels complex type.
          items:
            $ref: '#/components/schemas/CtObligationSignChannel'
        applMetadata:
          type: array
          description: CtApplMetadataEntry list. Application metadata. Application metadata entry complex type.
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
      description: Get Loan Appl service response.
    GetBuildingLoanApplResponse:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtPersonRstsGet'
        incomes:
          type: array
          description: CtPtIncome list. Party incomes.
          items:
            $ref: '#/components/schemas/CtPtIncomeRsts'
        buildingLoanAppl:
          $ref: '#/components/schemas/CtGetBuildingLoanAppl'
        applVariants:
          type: array
          description: CtApplVariant list. Application variants. Application variant complex type.
          items:
            $ref: '#/components/schemas/CtBuildingApplVariant'
        applObligations:
          type: array
          description: CtObligation list. Application obligations complex type.
          items:
            $ref: '#/components/schemas/CtBuildingObligation'
        obligationSignChannels:
          type: array
          description: CtObligationSignChannel list. Obligation sign channels complex type.
          items:
            $ref: '#/components/schemas/CtObligationSignChannel'
        applMetadata:
          type: array
          description: CtApplMetadataEntry list. Application metadata. Application metadata entry complex type.
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
        docs:
          type: array
          items:
            $ref: '#/components/schemas/CtDocGet'
      description: Get building loan Appl service response.
    ApplicationToCancel:
      required:
        - applKey
        - applTp
        - busApplId
        - busProdSubTp
        - busProdTp
        - state
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key / Klíč žádosti.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business Application Identifier / Business číslo žádosti.
          example: '**********'
        busProdTp:
          type: string
          description: Business Product Identifier / Typ schvalovaného produktu. LOVS LovBusProdTp
          example: ROD
        busProdSubTp:
          type: string
          description: Business Product Sub Type Identifier / Podtyp schvalovaného produktu. LOVS LovBusProdSubTp
          example: CCA_CZK_PO
        state:
          type: string
          description: 'Application Status Identifier / Stav žádosti. LOVS: LovApplStat'
          example: PZLEAD
        applTp:
          type: string
          description: 'Application Type Identifier / Typ žádosti. LOVS: LovApplTp'
          example: NEW_PROD
        url:
          type: string
          description: URL.
        hash:
          type: string
          description: Hash identifier / Identifikace žádosti hash.
          example: E0RXL7wLag1s4AqOhGbOfE9ca0olbRwtXWwqLiBOtkTbBxb2UR/WTsGcxfL+LZ6joYB6im0ZdcJdMONfLP
        opportunityId:
          type: string
          description: Opportunity ID / Identifikace nabídky.
      description: Get Appl List To Cancel service response element. (approval.applicationservice_v2.getappllisttocancel.CtApplication)
    ApplicationToStateChange:
      type: object
      properties:
        applKey:
          type: integer
          description: Application Key / Klíč žádosti.
          format: int64
          example: *********
        busApplId:
          type: string
          description: Business Application Identifier / Business číslo žádosti.
          example: '**********'
        busProdTp:
          type: string
          description: Business Product Identifier / Typ schvalovaného produktu. LOVS LovBusProdTp
          example: ROD
        busProdSubTp:
          type: string
          description: Business Product Sub Type Identifier / Podtyp schvalovaného produktu. LOVS LovBusProdSubTp
          example: CCA_CZK_PO
        actState:
          type: string
          description: 'Application Status Identifier / Stav žádosti. LOVS: LovApplStat'
          example: PZLEAD
        newState:
          type: string
          description: 'Application Status Identifier / Stav žádosti. LOVS: LovApplStat'
          example: PZLEAD
        applTp:
          type: string
          description: 'Application Type Identifier / Typ žádosti. LOVS: LovApplTp'
          example: NEW_PROD
        opportunityId:
          type: string
          description: Opportunity ID / Identifikace nabídky.
        tenantId:
          type: string
          description: Tenant ID / Identifikace tenanta.
      description: Get Appl List To State Change service response element.
    ApplicationForBrokers:
      type: object
      properties:
        applKey:
          type: integer
          format: int64
          description: Application Key / Klíč podžádosti
        applTpId:
          type: string
          description: Application Type Identifier / Typ žádosti (LOV_APPL_TP)
        applStatId:
          type: string
          description: Application Status Identifier / Stav žádosti (LOV APPL_STAT)
        brokerId:
          type: string
          description: Broker Identifier / Identifikace prodejce ((FIRST_TOUCH_POINT)
        busApplId:
          type: string
          description: Business Application Identifier / Business číslo žádosti
        busProdTpId:
          type: string
          description: Business Product Identifier / Typ schvalovaného produktu (LOV BUS_PROD_TP)
        busProdSubTpId:
          type: string
          description: Business Product Sub Type Identifier / Podtyp schvalovaného produktu (LOV BUS_PROD_SUB_TP)
        applDateTime:
          type: string
          format: date-time
          description: Application Date / Datum a čas založení žádosti
        contrSignDate:
          type: string
          format: date
          description: Contract Sign Date / Datum podpisu
        finaAmt:
          type: number
          description: Total amount of loan or limit / Celková výše pujčky nebo limitu
        instPtKey:
          type: integer
          format: int64
          description: Instance Party Key / Klíč instance hlavního žadatele
        applicantName:
          type: string
          description: Applicant First Name / Jméno hlavního žadatele
        applicantFamilyName:
          type: string
          description: Applicant Family Name / Příjmení hlavního žadatele
        hash:
          type: string
          description: Hash Identifier / Hash klíč
        brokerName:
          type: string
          description: Broker Name / Jméno prodejce (LOV BROKER)
        brokerComp:
          type: string
          description: Broker Company / Společnost prodejce (LOV BROKER)
        accOfficerId:
          type: string
          description: Acount Officer Identifier / Identifikace bankéře/ zpracovatele (FIRST_TOUCH_POINT_BNKR)
        accOfficerName:
          type: string
          description: Acount Officer Name / Jméno bankéře/ zpracovatele (LOV BANKER)
        mortCenterId:
          type: string
          description: Identifikaci bankovního centra (LOV POS)
        applDateExpr:
          type: string
          format: date-time
          description: Application Date Expiration / Datum a čas expirace žádosti
        distCnlId:
          type: string
          description: Distribution Channel Identifier / Distribuční kanál (LOV CNL)
        complCnlId:
          type: string
          description: Complete Channel Identifier / Kanál kompletace žádosti (LOV CNL)
        instlCnt:
          type: integer
          format: int32
          description: Instalment Count / Počet splátek
        intrsRx:
          type: number
          description: Interest Rate / Úroková sazba
        intrsRxFixPerTpId:
          type: string
          description: Interest Rate Fixation Period Type Identifier / Typ fixace úrokové sazby (LOV RX_FIX_PER_TP)
        surchrgTotAmt:
          type: number
          description: Surcharge Total Amount / Celková sleva
        feeAmtPon:
          type: number
          description: Fee Amount PON / Poplatek za odhad (FEE_TP.ID = PON)
        appctPhoneNum:
          type: string
          description: Applicant Phone Number / Telefonní kontakt na hlavního žadatele
        cmt:
          type: string
          description: Comment / Poznamka k zadosti
        appctEmail:
          type: string
          description: Applicant Email / Emailová adresa hlavního žadatele
        busProdSubTpDesignName:
          type: string
        surchrgIndivAmt:
          type: number
          description: výše individuální slevy (tedy pouze výše slevy se surchrgTpId="DIS_INDIV")
        appctSiebelId:
          type: string
          description: Applicant Siebel Identifier / SBL identifikace hlavního žadatele.
        appctBicId:
          type: string
          description: Applicant BIC Identifier / BIC identifikace hlavního žadatele.
    ApplicationForBrokersResult:
      type: object
      properties:
        applications:
          type: array
          items:
            $ref: '#/components/schemas/CtApplicationForBrokers'
        recordCount:
          type: integer
          format: int64
    CtApplicationForBrokers:
      type: object
      properties:
        applKey:
          type: integer
          format: int64
          description: Application Key / Klíč žádosti
        applTpId:
          type: string
          description: Application Type Identifier / Typ žádosti (LOV_APPL_TP)
        applStatId:
          type: string
          description: Application Status Identifier / Stav žádosti (LOV APPL_STAT)
        brokerId:
          type: string
          description: Broker Identifier / Identifikace prodejce ((FIRST_TOUCH_POINT)
        busApplId:
          type: string
          description: Business Application Identifier / Business číslo žádosti
        busProdTpId:
          type: string
          description: Business Product Identifier / Typ schvalovaného produktu (LOV BUS_PROD_TP)
        busProdSubTpId:
          type: string
          description: Business Product Sub Type Identifier / Podtyp schvalovaného produktu (LOV BUS_PROD_SUB_TP)
        applDateTime:
          type: string
          format: date-time
          description:   Application Date / Datum a čas založení žádosti
        contrSignDate:
          type: string
          format: date
          description: Contract Sign Date / Datum podpisu
        finaAmt:
          type: number
          description: Total amount of loan or limit / Celková výše pujčky nebo limitu
        instPtKey:
          type: integer
          format: int64
          description: Instance Party Key / Klíč instance hlavního žadatele
        applicantName:
          type: string
          description: Applicant First Name / Jméno hlavního žadatele
        applicantFamilyName:
          type: string
          description: Applicant Family Name / Příjmení hlavního žadatele
        hash:
          type: string
          description: Hash Identifier / Hash klíč
        brokerName:
          type: string
          description: Broker Name / Jméno prodejce (LOV BROKER)
        brokerComp:
          type: string
          description: Broker Company / Společnost prodejce (LOV BROKER)
        accOfficerId:
          type: string
          description: Acount Officer Identifier / Identifikace bankéře/ zpracovatele (FIRST_TOUCH_POINT_BNKR)
        accOfficerName:
          type: string
          description: Acount Officer Name / Jméno bankéře/ zpracovatele (LOV BANKER)
        mortCenterId:
          type: string
          description: Identifikaci bankovního centra (LOV POS)
        applDateExpr:
          type: string
          format: date-time
          description: Application Date Expiration / Datum a čas expirace žádosti
        distCnlId:
          type: string
          description: Distribution Channel Identifier / Distribuční kanál (LOV CNL)
        complCnlId:
          type: string
          description: Complete Channel Identifier / Kanál kompletace žádosti (LOV CNL)
        instlCnt:
          type: integer
          format: int32
          description: Instalment Count / Počet splátek
        intrsRx:
          type: number
          description: Interest Rate / Úroková sazba
        intrsRxFixPerTpId:
          type: string
          description: Interest Rate Fixation Period Type Identifier / Typ fixace úrokové sazby (LOV RX_FIX_PER_TP)
        surchrgTotAmt:
          type: number
          description: Surcharge Total Amount / Celková sleva
        feeAmtPon:
          type: number
          description: Fee Amount PON / Poplatek za odhad (FEE_TP.ID = PON)
        appctPhoneNum:
          type: string
          description: Applicant Phone Number / Telefonní kontakt na hlavního žadatele
        appctEmail:
          type: string
          description: Applicant Email / Emailová adresa hlavního žadatele
        cmt:
          type: string
          description: Comment / Poznamka k zadosti
        busProdSubTpDesignName:
          type: string
        appctSiebelId:
          type: string
          description: Applicant Siebel Identifier / SBL identifikace hlavního žadatele
        appctBicId:
          type: string
          description: Applicant BIC Identifier / BIC identifikace hlavního žadatele
        subapplications:
          type: array
          items:
            $ref: '#/components/schemas/ApplicationForBrokers'
    GetDeferredPaymentStatusResponse:
      required:
        - orderNumber
        - status
      type: object
      properties:
        orderNumber:
          type: string
          description: E-shop application/order number.
          example: ORD22446688
        status:
          type: string
          description: Status of the application for a deferred payment.
          example: NOT_FIN
          enum:
            - FIN_PROVIDED
            - APPR_COND
            - NOT_FIN
            - FIN_KO
        paymentVS:
          type: string
          description: Variable symbol for payment.
          example: '20071202351'
      description: Get Deferred Payment Status response.
    GetCreditCardApplResponse:
      type: object
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        primaryOwner:
          $ref: '#/components/schemas/CtPersonGet'
        incomes:
          type: array
          description: CtPtIncome list. Party incomes.
          items:
            $ref: '#/components/schemas/CtPtIncome'
        creditCardAppl:
          $ref: '#/components/schemas/CreditCardAppl'
        applVariants:
          type: array
          description: CtApplVariant list. Credit card application variants. Credit card application variant complex type.
          items:
            $ref: '#/components/schemas/CtApplVariant'
        applVariantParams:
          type: array
          description: CtVariantParameterGet list. Extends CtVariantParameter. Get credit card application variant parameters complex type.
          items:
            $ref: '#/components/schemas/CtVariantParameterGet'
        signChannels:
          type: array
          description: CtSignChannelGet list. Extends CtSignChannel. Get sign channel complex type.
          items:
            $ref: '#/components/schemas/CtSignChannelGet'
        applMetadata:
          type: array
          description: CtApplMetadataEntry list. Application metadata. Application metadata entry complex type.
          items:
            $ref: '#/components/schemas/CtApplMetadataEntry'
      description: Get Credit Card Appl service response.
    CtApplDecisionsMortGet:
      type: object
      properties:
        apprProcessPartId:
          type: string
          description: Approval Process Part Identifier / Fáze procesu schvalování (LOV APPR_PROCESS_PART)
          example:
        dsnTpId:
          type: string
          description: Decision Type Identifier / Typ rozhodnutí (LOV DSN_TP)
          example:
        crntFlag:
          type: boolean
          description: Idicate that decision is current/last / priznak, zda se jedna o posledni/aktualni rozhodnuti
          example: false
        maxDsti:
          type: number
          description: Max Debt Service to Income / Maximální dluhová služba
          example:
        maxDti:
          type: number
          description: Max Debt to Income / Maximální dluh na příjmu
          example: 132465
        applDsti:
          type: number
          description: Application DSTI / DSTI ukazatel na žádosti
          example: 123456
        applDti:
          type: number
          description: Application DTI / DTI ukazatel na žádosti
          example: 26000
        decrExp:
          type: number
          description: Declared Expenses / Deklarované výdaje
          example: 6056
        netIncSumFinal:
          type: number
          description: Net Income Sum Final / Celkový finální čistý měsíční příjem žadatelů
          example: 10000
        expSumFinal:
          type: number
          description: Expenses Sum Final / Celkové finální měsíční výdaje žadatelů
          example: 5000
        appctsAge:
          type: number
          description: Applicants Age / Referenční věk žadatelů uvedených na žádosti pro výpočet LTV
          example: 36
        instlSumFinal:
          type: number
          description: Installment Sum Final / Celková finální výše měsíčních splátek všech úvěrů
          example: 90000
        instlSumMortFinal:
          type: number
          description: Installment Sum Mortgage Final / Celková finální výše měsíčních splátek hypoték
          example: 40000
        expstSumFinal:
          type: number
          description: Exposition Sum Final / Celková finální expozice všech úvěrů, overdraftů a kreditních karet
          example: 50000
        totAvlblPlgAmt:
          type: number
          description: "Available pledge value / Neobsazená zástavní hodnota"
          example: 50000
      description: Application decision.
    CtApplPledgesMortGet:
      required:
        - plgAvlblAmt
        - plgCrntAmt
        - parcels
      type: object
      properties:
        extnRltId:
          type: number
          description: "External Realty ID (HYC)"
          example: 123456
        extnCltId:
          type: string
          description: "Composite Identifier Collateral ID ('HYC_RLT_' + Realty ID (HYC))"
          example: HYC_RLT_123456
        ordNum:
          type: integer
          description: "Order Number / Pořadové číslo"
          example: 123456
        rltCatgId:
          type: string
          description: "Realty Category Id"
          example: "someString"
        rltTpId:
          type: string
          description: "Realty Type Id"
          example: "someString"
        cdstrUnitId:
          type: integer
          description: "RUIAN Cadastral Unit Id"
          example: 123456
        muncplId:
          type: integer
          description: "RUIAN Municipality Id / Obec"
          example: 123456
        cdstrOfficeName:
          type: string
          description: "Cadastral Office Name / Ktastrální úřad"
          example: "someString"
        muncplName:
          type: string
          description: "Municipality Name / Obec"
          example: "someString"
        owspCertNum:
          type: integer
          description: "Ownership Certificate Number"
          example: 123456
        owspCertIssuedDate:
          type: string
          format: date
          description: "Ownership Certificate Issued Date / LV ze dne"
          example:
        loanSubjFlag:
          type: boolean
          description: "Loan Subject Flag / Nemovitost je předmětem úvěru"
          example: true
        esgCatgEvalFlag:
          type: boolean
          description: "ESG Flag / Nemovitost splňuje kritéria pro ESG hypotéku"
          example: true
        rplcmtCostMin:
          type: number
          description: "Replacement cost minimal / Reprodukční náklady minimální"
          example: 1234567890
        idAppr:
          type: integer
          description: "Appraisement ID / ID odhadu"
          example: 123456
        returnVal:
          type: number
          description: "Return value / Výnosová hodnota"
          example: 1234567890
        purchPrice:
          type: number
          description: "Purchase price / Kupní cena"
          example: 1234567890
        idSav:
          type: integer
          description: "Supervision and verification ID / ID supervize a verifikace"
          example: 123456
        exclLocalityFlag:
          type: boolean
          description: "Excluded locality flag /Vyloučená lokalita"
          example: false
        luxuryRltyFlag:
          type: boolean
          description: "Luxury realty flag / Luxusní nemovitost"
          example: true
        luxuryLtvMax:
          type: number
          description: "Maximal LTV for luxury property"
          example: 1234567890
        consPlgValFlag:
          type: boolean
          description: "Consolidates pledge values flag / Sjednocení zást. hodnot"
          example: true
        plgAvlblAmt:
          type: number
          description: "Available pledge value / Neobsazená zástavní hodnota"
          example: 1234567890
        plgCrntAmt:
          type: number
          description: "Pledge value current / Zástavní hodnota současná"
          example: 1234567890
        plgMinAmt:
          type: number
          description: "Pledge value minimal / Zástavní hodnota minimální"
          example: 1234567890
        plgFutureAmt:
          type: number
          description: "Pledge value future / Zástavní hodnota budoucí"
          example: 1234567890
        wearPct:
          type: number
          description: "Wear Percentage / Opotřebení %"
          example: 1234567890
        annualRntlInc:
          type: number
          description: "Annual Rental Income / Roč. příjem z pronájmu"
          example: 1234567890
        plgSuitability:
          type: string
          description: "Pledge suitability / Vhodnost zástavy"
          example: "someString"
        mktabiltRtg:
          type: string
          description: "Marketability Rating / Rating prodejnosti"
          example: "someString"
        noteSupvsn:
          type: string
          description: "Supervisors note / Poznámka SUP"
          example: "someString"
        noteCond:
          type: string
          description: "Conditions note / Podmínky platnosti ceny"
          example: "someString"
        noteOblgtn:
          type: string
          description: "Obligations note / Závazky úvěru"
          example: "someString"
        residentialUnit:
          $ref: '#/components/schemas/CtApplPledgeResidentalUnitMortGet'
        parcels:
          type: array
          description: "Application Pledge Parcels List."
          items:
            $ref: '#/components/schemas/CtApplPledgeParcelMortGet'
        partyRoles:
          type: array
          description: "Application Pledge Party Roles List."
          items:
            $ref: '#/components/schemas/CtApplPledgePartyRoleMortGet'
        rightsRestrictions:
          type: array
          description: "Application Pledge Rights Restrictions List."
          items:
            $ref: '#/components/schemas/CtApplPledgeRightsRestrictMortGet'
      description: "Application Pledge (get)"
    CtApplPledgesMortSet:
      type: object
      properties:
        extnRltId:
          type: number
          description: "External Realty ID (HYC)"
          example: 123456
        extnCltId:
          type: string
          description: "Composite Identifier Collateral ID ('HYC_RLT_' + Realty ID (HYC))"
          example: HYC_RLT_123456
        extnParcelId:
          type: string
          description: "External Parcel Identifier / Klíč záznamu uložení pozemku v primárním systému (HYC)"
          example: 123456789
        ordNum:
          type: integer
          description: "Order Number / Pořadové číslo"
          example: 123456
        cltAmt:
          type: number
          description: "Cumulative Amount / Kumolovaná hodnota zastavy."
          example: 1234567890
        partyRoles:
          type: array
          description: "Application Pledge Party Roles List."
          items:
            $ref: '#/components/schemas/CtApplPledgePartyRoleMortSet'
      description: "Application Pledge (set)"
    CtLoanSubjectsMortGet:
      type: object
      properties:
        loanSubjKey:
          type: integer
          format: int64
          description: "Loan Subject Key / Instanční klíč objektu úvěru."
          example: "1234567890"
        loanSubjCatgId:
          type: string
          description: "Loan Subject Category Identifier / Kategorie objektu úvěru (LOV LOAN_SUBJ_CATG)."
          example: "someString"
        finaRltTpId:
          type: string
          description: "Financed Realty Type Identifier / Typ financované nemovitosti (LOV FINA_RLT_TP)."
          example: "someString"
        rltUtilTpId:
          type: string
          description: "Identifier / Typ využití nemovitosti (LOV RLT_UTIL_TP)."
          example: "someString"
        rltSelFlag:
          type: boolean
          description: "Realty Selection Flag / Příznak, že žadatel už má nemovitost vybranou."
          example: false
        plgFlag:
          type: boolean
          description: "Pledge Flag / Příznak, že nemovitost slouží jako zástava."
          example: false
      description: "Loan Subjects object."
    CtApplRealtyInsurancesMortGet:
      type: object
      properties:
        extnCltId:
          type: string
          description: "Composite Identifier Collateral ID ('HYC_CLT_' + Collateral ID (HYC))"
          example: HYC_CLT_123456
        ordNum:
          type: integer
          description: "Order Number / Pořadové číslo"
          example: 123456
        cltCatgId:
          type: string
          description: "Collateral Category Id"
          example: "someString"
        extnRltId:
          type: number
          description: "Insured external realty ID (HYC) / Pojištěná nemovitost"
          example: 123456
        insurAmt:
          type: number
          description: "Insurance Amount / Pojistná částka"
          example: 1234567890
        insurAmtExcFlag:
          type: boolean
          description: "Insurance Amount Exception Flag / Výjimka pojistné částky (příznak)"
          example: false
        insurAmtMin:
          type: number
          description: "Insurance Amount Minimal / Pojistná částka minimální"
          example: 1234567890
        insurAmtCondOblgtn:
          type: number
          description: "Insurance Amount for Conditions and Obligations / Pojistná částka pro stanovení podmínek a závazků"
          example: 1234567890
      description: "Realty Insurance object."
    CtLoanSubjectsMortSet:
      type: object
      properties:
        loanSubjKey:
          type: integer
          format: int64
          description: "Loan Subject Key / Instanční klíč objektu úvěru."
          example: "1234567890"
        loanSubjCatgId:
          type: string
          description: "Loan Subject Category Identifier / Kategorie objektu úvěru (LOV LOAN_SUBJ_CATG)."
          example: "someString"
        finaRltTpId:
          type: string
          description: "Financed Realty Type Identifier / Typ financované nemovitosti (LOV FINA_RLT_TP)."
          example: "someString"
        rltUtilTpId:
          type: string
          description: "Identifier / Typ využití nemovitosti (LOV RLT_UTIL_TP)."
          example: "someString"
        rltSelFlag:
          type: boolean
          description: "Realty Selection Flag / Příznak, že žadatel už má nemovitost vybranou."
          example: false
        plgFlag:
          type: boolean
          description: "Pledge Flag / Příznak, že nemovitost slouží jako zástava."
          example: false
        delFlag:
          type: boolean
          description: "Delete Flag / 1 – smazat instanci předmětu úvěru."
          example: false
      description: "Loan Subjects object."
    CtApplRealtyInsurancesMortSet:
      type: object
      properties:
        extnCltId:
          type: string
          description: "Composite Identifier Collateral ID ('HYC_CLT_' + Collateral ID (HYC) or 'HYC_RLT_' + Realty ID (HYC))"
          example: HYC_CLT_1234567890
        ordNum:
          type: integer
          description: "Order Number / Pořadové číslo"
          example: 123456
        extnRltId:
          type: number
          description: "Insured external realty ID (HYC) / Pojištěná nemovitost"
          example: 123456
      description: "Realty Insurance object."
    CtApplPledgeResidentalUnitMortGet:
      type: object
      properties:
        rsdntUnitNum1:
          type: integer
          description: "Residential Unit Number 1 / Číslo bytové jednotky před lomítkem"
          example: 123456
        rsdntUnitNum2:
          type: integer
          description: "Residential Unit Number 2 / Číslo bytové jednotky za lomítkem"
          example: 123456
        shComnParts:
          type: string
          description: "Share in Common Parts of Building/ Podíl na spol. částech budovy"
          example: "someString"
        shPlotArea:
          type: string
          description: "Share of Plot Area / Podíl bytové jednotky na pozemku"
          example: "someString"
      description: "Pledge Residental Unit object."
    CtApplPledgeParcelMortGet:
      type: object
      properties:
        parcelIdentFlag:
          type: boolean
          description: "Parcel identificafion Flag / příznak určující parcelu, na které stojí budova, která je předmětem zástavy (identifikuje stavbu/budovu)"
          example: true
        parcel:
          $ref: '#/components/schemas/CtApplPledgesParcelParcelMortGet'
        building:
          $ref: '#/components/schemas/CtApplPledgesParcelBuildingMortGet'
      description: "Pledge Parcel object."
    CtApplPledgesParcelParcelMortGet:
      type: object
      properties:
        extnParcelId:
          type: integer
          description: "External Parcel ID (HYC)"
          example: 123456
        grpNum:
          type: string
          description: "Parcel Group number / Skupinové číslo parcely"
          example: "someString"
        stemNum:
          type: integer
          description: "Parcel Stem Number / Kmenové parcelní číslo"
          example: 123456
        subdivNum:
          type: integer
          description: "Parcel Subdivision Number / Pododdělení čísla parcely"
          example: 123456
        pieceNum:
          type: integer
          description: "Díl parcely"
          example: 123456
        constrFlag:
          type: boolean
          description: "WIth construction flag / Pozemek se stavbou?"
          example: true
      description: "Pledge Parcel Parcel object."
    CtApplPledgesParcelBuildingMortGet:
      type: object
      properties:
        rltTpId:
          type: string
          description: "Realty Type Id"
          example: "someString"
        buildNumTpId:
          type: string
          description: "Building number type Id / Typ čísla budovy"
          example: "someString"
        buildNum:
          type: integer
          description: "Building Number / Číslo budovy"
          example: "someString"
      description: "Pledge Parcel Building object."
    CtApplPledgePartyRoleMortGet:
      type: object
      properties:
        adbInstPtKey:
          type: string
          description: "ADB Instance Party Key"
          example: "someString"
        siebelId:
          type: string
          description: "Siebel Client Identifier"
          example: "someString"
        ptTpId:
          type: string
          description: "Party type ID"
          example: "someString"
        cltPtRoleTpId:
          type: string
          description: "Collateral Party Role Type Id"
          example: "someString"
        rltOwspTpId:
          type: string
          description: "Realty Ownership Type Id"
          example: "someString"
        owspShAmt:
          type: string
          description: "Ownership Share Amount Fraction (HYC) "
          example: "5/5"
        busName:
          type: string
          description: "Business name of company / název firmy"
          example: "someString"
        icoNum:
          type: string
          description: "ICO of company / IČO"
          example: "someString"

      description: "Pledge Party Role object."
    CtApplPledgePartyRoleMortSet:
      type: object
      properties:
        adbInstPtKey:
          type: string
          description: "ADB Instance Party Key"
          example: "someString"
        cltPtRoleTpId:
          type: string
          description: "Collateral Party Role Type Id"
          example: "someString"
        rltOwspTpId:
          type: string
          description: "Realty Ownership Type Id"
          example: "someString"
      description: "Pledge Party Role object."
    CtApplPledgeRightsRestrictMortGet:
      type: object
      properties:
        owspRghtsRestTpId:
          type: string
          description: "Ownership Rights Restriction Type Id"
          example: "someString"
        restAmtUpTo:
          type: number
          description: "Restriction Amount Up To / Omezení do výše"
          example: 12345678790
        plgValOcc:
          type: number
          description: "Pledge Value Occupied /  Obsazená zástavní hodnota (Kč)"
        plgContrNum:
          type: string
          description: "Pledge Contract Number / Číslo zástavní smlouvy"
          example: 12345678790
        owspCertNum:
          type: integer
          description: "Ownership Certificate Number / Na LV č."
          example: 123456
        owspCertIssuedDate:
          type: string
          format: date
          description: "Ownership Certificate Issued Date / LV ze dne"
          example: "2025-05-25"
        owspCertFileNum:
          type: string
          description: "Ownership Certificate File Number / Č.j. zápisu"
          example: "someString"
        plgAgreementFlag:
          type: boolean
          description: "Pledge agreement flag / Souhlas se zřízením ZP v dalším pořadí"
          example: true
      description: "Pledge Rights Restriction object."
    GetMortgageServicingResponse:
      type: object
      description: Get mortgage Appl service response.
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        hash:
          type: string
          description: Hash identifier of application
          example: 60565464646465465
        applTpId:
          type: string
          description: Application Type Identifier (LOV APPL_TP)
          example:
        applStatId:
          type: string
          description: Application State Identifier (LOV APPL_STAT)
          example: 6056
        prodSrcId:
          type: string
          description: Product Source Identifier / Identifikace (PK) hypotečního produktu
          example: 6056
        prodSrcSysId:
          type: string
          description: Product Source System Identifier / Identifikace systému hypotečního produktu
          example: 6056
        prodContrNum:
          type: string
          description: Product Contact Number / Číslo smlouvy hypotečního produktu
          example: 6056
        busProdTpId:
          type: string
          description: Business Product Type Identifier / Typ produktu (LOV BUS_PROD_TP)
          example: 6056
        busProdSubTpId:
          type: string
          description: Business Product SubType Identifier / Podtyp produktu (LOV BUS_PROD_SUB_TP)
          example: 6056
        applDate:
          type: string
          format: date
          description: Application Date / Datum vytvoření žádosti
          example: '2022-12-16'
        exprDate:
          type: string
          format: date
          description: Expiration Date / Datum expirace žádosti
          example: '2022-12-16'
        firstTouchPoint:
          type: string
          description: First touch Point / Identifikace zadavatele žádosti
          example: 6056
        distCnlId:
          type: string
          description: Distribution chanel identification / Distribuční kanál (LOV CNL)
          example: 6056
        fulfillmentCnlId:
          type: string
          description: Fulfilment channel / Kanál vyplnění žádosti (LOV CNL)
          example: 6056
        propId:
          type: string
          description: Proposal Identifier / Identifikátor předschválené nabídky, ze které žádost vznikla
          example: 6056
        oprtyId:
          type: string
          description: Opportunity Identifier / Identifikátor příležitosti
          example: 6056
        actIntrsRx:
          type: number
          description: Actual Interest Rate / Stávající úroková sazba
          example: 6056
        actRxTpId:
          type: string
          description: Actual Rate Type Identifier / Typ úrokové sazby (LOV RX_TP)
          example: 6056
        actRxFixPerTpId:
          type: string
          description: Actual Rate Fix Period Type Identifier/ Perioda fixace úrokové sazby (LOV RX_FIX_PER_TP)
          example: 6056
        actSurchrgRx:
          type: number
          description: Actual Surcharge Rate / Stávající přirážka úrokové sazby
          example: 0.121
        nextRollovDate:
          type: string
          format: date
          description: Next Rollover Date / Datum otočky úrokové sazby
          example: '2022-12-16'
        rsdlPrinc:
          type: number
          description: Residual Principal / Předpokládaná zbývající výše jistiny ke dni realizace servisního požadavku
          example: 6056
        rsdlInstlCnt:
          type: integer
          format: int32
          description: Residual Instalment Count / Předpokládaný zbývající počet splátek ke dni realizace servisního požadavku
          example: 33
        nextRpmtDate:
          type: string
          format: date
          description: Next Repayment Date / Datum, kdy proběhne splátka v nové výši
          example: '2022-12-16'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtPersonMortgageServicing'
        fees:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtFeeMortgageServicing'
        rollovers:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtRolloverMortgageServicing'
    SetMortgageServicingRequest:
      type: object
      description: Set mortgage appl service request.
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
        hash:
          type: string
          description: Hash identifier of application
          example: 60565464646465465
        prodSrcId:
          type: string
          description: Product Source Identifier / Identifikace (PK) hypotečního produktu
          example: 6056
        prodSrcSysId:
          type: string
          description: Product Source System Identifier / Identifikace systému hypotečního produktu
          example: 6056
        prodContrNum:
          type: string
          description: Product Contact Number / Číslo smlouvy hypotečního produktu
          example: 6056
        busProdSubTpId:
          type: string
          description: Business Product SubType Identifier / Podtyp produktu (LOV BUS_PROD_SUB_TP)
          example: 6056
        exprDate:
          type: string
          format: date
          description: Expiration Date / Datum expirace žádosti
          example: '2022-12-16'
        firstTouchPoint:
          type: string
          description: First touch Point / Identifikace zadavatele žádosti
          example: 6056
        distCnlId:
          type: string
          description: Distribution chanel identification / Distribuční kanál (LOV CNL)
          example: 6056
        fulfillmentCnlId:
          type: string
          description: Fulfilment channel / Kanál vyplnění žádosti (LOV CNL)
          example: 6056
        propId:
          type: string
          description: Proposal Identifier / Identifikátor předschválené nabídky, ze které žádost vznikla
          example: 6056
        oprtyId:
          type: string
          description: Opportunity Identifier / Identifikátor příležitosti
          example: 6056
        actIntrsRx:
          type: number
          description: Actual Interest Rate / Stávající úroková sazba
          example: 6056
        actRxTpId:
          type: string
          description: Actual Rate Type Identifier / Typ úrokové sazby (LOV RX_TP)
          example: 6056
        actRxFixPerTpId:
          type: string
          description: Actual Rate Fix Period Type Identifier/ Perioda fixace úrokové sazby (LOV RX_FIX_PER_TP)
          example: 6056
        actSurchrgRx:
          type: number
          description: Actual Surcharge Rate / Stávající přirážka úrokové sazby
          example: 0.121
        nextRollovDate:
          type: string
          format: date
          description: Next Rollover Date / Datum otočky úrokové sazby
          example: '2022-12-16'
        rsdlPrinc:
          type: number
          description: Residual Principal / Předpokládaná zbývající výše jistiny ke dni realizace servisního požadavku
          example: 6056
        rsdlInstlCnt:
          type: integer
          format: int32
          description: Residual Instalment Count / Předpokládaný zbývající počet splátek ke dni realizace servisního požadavku
          example: 33
        nextRpmtDate:
          type: string
          format: date
          description: Next Repayment Date / Datum, kdy proběhne splátka v nové výši
          example: '2022-12-16'
        persons:
          type: array
          description: List of (co)applicants.
          items:
            $ref: '#/components/schemas/CtPersonMortgageServicing'
        fees:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtFeeMortgageServicing'
        rollovers:
          type: array
          description: List of main applicaion and its sub-applications.
          items:
            $ref: '#/components/schemas/CtRolloverMortgageServicing'
    SetMortgageServicingResponse:
      type: object
      description: Get mortgage Appl service response.
      properties:
        applId:
          $ref: '#/components/schemas/CtApplIdKeyId'
    CtPersonMortgageServicing:
      type: object
      properties:
        adbInstPtKey:
          type: integer
          description: ADB Instance Party Key / Instanční klíč žadatele
          format: int64
          example: *********
        applPtRoleTpId:
          type: string
          description: Application Party Role Type Identifier / Role žadatelek žádosti (LOV APPL_PT_ROLE_TP)
        contPersonFlag:
          type: boolean
          description: Contact Person Flag / Kontaktní osoba
        siebelId:
          type: string
          description: Siebel identifier.
          example: '********'
        rcNum:
          type: string
          description: Birth number. Social insurance number of a person.
          example: '801130/8943'
        titleBefore:
          type: string
          description: Title before the name.
          example: 'Mgr.'
        firstName:
          type: string
          description: First name of a person.
          example: Petr
        familyName:
          type: string
          description: Last name of a person.
          example: Novák
        birthDate:
          type: string
          description: "Birth day of a person."
          format: date
          example: '2022-12-16'
        genderId:
          type: string
          description: Gender, LovGender (LOV GENDER).
          example: M
        email:
          type: string
          description: Email Identifier.
          example: <EMAIL>
        phoneNum:
          type: string
          description: Phone Number.
          example: '420412440000'
        postalAddr:
          $ref: '#/components/schemas/CtAddress'
      description: CtPersonGetMortServ. Personal data.
    CtFeeMortgageServicing:
      type: object
      properties:
        feeTpId:
          type: string
          description: Fee Type Identifier / Typ poplatku (LOV FEE_TP)
        feeAmt:
          type: number
          description: Fee Amount / Výše poplatku v měně
          example: 1000
      description: CtFeeGetMortServ. Fee data.
    CtRolloverMortgageServicing:
      type: object
      properties:
        rollovOfrId:
          type: string
          description: Rollover Offer Identifier / Klíč nabídky úrokových sazeb pro otočku hypotéky
        rollovOfrIntrsId:
          type: string
          description: Rollover Offer Interest Identifier / Klíč sazby v nabídce úrokových sazeb pro otočku
        rollovOfrCreDate:
          type: string
          format: date
          description: Rollover Offer Create Date / Datum vygenerování nabídky úrokových sazeb
          example: '2022-12-16'
        rollovOfrTakeDate:
          type: string
          format: date
          description: Rollover Offer Take Date / Datum, ze kterého se získaly úrokové sazby
          example: '2022-12-16'
        rollovOfrValidTo:
          type: string
          format: date
          description: Rollover Offer Valid To / Platnost nabízené sazby
          example: '2022-12-16'
        rollovRxTpId:
          type: string
          description: Rollover Rate Type Identifier / Typ úrokové sazby (LOV RX_TP)
        rollovRxFixPerTpId:
          type: string
          description: Rollover Rate Fix Period Type Identifier / Perioda fixace úrokové sazby (LOV RT_FX_PER_TP)
        rollovSurchrgRx:
          type: number
          description: Rollover Surcharge Rate / Obchodní přirážka/sleva
        rollovTotIntrsRx:
          type: number
          description: Rollover Total Interest Rate / Výše nabízené sazby
        rollovInstlAmt:
          type: number
          description: Rollover Instalment Amount / Výše anuitní splátky při dané sazbě
        rollovFirstTouchPointBnkr:
          type: string
          description: Rollover First Touch Point Banker / FTP bankéře, který upravoval sazby nabídky
        rollovSelFlag:
          type: boolean
          description: Rollover Selected Flag / Vybraná skupina parametrů otočky
      description: CtRolloverGetMortServ. Rollover data.
    ErrorResponse:
      required:
        - correlationId
        - reasons
        - requestId
      type: object
      properties:
        requestId:
          type: string
          description: Original request id from header, or generated if not provided.
          example: MCH001207641782552682875
        correlationId:
          type: string
          description: Original correlation id from header, or generated if not provided.
          example: 99391c7e-ad88-49ec-a2ad-99ddcb1f7721
        status:
          maximum: 600
          minimum: 100
          type: integer
          description: Http status of response.
          format: int32
          example: 500
        reasons:
          type: array
          items:
            $ref: '#/components/schemas/ErrorReason'
      description: In case of any error.
    ErrorReason:
      type: object
      properties:
        code:
          type: string
          description: Error code.
          example: OTHER
          enum:
            - VALIDATION_REQUEST_ERROR
            - DATA_NOT_FOUND
            - DATABASE_SQL_ERROR
            - BUSINESS_VALIDATION_FAILED
            - VALIDATION_FAILED
            - MISSING_REQUEST_PARAMETER
            - MISSING_PATH_VARIABLE
            - OTHER
            - DUPLICATE_REQUEST
            - UNAUTHORIZED
            - EXTERNAL_SERVICE_ERROR
        severity:
          type: string
          description: Error severity.
          example: ERROR
          enum:
            - WARN
            - ERROR
            - FATAL
        message:
          type: string
          description: Error reason.
          example: Error between keyboard and chair.
      description: Error Reason.
  parameters:
    X-Api-Name:
      name: X-Api-Name
      in: header
      description: ID of the called service, definded by Integration platform RBCZ (Service IDs are managed in [kip.rb.cz](http://kip.rb.cz))
      required: true
      schema:
        type: string
      example: las-application-v1-xxxxxxxxxxx
    X-Request-Id:
      name: X-Request-Id
      in: header
      description: 'ID of the request, unique to the call, as determined by the initiating (consumer) application. UUID format or RBCZ Message ID convention: application code + 21 numbers. (i.e. <SYS>001207641782552682875)'
      required: true
      schema:
        type: string
      example: MCH001207641782552682875
    X-Correlation-Id:
      name: X-Correlation-Id
      in: header
      description: X-Correlation-ID will be used to track requests through all involved services to be able to graph request flows, map how services contribute to response times, identify bottle necks, etc. The X-Correlation-ID must be propagated unchanged in any and all downstream service calls (e.g. RBCZ services).
      required: true
      schema:
        type: string
      example: 99391c7e-ad88-49ec-a2ad-99ddcb1f7721
    X-Request-App:
      name: X-Request-App
      in: header
      description: ID of the service consumer application that generates the request (RBCZ application code).
      required: true
      schema:
        type: string
      example: ADB
    X-Idempotency-Key:
      name: X-Idempotency-Key
      in: header
      description: Unique request identifier to support idempotency.
      required: false
      schema:
        type: string
      example: d25ea706-5f78-11ed-9b6a-0242ac120002
    X-Frontend-App:
      name: X-Frontend-App
      in: header
      description: ID of the service consumer system which originally generated the request. Used only when the service is mediated by another system (RBCZ application code).
      required: false
      schema:
        type: string
      example: GIB
    X-Frontend-Service:
      name: X-Frontend-Service
      in: header
      description: Operation id of service from whitch this call originated.
      required: false
      schema:
        type: string
      example: xxx-application-v1-xxxxxx
    X-User-Id:
      name: X-User-Id
      in: header
      description: Unique identification of the user, who initiates the request (cza or technical user).
      required: false
      schema:
        type: string
      example: cza12345
    X-User-Type:
      name: X-User-Type
      in: header
      description: Additional information to destinguish internal user (cza...) or external user (technical accounts, 3rd parties, clients...) - INT | EXT.
      required: false
      schema:
        type: string
      example: INT
    X-Channel-Code:
      name: X-Channel-Code
      in: header
      description: Identification of the channel in which the request was sent.
      required: false
      schema:
        type: string
      example: BFO
    X-Cl-Context-Id:
      name: X-Cl-Context-Id
      in: header
      description: Identification tenant/context in which the request was sent (RB/RSTS). Default value is RB
      required: false
      schema:
        type: string
        example: RSTS
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic