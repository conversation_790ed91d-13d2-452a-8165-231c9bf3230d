openapi: 3.0.1
info:
  title: LAS ParamApp OpenAPI
  version: 7.2.0
  description: API for accessing parametrization data for loan application systems
servers:
  - url: http://localhost:8080/rbapl/las
    description: Generated server url
  - url: https://dev-api.rb.cz/rbapl/las
    description: PreSIT
  - url: https://tfx1-api.rb.cz/rbapl/las
    description: TFX1
  - url: https://preprod-api.rb.cz/rbapl/las
    description: PrePROD
  - url: https://api.rb.cz/rbapl/las
    description: PROD

tags:
  - name: Actuator
    description: Endpoints for monitoring and managing application internals. For internal use only.
  - name: Parametrization
    description: Endpoints to be called by business consumers.

paths:
  /parametrization-app/v1/actuator/loggers/{name}:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'loggers-name'
      operationId: loggers-name
      parameters:
        - name: name
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
    post:
      tags:
        - Actuator
      summary: Actuator web endpoint 'loggers-name'
      operationId: loggers-name_2
      parameters:
        - name: name
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: string
              enum:
                - TRACE
                - DEBUG
                - INFO
                - WARN
                - ERROR
                - FATAL
                - "OFF"
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /parametrization-app/v1/actuator:
    get:
      tags:
        - Actuator
      summary: Actuator root web endpoint
      operationId: links
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
                additionalProperties:
                  type: object
                  additionalProperties:
                    $ref: '#/components/schemas/Link'
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
                additionalProperties:
                  type: object
                  additionalProperties:
                    $ref: '#/components/schemas/Link'
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: object
                  additionalProperties:
                    $ref: '#/components/schemas/Link'
  /parametrization-app/v1/actuator/threaddump:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'threaddump'
      operationId: threaddump
      responses:
        "200":
          description: OK
          content:
            text/plain;charset=UTF-8:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/metrics:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'metrics'
      operationId: metrics
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/metrics/{requiredMetricName}:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'metrics-requiredMetricName'
      operationId: metrics-requiredMetricName
      parameters:
        - name: requiredMetricName
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/loggers:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'loggers'
      operationId: loggers
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/liquibase:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'liquibase'
      operationId: liquibase
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/info:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'info'
      operationId: info
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/heapdump:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'heapdump'
      operationId: heapdump
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                type: object
  /parametrization-app/v1/actuator/health:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'health'
      operationId: health
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/health/**:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'health-path'
      operationId: health-path
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/env:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'env'
      operationId: env
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/env/{toMatch}:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'env-toMatch'
      operationId: env-toMatch
      parameters:
        - name: toMatch
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object
  /parametrization-app/v1/actuator/beans:
    get:
      tags:
        - Actuator
      summary: Actuator web endpoint 'beans'
      operationId: beans
      responses:
        "200":
          description: OK
          content:
            application/vnd.spring-boot.actuator.v3+json:
              schema:
                type: object
            application/vnd.spring-boot.actuator.v2+json:
              schema:
                type: object
            application/json:
              schema:
                type: object

  /parametrization-app/v1/s2s/parametrizations/rcl:
    get:
      tags:
        - Parametrization
      summary: Retail Cash Loan parametrization resource
      description: Retrieves the current parametrization for Retail Cash Loans product
      operationId: las-parametrization-app-v1-getParamsRCL
      parameters:
        - $ref: '#/components/parameters/ValidityDate'
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: product parametrization result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RCLProductType'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /parametrization-app/v1/s2s/parametrizations/rcc:
    get:
      tags:
        - Parametrization
      summary: Retail Credit Card parametrization resource
      operationId: las-parametrization-app-v1-getParamsRCC
      parameters:
        - $ref: '#/components/parameters/ValidityDate'
        - $ref: '#/components/parameters/LangId'
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: product parametrization result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RCCProductType'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /parametrization-app/v1/s2s/parametrizations/rml:
    get:
      tags:
        - Parametrization
      summary: Retail Mortgages parametrization resource
      operationId: las-parametrization-app-v1-getParamsRML
      parameters:
        - $ref: '#/components/parameters/ValidityDate'
        - $ref: '#/components/parameters/LangId'
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: product parametrization result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RMLProductType'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /parametrization-app/v1/s2s/parametrizations/rml/agreement-provision-template-set/{id}:
    get:
      tags:
        - Parametrization
      summary: Agreement Provisions Template Set parametrization resource
      operationId: getRmlAgreementProvisionTemplateSet
      parameters:
        - name: id
          in: path
          required: true
          description: Agreement Provision Template Set identifier
          schema:
            type: string
        - $ref: '#/components/parameters/LangId'
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: Agreement Provisions Template Set parametrization result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgreementProvisionTemplateSet'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /parametrization-app/v1/s2s/parametrizations/rml/agreement-provision-groups:
    get:
      tags:
        - Parametrization
      summary: All Agreement Provision groups parametrization resource
      operationId: getAllRmlAgreementProvisionGroups
      parameters:
        - $ref: '#/components/parameters/ValidityDate'
        - $ref: '#/components/parameters/LangId'
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: All Agreement Provision Templates parametrization result
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AgreementProvisionGroup'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /parametrization-app/v1/s2s/parametrizations/bsl:
    get:
      tags:
        - Parametrization
      summary: Retail Building Loans parametrization resource
      operationId: las-parametrization-app-v1-getParamsBSL
      parameters:
        - $ref: '#/components/parameters/ValidityDate'
        - $ref: "#/components/parameters/BusProdSubTpId"
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: product parametrization result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BSLProductType'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /parametrization-app/v1/s2s/parametrizations/rod:
    get:
      tags:
        - Parametrization
      summary: Retail Overdrafts parametrization resource
      operationId: las-parametrization-app-v1-getParamsROD
      parameters:
        - $ref: '#/components/parameters/ValidityDate'
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
      responses:
        '200':
          description: product parametrization result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RODProductType'
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

components:
  parameters:
    #Header parameters
    X-Api-Name:
      name: X-Api-Name
      in: header
      description: ID of the called service, definded by Integration platform RBCZ (Service IDs are managed in [kip.rb.cz])
      required: true
      example: '<xyz>-<service-group>-v1-getApplicationStatus'
      schema:
        type: string
    X-Request-Id:
      name: X-Request-Id
      in: header
      description: |
        ID of the request, unique to the call, as determined by the initiating (consumer) application. UUID format or RBCZ Message ID convention: application code + 21 numbers.
        (i.e. <SYS>001207641782552682875)
      required: true
      example: 'MCH001207641782552682875'
      schema:
        type: string
    X-Correlation-Id:
      name: X-Correlation-Id
      in: header
      description: |
        X-Correlation-ID will be used to track requests through all involved services to be able to graph
        request flows, map how services contribute to response times, identify bottle necks, etc.
        The X-Correlation-ID must be propagated unchanged in any and all downstream service calls (e.g. RBCZ services).
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        format: uuid
    X-Idempotency-Key:
      name: X-Idempotency-Key
      in: header
      description: |
        Unique request identifier to support idempotency.
      required: true
      example: 'd25ea706-5f78-11ed-9b6a-0242ac120002'
      schema:
        type: string
        format: uuid
    X-Request-App:
      name: X-Request-App
      in: header
      description: ID of the service consumer application that generates the request (RBCZ application code).
      example: 'MCH'
      required: true
      schema:
        type: string
        maxLength: 3
    X-Frontend-App:
      name: X-Frontend-App
      in: header
      description: ID of the service consumer system which originally generated the request. Used only when the service is mediated by another system (RBCZ application code).
      example: 'GIB'
      schema:
        type: string
        maxLength: 3
    X-Frontend-Service:
      name: X-Frontend-Service
      in: header
      description: ID of the previous level service. Used only when the service is called from another service.
      example: 'serviceId-operation'
      schema:
        type: string
    X-User-Id:
      name: X-User-Id
      in: header
      description: Unique identification of the user, who initiates the request (cza or technical user).
      example: "cza12345"
      required: true
      schema:
        type: string
    X-User-Type:
      name: X-User-Type
      in: header
      description: Additional information to destinguish internal user (cza...) or external user (technical accounts, 3rd parties, clients...) - INT | EXT.
      example: "INT"
      required: true
      schema:
        type: string
        enum:
          - INT
          - EXT
    Content-Language:
      name: X-Content-Language
      in: header
      description: Defines the language(s) of the target audience allowing to identify and differentiate between the users' language preference.
      example: "cs-CZ"
      schema:
        type: string
    X-Channel-Code:
      name: X-Channel-Code
      in: header
      description: Identification of the channel in which the request was sent.
      example: "BFO"
      schema:
        type: string
    ValidityDate:
      name: validity-date
      in: query
      description: The resource returns product by version when the query parameter is specified; otherwise, the resource returns the current valid product
      required: false
      schema:
        type: string
        format: date
    LangId:
      name: lang-id
      in: query
      description: Text language identifier used for text localization inside a parametrization context
      required: false
      schema:
        type: string
        enum:
          - cs
          - en
        default: cs
    BusProdSubTpId:
      name: BusProdSubTpId
      in: query
      description: the resource returns product filtered by specific business product subtype if mentioned. Otherwise all parametrization is returned
      required: false
      schema:
        type: string

  schemas:
    Link:
      type: object
      properties:
        href:
          type: string
        templated:
          type: boolean
          example: true

    RCLProductType:
      type: object
      description: RCL parametrizace (busProdTp)
      required:
        - busProdClassId
        - busProdTp
        - busProdTpName
        - subtypes
      properties:
        busProdTp:
          type: string
          description: Typ produktu
        busProdTpName:
          type: string
          description: Název produktu
        busProdClassId:
          type: string
          description: Class ID z číselníku
        subtypes:
          type: array
          items:
            $ref: '#/components/schemas/RCLSubtypeParametrization'

    RCLSubtypeParametrization:
      type: object
      description: Varianta RCL parametrizace (busProdSubTp)
      required:
        - busProdSubTp
        - busProdSubTpId
        - busProdSubTpName
        - validFrom
      properties:
        busProdSubTp:
          type: string
          description: Typ varianty produktu
        busProdSubTpName:
          type: string
          description: Název varianty produktu
        busProdSubTpId:
          type: integer
          description: ID varianty produktu
          format: int64
        validFrom:
          type: string
          description: Platnost parametrizace OD
          format: date
        validTo:
          type: string
          description: Platnost parametrizace DO
          format: date
        currentAccount:
          $ref: '#/components/schemas/CurrentAccountParametrization'
        loan:
          $ref: '#/components/schemas/LoanParametrization'
        debitCard:
          $ref: '#/components/schemas/DebitCardParametrization'
        interestRates:
          type: array
          items:
            $ref: '#/components/schemas/RCLInterestRateParametrization'
        ranges:
          type: array
          items:
            $ref: '#/components/schemas/RCLRangeParametrization'

    CurrentAccountParametrization:
      type: object
      required:
        - productCode
        - priceProgram
        - mainCurrency
        - accountName
      properties:
        productCode:
          type: string
          example: PRDVBU
        priceProgram:
          type: string
          example: EKA
        mainCurrency:
          type: string
          example: CZK
        accountName:
          type: string
          example: CHYTRÝ účet

    LoanParametrization:
      type: object
      required:
        - productCode
        - type
        - applicationType
        - paramType
        - approvalProcess
        - businessAction
        - drawingType
      properties:
        productCode:
          type: string
          example: MIPU
        type:
          type: string
          example: TUPNCL
        applicationType:
          type: string
          example: CLIREQ
        paramType:
          type: string
          example: TPSPSS
        approvalProcess:
          type: string
          example: PSPNCL
        businessAction:
          type: string
          example: COMLOA
        drawingType:
          type: string
          example: B
        loanValueDflt:
          type: integer
          example: 200000

    DebitCardParametrization:
      type: object
      required:
        - validDuringLifeOnly
        - expressDelivery
        - statementToAtm
        - automaticReissuing
        - eCommerceActivated
        - deliveryType
        - pinDeliveryType
        - productType
        - productName
      properties:
        validDuringLifeOnly:
          type: boolean
          example: true
        expressDelivery:
          type: boolean
          example: true
        statementToAtm:
          type: boolean
          example: true
        automaticReissuing:
          type: boolean
          example: true
        eCommerceActivated:
          type: boolean
          example: true
        deliveryType:
          type: string
          example: A
        pinDeliveryType:
          type: string
          example: I
        productType:
          type: string
          example: RBMCDE
        productName:
          type: string
          example: Debit Master Card STANDARD
        limits:
          items:
            $ref: '#/components/schemas/DebitCardLimitParametrization'

    DebitCardLimitParametrization:
      type: object
      required:
        - limitType
        - amount
        - currency
        - period
      properties:
        limitType:
          type: string
          enum: [ ATM, POS, General ]
        amount:
          type: string
          example: "50000.00"
        currency:
          type: string
          example: CZK
        period:
          type: string
          enum: [ D, W, M ]

    RCLInterestRateParametrization:
      type: object
      required:
        - priority
        - regularInterestRate
        - minDuration
        - maxDuration
        - minValue
        - maxValue
      properties:
        priority:
          type: number
          example: 1
        regularInterestRate:
          type: string
          example: "9.9"
        minDuration:
          type: number
          example: 3
        maxDuration:
          type: number
          example: 12
        minValue:
          type: string
          example: "5000.0"
        maxValue:
          type: string
          example: "50000.0"
        preapprInterestRateIncrease:
          type: string
          example: "1.20"

    RCLRangeParametrization:
      type: object
      required:
        - minAmount
        - maxAmount
        - isActivatedInsuranceOffered
      properties:
        minAmount:
          type: number
          example: 0
        maxAmount:
          type: number
          example: 10000
        isActivatedInsuranceOffered:
          type: boolean
          example: true

    RCCProductType:
      type: object
      description: RCC parametrizace (busProdTp)
      required:
        - busProdClassId
        - busProdTp
        - busProdTpName
        - subtypes
      properties:
        busProdTp:
          type: string
          description: Typ produktu
        busProdTpName:
          type: string
          description: Název produktu
        busProdClassId:
          type: string
          description: Class ID z číselníku
        subtypes:
          type: array
          items:
            $ref: '#/components/schemas/RCCSubtypeParametrization'

    RCCPromoParametrization:
      type: object
      required:
        - promo
        - active
        - freeOfChargeMonths
        - freeOfChargeMonthsRPC
        - freeOfInterestMonths
        - freeOfInterestMonthsRPC
        - freeOfInsuranceMonths
        - freeOfInsuranceMonthsRPC
        - promoText
      properties:
        promo:
          type: string
          example: pulrokuzdarma2022
          description: Název promo kódu
        active:
          type: boolean
          example: true
          description: Parametr definuje, zda může být aktuálně daný promo kod použit
        freeOfChargeMonths:
          type: integer
          example: 10
          description: Počet měsíců bez poplatku
        freeOfChargeMonthsRPC:
          type: integer
          example: 22
          description: Service kód zasílaný do RPC místo produktového nastavení RPCFee z RCCPriceVariantParametrization
        freeOfInterestMonths:
          type: integer
          example: 3
          description: Počet měsíců s nulovým úrokem
        freeOfInterestMonthsRPC:
          type: integer
          example: 18
          description: Service kód zasílaný do RPC místo produktového nastavení RPCAffinityGroup z RCCPriceVariantParametrization
        freeOfInsuranceMonths:
          type: integer
          example: 12
          description: Počet měsíců s pojištěním zdarma
        freeOfInsuranceMonthsRPC:
          type: integer
          example: 123
          description: Service kód zasílaný do RPC místo produktového nastavení RPCServiceCode z RCCInsuranceParametrization
        promoText:
          type: string
          example: "Využijte promo akci a získejte 4 měsíce bez poplatku, 3 měsíce bez úroku a 2 měsíce pojištění zdarma"
          description: Text odstavce specifikující promo nabídku. Bude zobrazovaný v akceptačním e-mailu X0000018.
        w4FreeOfChargePromo:
          type: string
          example: "CHARGE-PROMO"
          description: Typ nastaveného promo pro zaslání do Way4 - sleva na poplatku
        w4FreeOfInterestPromo:
          type: string
          example: "INT-PROMO"
          description: Typ nastaveného promo pro zaslání do Way4 - sleva na úroku
        w4FreeOfInsurancePromo:
          type: string
          example: "INS-PROMO"
          description: Typ nastaveného promo pro zaslání do Way4 - sleva na pojištění


    RCCSubtypeParametrization:
      type: object
      description: Varianta RCC parametrizace (busProdSubTp)
      required:
        - busProdSubTp
        - busProdSubTpName
        - busProdSubTpId
        - validFrom
        - loanPurpose
        - MPROD
        - active
        - activelyOffered
        - preffered
        - clipAllowed
        - minCreditLimit
        - maxCreditLimit
        - defaultCreditLimit
        - RPCProduct
        - RPCCardType
        - RPCDesign
        - statementDate
        - RPCCycle
        - cashLimitMin
        - cashLimitMax
        - cashLimitPct
        - repaymentAccount
        - creditCardType
        - targetSystem
        - priceVariants
        - insurances
        - design
      properties:
        busProdSubTp:
          type: string
          description: Typ varianty produktu
        busProdSubTpName:
          type: string
          description: Název varianty produktu
        busProdSubTpId:
          type: integer
          description: ID varianty produktu
          format: int64
        validFrom:
          type: string
          description: Platnost parametrizace OD
          format: date
        validTo:
          type: string
          description: Platnost parametrizace DO
          format: date
        loanPurpose:
          type: integer
          description: Účel úvěru
          format: int32
          example: 90
        MPROD:
          type: integer
          description: Identifikace produktu v marketingovém katalogu
          format: int32
          example: 2114001
        active:
          type: boolean
          example: true
          description: Parametr definuje, zda karta může být prodávána, pokud dorazí na vstupu do CLFE
        activelyOffered:
          type: boolean
          example: true
          description: Parametr definuje, zda se karta zobrazí na seznamu karet v Sc70 pokud nedorazil seznam karet na vstupu
        preffered:
          type: boolean
          example: true
          description: Identifikuje, zda je karta zvýrazněna na prodejní obrazovce. Max 1 karta.
        clipAllowed:
          type: boolean
          example: true
          description: Identifikuje, zda je pro daný produkt povolené navýšení limitu (CLIP)
        minCreditLimit:
          type: string
          example: "10000"
          description: Minimální limit na kreditní kartě
        maxCreditLimit:
          type: string
          example: "500000"
          description: Maximální limit na kreditní kartě
        defaultCreditLimit:
          type: string
          example: "50000"
          description: Základní limit nabízený klientovi
        cobrandType:
          type: string
          example: "o2"
          description: Definuje, zda je karta součástí nějakého cobrand programu (o2, csa)
        RPCProduct:
          type: integer
          example: 10
          description: Typ produktu pro RPC
          format: int32
        RPCCardType:
          type: integer
          example: 10
          description: Typ karty pro RPC
          format: int32
        RPCDesign:
          type: integer
          example: 998
          description: Service kód zasílaný pro design karty
          format: int32
        statementDate:
          type: integer
          example: 21
          description: Den v měsíci, ke kterému se bude generovat výpis
          format: int32
        RPCCycle:
          type: integer
          example: 14
          description: Cycle date do RPC odpovídající požadovanému statementDate
          format: int32
        cashLimitMin:
          type: string
          example: "5000"
          description: Minimální hotovostní limit
        cashLimitMax:
          type: string
          example: "100000"
          description: Maximální hotovostní limit
        cashLimitPct:
          type: string
          example: "20.00"
          description: Procento hotovostního limitu z celkového limitu karty
        repaymentAccount:
          type: integer
          format: int64
          example: **********
          description: Technický účet pro splácení kreditní karty
        creditCardType:
          type: string
          example: "MCEASY"
          description: Typ karty v CIS číselníku CreditCardTypeCode
        targetSystem:
          type: string
          enum: ["VF", "W4"]
          example: "VF"
          description: Identifikuje, do jakého systému má být nová karta zakládaná
        w4IssuingContractType:
          type: string
          example: "CODE"
          description: Product kod issuing kontraktu
        w4CardType:
          type: string
          example: "CT"
          description: Product kod karetního kontraktu
        w4StatementDay:
          type: integer
          format: int32
          minimum: 1
          maximum: 31
          example: 14
          description: Den v měsíci, ke kterému se bude generovat výpis
        priceVariants:
          type: array
          items:
            $ref: '#/components/schemas/RCCPriceVariantParametrization'
        insurances:
          type: array
          items:
            $ref: '#/components/schemas/RCCInsuranceParametrization'
        promos:
          type: array
          items:
            $ref: '#/components/schemas/RCCPromoParametrization'
        design:
          $ref: '#/components/schemas/RCCDesign'

    RCCDesign:
      type: object
      required:
        - name
        - design
        - reviewDescriptions
        - detailDescriptions
        - listOrder
      properties:
        ribbon:
          type: string
          description: Text pro bublinu označující preferovanou kartu
          example: Oblíbená karta
        name:
          type: string
          description: Název produktu
          example: RB PREMIUM
        design:
          type: string
          description: URL odkaz na logo na public webu
          example: premium.jpg
        motto:
          type: string
          description: Tip ke kartě
          example: Skvělá karta na nákupy
        language:
          type: string
          description: Jazyk
          example: CZ
        listOrder:
          type: integer
          format: int32
          description: Pořadí zobrazení karty produktu - vzestupné
          example: 1
        reviewDescriptions:
          items:
            $ref: '#/components/schemas/RCCDesignDescription'
        detailDescriptions:
          items:
            $ref: '#/components/schemas/RCCDesignDescription'

    RCCDesignDescription:
      type: object
      required:
        - text
        - type
      properties:
        text:
          type: string
          description: Popis karty (benefit)
          example: Odměny až 1800 Kč ročně
        type:
          type: string
          description: URL odkaz na logo na public webu
          example: text

    RCCPriceVariantParametrization:
      type: object
      required:
        - name
        - intrestRatePOS
        - interestRateATM
        - RPCAffinityGroup
        - monthlyFee
      properties:
        name:
          type: string
          example: "Kreditní karta Premium FWR"
          description: Název varianty
        intrestRatePOS:
          type: number
          format: double
          example: 23.99
          description: Úroková sazba pro nehotovostní transakce
        interestRateATM:
          type: number
          format: double
          example: 29.99
          description: Úroková sazba pro hotovostní transakce
        RPCAffinityGroup:
          type: integer
          format: int32
          example: 100
          description: Definuje úrokovou míru pro RPC
        monthlyFee:
          type: integer
          format: int32
          example: 99
          description: Měsíční poplatek za vedení karty v CZK
        RPCFee:
          type: integer
          format: int32
          example: 143
          description: Service kód zasílaný pro pro měsíční poplatek
        w4InterestGroupAtm:
          type: number
          format: double
          example: 12.22
          description: Úroková sazba ATM (hodnota v %)
        w4InterestGroupPos:
          type: number
          format: double
          example: 12.22
          description: Úroková sazba POS (hodnota v %)
        w4InterestGroupInstantCash:
          type: number
          format: double
          example: 12.22
          description: Úroková sazba Instant Cash (hodnota v %)
        VIPClientSegment:
          $ref: '#/components/schemas/VIPClientSegment'

    VIPClientSegment:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: FWR
          description: Vazba na segment klienta, kterého se varianta týká.

    RCCInsuranceParametrization:
      type: object
      required:
        - insuranceCode
        - w4InsuranceCode
        - insuranceName
        - RPCServiceCode
        - active
        - type
        - priceCzk
        - pricePct
      properties:
        insuranceCode:
          type: string
          example: "100"
          description: Kód pojištění
        w4InsuranceCode:
          type: string
          example: "200"
          description: Kód pojištění pro Way4
        insuranceName:
          type: string
          example: "Auto asistence"
          description: Název pojištění
        RPCServiceCode:
          type: string
          example: "151"
          description: Kód servisu pro založení pojištění v RPC
        active:
          type: boolean
          example: true
          description: Parametr definuje, zda může být dané pojištění aktuálně prodáváno
        type:
          type: string
          enum: [ "M", "O" ]
          example: "M"
          description: (M-mandatory/O-optional) Identifikace, zda je pojištění v ceně
        priceCzk:
          type: string
          example: "500"
          description: Cena pojištění v Kč
        pricePct:
          type: string
          example: "0.5"
          description: Cena pojištění v procentech z vyčerpané částky

    RMLProductType:
      type: object
      description: RML parametrizace (busProdTp)
      required:
        - busProdClassId
        - busProdTp
        - busProdTpName
        - prodTpParameters
        - subtypes
      properties:
        busProdTp:
          type: string
          description: Typ produktu
        busProdTpName:
          type: string
          description: Název produktu
        busProdClassId:
          type: string
          description: Product Class ID z číselníku
        prodTypeParameters:
          $ref: '#/components/schemas/RMLTypeParametrization'
        subtypes:
          type: array
          items:
            $ref: '#/components/schemas/RMLSubtypeParametrization'

    RMLTypeParametrization:
      type: object
      description: Varianta RML parametrizace (busProdTp)
      required:
        - validFrom
        - coefCoLivCoaplicant
        - coefCoLivChild
        - costOfLiving
        - coefCC
        - coefOvd
        - instlStress
        - acceptedRolloverFixingPeriodTypeIds
      properties:
        validFrom:
          type: string
          description: Platnost od data
          format: date
        validTo:
          type: string
          description: Platnost do data
          format: date
        coefCoLivCoaplicant:
          type: number
          format: double
          description: Koeficient životních nákladů na spolužadatele v %
          example: 0.75
        coefCoLivChild:
          type: number
          format: double
          description: Koeficient životních nákladů na dítě v %
          example: 0.72
        costOfLiving:
          type: integer
          description: Průměrné životní náklady v kč
          format: int32
          example: 4850
        coefCC:
          type: number
          format: double
          description: Koeficient splátky kreditní karty v %
          example: 0.023
        coefOvd:
          type: number
          format: double
          description: Koeficient splátky overdraftu v %
          example: 0.023
        instlStress:
          type: number
          format: double
          description: Stress splátky v %
          example: 0.77
        houshExpMin:
          type: integer
          description: Minimální náklady na bydlení v Kč
          format: int32
          example: 3840
        medicalExpMin:
          type: integer
          description: Minimální výdaje za zdravotní péči v Kč
          format: int32
          example: 480
        parentalAllovanceGrntdMax:
          type: integer
          description: Maximální rodičovský příspěvek přiznaný v Kč
          format: int32
          example: 17300
        parentalAllovanceNotGrntdMax:
          type: integer
          description: Maximální rodičovský příspěvek nepřiznaný v Kč
          format: int32
          example: 6997
        maxPensRatio:
          type: number
          description: Maximální poměr důchodů a výživného ku hlavním příjmům v %
          format: double
          example: 0.5
        acceptedRolloverFixingPeriodTypeIds:
          type: array
          items:
            type: string
          description: List of accepted fixing period type IDs for rollover
          example: [ "1", "3", "5" ]

    RMLSubtypeParametrization:
      type: object
      description: Varianta RML parametrizace (busProdSubTp)
      required:
        - busProdSubTp
        - busProdSubTpName
        - validFrom
        - propertyPriceMin
        - propertyPriceMax
        - propertyPriceDflt
        - loanValueMin
        - loanValueMax
        - loanValueDflt
        - obligationAmtMin
        - obligationAmtMax
        - obligationAmtDflt
        - upsellAmtMin
        - upsellAmtMax
        - upsellAmtDflt
        - instalmentCountMin
        - instalmentCountMax
        - instalmentCountDflt
        - applicantCountMin
        - applicantCountMax
        - applicantCountDflt
        - applicantAgeMin
        - applicantAgeMax
        - applicantAgeDflt
        - applicantAgeRangeIdDflt
        - applicantsAgeMaturityMax
        - applicantAgeIncMin
        - householdCountMin
        - householdCountMax
        - householdCountDflt
        - applicantNetIncomeMin
        - applicantNetIncomeDflt
        - applicantsNetIncomeSumMin
        - applicantsNetIncomeSumDflt
        - expensesSumMin
        - expensesSumDflt
        - instlSumMin
        - instlSumDflt
        - currentAccountFlag
        - maturityExtensionDflt
        - coLivChildCntMin
        - coLivChildCntMax
        - acceptableOfferReduction
        - design
      properties:
        busProdSubTp:
          type: string
          description: Typ varianty produktu
        busProdSubTpName:
          type: string
          description: Název varianty produktu
        busProdSubTpId:
          type: integer
          description: ID varianty produktu
          format: int64
        validFrom:
          type: string
          description: Platnost parametrizace OD
          format: date
        validTo:
          type: string
          description: Platnost parametrizace DO
          format: date
        propertyPriceMin:
          type: integer
          format: int64
          description: Minimální výše nemovitosti
          example: 50000
        propertyPriceMax:
          type: integer
          format: int64
          description: Maximální výše nemovitosti
          example: 12000000
        propertyPriceDflt:
          type: integer
          format: int64
          description: Defaultní výše nemovitosti
          example: 5000000
        loanValueMin:
          type: integer
          format: int32
          description: Minimální výše půjčky
          example: 5000000
        loanValueMax:
          type: integer
          format: int32
          description: Maximální výše půjčky
          example: 50000000
        loanValueDflt:
          type: integer
          format: int32
          description: Defaultní výše půjčky
          example: 5000000
        obligationAmtMin:
          type: integer
          format: int32
          description: Minimální výše refinancovaného závazku (Refi)
          example: 1000
        obligationAmtMax:
          type: integer
          format: int32
          description: Maximální výše refinancovaného závazku (Refi)
          example: 100000
        obligationAmtDflt:
          type: integer
          format: int32
          description: Defaultní výše refinancovaného závazku (Refi)
          example: 5000
        upsellAmtMin:
          type: integer
          format: int32
          description: Minimální navýšení úvěru (Refi)
          example: 10000
        upsellAmtMax:
          type: integer
          format: int32
          description: Maximální navýšení úvěru (Refi)
          example: 100000
        upsellAmtDflt:
          type: integer
          format: int32
          description: Defaultní navýšení úvěru (Refi)
          example: 100000
        upsellAmtRangeIdDflt:
          type: string
          description: Identifikátor defaultního rozsahu navýšení úvěru
          example: "UPS0"
        instalmentCountMin:
          type: integer
          description: Minimální počet měsíčních splátek
          format: int32
          example: 6
        instalmentCountMax:
          type: integer
          description: Maximální počet měsíčních splátek
          format: int32
          example: 48
        instalmentCountDflt:
          type: integer
          description: Defaultní počet měsíčních splátek
          format: int32
          example: 12
        applicantCountMin:
          type: integer
          description: Minimální počet žadatelů
          format: int32
          example: 1
        applicantCountMax:
          type: integer
          description: Maximální počet žadatelů
          format: int32
          example: 4
        applicantCountDflt:
          type: integer
          description: Defaultní počet žadatelů
          format: int32
          example: 2
        applicantAgeMin:
          type: integer
          description: Minimální věk žadatele
          format: int32
          example: 18
        applicantAgeMax:
          type: integer
          description: Maximální věk žadatele
          format: int32
          example: 70
        applicantAgeDflt:
          type: integer
          description: Defaultní věk žadatele
          format: int32
          example: 30
        applicantAgeRangeIdDflt:
          type: string
          description: Identifikátor defaultního rozsah věku žadatele
          example: "AGE1836"
        applicantAgeMaturityMax:
          type: integer
          description: Maximální věk žadatele v době maturity úvěru
          format: int32
          example: 80
        applicantAgeIncMin:
          type: integer
          description: Minimální věk žadatele pro doložení příjmů
          example: 18
        householdCountMin:
          type: integer
          description: Minimální počet domácností
          format: int32
          example: 1
        householdCountMax:
          type: integer
          description: Maximální počet domácností
          format: int32
          example: 8
        householdCountDflt:
          type: integer
          description: Defaultní počet domácností
          format: int32
          example: 2
        applicantNetIncomeMin:
          type: integer
          format: int32
          description: Minimální příjem žadatele
          example: 20000
        applicantNetIncomeDflt:
          type: integer
          format: int32
          description: Defaultní příjem žadatele
          example: 30000
        applicantsNetIncomeSumMin:
          type: integer
          format: int32
          description: Minimální příjem více žadatelů
          example: 20000
        applicantsNetIncomeSumDflt:
          type: integer
          format: int32
          description: Defaultní příjem více žadatelů
          example: 60000
        expensesSumMin:
          type: integer
          format: int32
          description: Minimální celkové měsíční výdaje domácnosti
          example: 5000
        expensesSumDflt:
          type: integer
          format: int32
          description: Defaultní celkové měsíční výdaje domácnosti
          example: 20000
        instlSumMin:
          type: integer
          format: int32
          description: Minimální celková výše měsíčních splátek úvěrů domácnosti
          example: 0
        instlSumDflt:
          type: integer
          format: int32
          description: Defaultní celková výše měsíčních splátek úvěrů domácnosti
          example: 0
        currentAccountFlag:
          type: boolean
          example: true
          description: Příznak vedení platebního styku u RB
        maturityExtensionDflt:
          type: integer
          format: int32
          description: Defaultní prodloužení splatnosti úvěru u REFI
          example: 2
        maturityExtensionRangeIdDflt:
          type: string
          description: Identifikátor defaultního rozsahu prodloužení splatnosti u REFI
          example: "MAT0"
        coLivChildCntMin:
          type: integer
          description: Minimum rozsahu počtu dětí
          format: int32
          example: 0
        coLivChildCntMax:
          type: integer
          description: Maximum rozsahu počtu dětí
          format: int32
          example: 4
        loanSubjectFlag:
          type: boolean
          example: true
          description: Příznak potřeby zadání předmětu úvěru na žádosti
        acceptableOfferReduction:
          type: number
          format: double
          description: Maximální akceptovatelné snížení nabídky bez poradce v %
          example: 0.1
        loansToValues:
          type: array
          description: Maximální výše LTV  pro hypoteční business product subtyp
          items:
            $ref: '#/components/schemas/RMLLoanToValue'
        debtServicesToIncomes:
          type: array
          description: Maximální výše debt service to income pro hypoteční business product subtyp
          items:
            $ref: '#/components/schemas/RMLDebtServicesToIncome'
        fees:
          description: Poplatek účtovatelný k business product subtypu
          type: array
          items:
            $ref: '#/components/schemas/RMLFee'
        insurances:
          type: array
          description: Pojištění nabízené k business product subtypu
          items:
            $ref: '#/components/schemas/RMLInsurance'
        hycProductMappings:
          type: array
          description: Mapping hypotečního business product subtypu na Hypoclientí produkty
          items:
            $ref: '#/components/schemas/RMLHycProductMapping'
        interestVariancesForVariable:
          type: array
          description: Slevy/přírážky pro PROMĚNNOU na úrokové sazbě pro business product subtyp
          items:
            $ref: '#/components/schemas/RMLInterestVarianceForVariable'
        interestVariancesForInsurance:
          type: array
          description: Slevy/přírážky pro POJIŠTĚNÍ na úrokové sazbě pro business product subtyp
          items:
            $ref: '#/components/schemas/RMLInterestVarianceForInsurance'
        individualInterestVariances:
          type: array
          description: INDIVIDUÁLNÍ slevy/přirážky na úrokové sazbě pro business product subtyp
          items:
            $ref: '#/components/schemas/RMLIndividualInterestVariance'
        interestRates:
          type: array
          description: Přiřazení typu sazby k hypotečnímu business product subtypu
          items:
            $ref: '#/components/schemas/RMLInterestRate'
        design:
          $ref: '#/components/schemas/RMLDesign'
        loanPurposes:
          type: array
          description: Přiřazení povolených účelů k hypotéčnímu business product subtypu
          items:
            $ref: '#/components/schemas/RMLLoanPurpose'
        agreementProvisionsTemplateSets:
          type: array
          description: Sady šablon smluvních závazků a podmínek pro business product subtyp
          items:
            $ref: '#/components/schemas/RMLAgreementProvisionsTemplateSet'


    RMLDesign:
      type: object
      description: Design parametry zobrazení dlaždice produktu
      required:
        - name
        - reviewDescription
        - listOrder
      properties:
        name:
          type: string
          description: Název produktu na dlaždici
          example: "Hypotéka na bydlení"
        reviewDescription:
          type: string
          description: Detailní popis popis dlaždice produktu
          example: "Lze použít i k financování rekonstrukce nebo k vypořádání majetkových poměrů či ke koupi podílu v bytovém družstvu"
        listOrder:
          type: integer
          format: int32
          description: Pořadí zobrazení produktu
          example: 1

    RMLLoanToValue:
      type: object
      required:
        - ltvMin
        - ltvMax
        - ltvDflt
        - ageRange
      properties:
        ltvMin:
          type: number
          format: double
          description: Minimální výše LTV v %
          example: 0.70
        ltvMax:
          type: number
          format: double
          description: Maximální výše LTV v %
          example: 0.80
        ltvDflt:
          type: number
          format: double
          description: Defaultní výše LTV v %
          example: 0.70
        citizenshipCz:
          type: boolean
          description: Příznak českého občanství
          example: false
        incomeRange:
          $ref: '#/components/schemas/IncomeRange'
        ageRange:
          $ref: '#/components/schemas/AgeRange'
        upsellRange:
          $ref: '#/components/schemas/UpsellRange'
        maturityExtensionRange:
          $ref: '#/components/schemas/MaturityExtensionRange'

    IncomeRange:
      type: object
      required:
        - id
        - name
        - from
        - to
      properties:
        id:
          type: string
          description: Identifikátor rozsahu
          example: "INCNOLIMIT"
        name:
          type: string
          description: Pojmenování rozsahu
          example: "do 999.999.999 CZK"
        from:
          type: integer
          format: int32
          description: Spodní hranice rozsahu příjmu
          example: 0
        to:
          type: integer
          format: int32
          description: Horní hranice rozsahu příjmu
          example: 999999999

    UpsellRange:
      type: object
      required:
        - id
        - name
        - from
        - to
      properties:
        id:
          type: string
          description: Identifikátor rozsahu
          example: "UPS0"
        name:
          type: string
          description: Pojmenování rozsahu
          example: "bez limitu"
        from:
          type: integer
          format: int32
          description: Spodní hranice rozsahu navýšení částky úvěru
          example: 0
        to:
          type: integer
          format: int32
          description: Horní hranice rozsahu navýšení částky úvěru
          example: 999999999

    MaturityExtensionRange:
      type: object
      required:
        - id
        - name
        - from
        - to
      properties:
        id:
          type: string
          description: Identifikátor rozsahu
          example: "MATNOLIMIT"
        name:
          type: string
          description: Pojmenování rozsahu
          example: "bez limitu"
        from:
          type: integer
          format: int32
          description: Spodní hranice rozsahu navýšení splatnosti
          example: 0
        to:
          type: integer
          format: int32
          description: Horní hranice rozsahu navýšení splatnosti
          example: 999

    AgeRange:
      type: object
      required:
        - id
        - name
        - from
        - to
      properties:
        id:
          type: string
          description: Identifikátor rozsahu
          example: "AGE1836"
        name:
          type: string
          description: Pojmenování rozsahu
          example: "do 36 let"
        from:
          type: integer
          description: Spodní hranice rozsahu věku
          format: int32
          example: 18
        to:
          type: integer
          description: Horní hranice rozsahu věku
          format: int32
          example: 36

    RMLDebtServicesToIncome:
      type: object
      required:
        - dstiMax
        - dstiProdMax
        - dtiMax
        - ageRange
      properties:
        dstiMax:
          type: number
          format: double
          description: Maximání výše DSTI v %
          example: 0.30
        dstiProdMax:
          type: number
          format: double
          description: Maximální DSTI - Debt Service To Income v %
          example: 0.45
        dtiMax:
          type: number
          format: double
          description: Maximální DTI - Debt To Income
          example: 8.20
        citizenshipCz:
          type: boolean
          description: Příznak českého občanství
          example: false
        incomeRange:
          $ref: '#/components/schemas/IncomeRange'
        ageRange:
          $ref: '#/components/schemas/AgeRange'
        ltvRange:
          $ref: '#/components/schemas/LTVRange'
        upsellRange:
          $ref: '#/components/schemas/UpsellRange'
        maturityExtensionRange:
          $ref: '#/components/schemas/MaturityExtensionRange'

    LTVRange:
      type: object
      required:
        - id
        - name
        - from
        - to
      properties:
        id:
          type: string
          description: Identifikátor rozsahu
          example: "LTV70"
        name:
          type: string
          description: Pojmenování rozsahu
          example: "do 80%"
        from:
          type: number
          format: double
          description: Spodní hranice rozsahu LTV
          example: 0.00
        to:
          type: number
          format: double
          description: Horní hranice rozsahu LTV
          example: 0.80

    RMLFee:
      type: object
      required:
        - chargeAmt
        - typeId
        - typeName
        - dispoFlag
        - dstiFlag
        - rpsnFlag
        - chargeEvent
      properties:
        chargeAmt:
          type: number
          format: double
          example: 250.00
          description: Částka poplatku
        typeId:
          type: string
          example: "PPU"
          description: Identifikátor typu poplatku
        typeName:
          type: string
          example: "Poplatek za poskytnutí úvěru"
          description: Název typu poplatku
        dispoFlag:
          type: boolean
          example: true
          description: Příznak dispo
        dstiFlag:
          type: boolean
          example: true
          description: Příznak dsti
        rpsnFlag:
          type: boolean
          example: true
          description: Příznak, zda poplatek započítat do výpočtu RPSN
        chargeEvent:
          type: string
          enum: [ POP, EOM, PMA ]
          example: POP
          description: Událost, při které dochází k účtování poplatku

    RMLInsurance:
      type: object
      required:
        - insPremiumPct
        - typeId
        - typeName
        - defaultFlag
        - dispoFlag
        - dstiFlag
        - rpsnFlag
        - chargeEvent
      properties:
        insPremiumPct:
          type: number
          format: double
          example: 0.20
          description: Procento prémiového pojištění
        typeId:
          type: string
          example: "PPI"
          description: Identifikátor typu pojištění
        typeName:
          type: string
          example: "Pojištění schopnosti splácet"
          description: Název typu pojištění
        defaultFlag:
          type: boolean
          example: true
          description: Příznak výchozího pojištění
        dispoFlag:
          type: boolean
          example: true
          description: Příznak dispo
        dstiFlag:
          type: boolean
          example: true
          description: Příznak dsti
        rpsnFlag:
          type: boolean
          example: true
          description: Příznak, zda pojistné započítat do výpočtu RPSN
        chargeEvent:
          type: string
          enum: [ POP, EOM, PMA ]
          example: POP
          description: Událost, při které dochází k účtování pojištění
        variable:
          type: string
          example: "ANUITY"
          description: Proměnná určující výpočet ceny pojištění

    RMLHycProductMapping:
      type: object
      required:
        - hycProductTypeId
        - hycProductTypeName
        - ltvRange
        - ageRange
      properties:
        hycProductTypeId:
          type: string
          example: "112"
          description: Identifikátor typu produktu v systému hypoclient
        hycProductTypeName:
          type: string
          example: "Klasic do 70% LTV - pro mladé"
          description: Název typu produktu v systému hypoclient
        realtySelectionFlag:
          type: boolean
          example: true
          description: Příznak, že žadatel už má nemovitost vybranou
        ltvRange:
          $ref: '#/components/schemas/LTVRange'
        ageRange:
          $ref: '#/components/schemas/AgeRange'
        upsellRange:
          $ref: '#/components/schemas/UpsellRange'
        maturityExtensionRange:
          $ref: '#/components/schemas/MaturityExtensionRange'

    RMLInterestVarianceForVariable:
      type: object
      required:
        - variance
        - thresholdValue
        - condition
        - variable
        - typeId
        - typeName
        - expression
      properties:
        variance:
          type: number
          format: double
          example: 0.01
          description: Odchylka od úrokové sazby v %
        thresholdValue:
          type: number
          format: double
          example: 1000000.00
          description: Prahová hodnota pro započtení slevy/přirážky
        typeId:
          type: string
          example: "XYZ"
          description: Identifikátor typu slevy/přirážky
        typeName:
          type: string
          example: "Přirážka za úvěr do částky"
          description: Název typu slevy/přirážky
        expression:
          type: string
          example: "Variable=FIN_AMT"
          description: Název typu slevy/přirážky
        condition:
          type: string
          enum: [ GT, LT, EQ, NE, LE, GE ]
          example: LT
          description: Logická podmínka slevy/přirážky typu Variable
        variable:
          type: string
          example: "FIN_AMT"
          description: Proměnná určující výpočet slevy/přirážky

    RMLInterestVarianceForInsurance:
      type: object
      required:
        - variance
        - insuranceTypeId
        - typeId
        - typeName
        - expression
      properties:
        variance:
          type: number
          format: double
          example: -0.01
          description: Odchylka od úrokové sazby v %
        typeId:
          type: string
          example: "XYZ"
          description: Identifikátor typu slevy/přirážky
        typeName:
          type: string
          example: "Sleva za pojištění nemovitosti"
          description: Název typu slevy/přirážky
        expression:
          type: string
          example: "InsuranceType=PPI"
          description: Název typu slevy/přirážky
        insuranceTypeId:
          type: string
          example: "PPI"
          description: Identifikátor typu pojištění

    RMLIndividualInterestVariance:
      required:
        - maxVariance
        - minVariance
        - step
        - typeId
        - typeName
      type: object
      properties:
        minVariance:
          type: number
          description: Minimální individuální sleva/přirážka na úrokové sazbě v %
          example: 0.1
        maxVariance:
          type: number
          description: Maximální individuální sleva/přirážka na úrokové sazbě v %
          example: 0.5
        step:
          type: number
          description: Krok pro individuální slevu/přirážku na úrokové sazbě
          example: 0.1
        typeId:
          type: string
          description: Identifikátor typu slevy/přirážky
          example: XYZ
        typeName:
          type: string
          description: Název typu slevy/přirážky
          example: Přirážka za úvěr do částky
      description: Parametrizace individuálních slev/přirážek na úrokové sazbě pro variantu RML

    RMLInterestRate:
      type: object
      required:
        - typeId
        - typeName
        - class
        - offsetFlag
        - defaultFlag
      properties:
        typeId:
          type: string
          example: "US_CLAS_PROFI"
          description: Identifikátor typu úrokové sazby
        typeName:
          type: string
          example: "Fixed úroková sazba pro klasik a profi"
          description: Název typu úrokové sazby
        class:
          type: string
          enum: [ M, F, X, R ]
          example: X
          description: Třída úrokové sazby (M - Market, F - Float, X - Fixed, R - Reference Fixed)
        offsetFlag:
          type: boolean
          example: true
          description: Příznak offsetové sazby
        defaultFlag:
          type: boolean
          example: true
          description: Default flag - sazba bude přednastavena v kalkulačce
        defaultFixingPeriodTypeId:
          type: string
          example: "5Y"
          description: Výchozí fixing perioda v případě class = FIXED
        margin:
          type: number
          format: double
          example: 0.6
          description: úroková marže v % v případě class = FLOAT/REF_FIXED
        interestRatesByPeriodType:
          type: array
          items:
            $ref: '#/components/schemas/InterestRateByPeriodType'

    RMLLoanPurpose:
      type: object
      required:
        - id
        - name
        - primaryFlag
        - listOrder
        - drawdownPeriodDefFlag
        - drawdownPeriodMin
        - drawdownPeriodDef
      properties:
        id:
          type: string
          example: "1"
          description: Identifikátor účelu harmonizovaný s ADB(LOV)
        masterId:
          type: string
          example: "1"
          description: Reference na identifikátor hlavního účelu harmonizovaný s ADB(LOV) - hierarchie účelů
        name:
          type: string
          example: "Koupě nemovitosti"
          description: Název účelu
        summary:
          type: string
          example: "Byt, dům, pozemek, družstevní bydlení - podíl v družstvu"
          description: Shrnutí - popis účelu
        detailDescription:
          type: string
          example: "..."
          description: Detailní popis účelu
        primaryFlag:
          type: boolean
          example: true
          description: Příznak primárního účelu
        primaryPurposesPctMax:
          type: number
          format: double
          example: 0.3
          description: Maximální % z celkové výše primárních účelů
        purposeAmtMax:
          type: integer
          format: int64
          example: 1000000
          description: Maximalní výše účelu
        listOrder:
          type: integer
          format: int32
          example: 1
          description: Pořadí zobrazení
        drawdownPeriodDefFlag:
          type: boolean
          example: true
          description: Příznak defaultního účelu pro stanovení minimální, maximální a defaultní doby čerpání pro kalkulačky, které neznají seznam účelů
        drawdownPeriodMin:
          type: integer
          format: int32
          example: 1
          description: Minimální doba čerpání v měsících
        drawdownPeriodMax:
          type: integer
          format: int32
          example: 24
          description: Maximální doba čerpání v měsících
        drawdownPeriodDef:
          type: integer
          format: int32
          example: 1
          description: Defaultní doba čerpání v měsících

    RMLAgreementProvisionsTemplateSet:
      type: object
      required:
        - id
        - name
        - defaultFlag
      properties:
        id:
          type: string
          example: "CLAS_1"
          description: Identifikátor sady šablon smluvních podmínek
        name:
          type: string
          example: "Šablona klasik - základní"
          description: Název sady šablon smluvních podmínek
        defaultFlag:
          type: boolean
          example: true
          description: Příznak výchozí sady šablon smluvních podmínek pro business produkt subtyp

    InterestRateByPeriodType:
      type: object
      required:
        - interestRate
        - fixingPeriodTypeId
      properties:
        interestRate:
          type: number
          format: double
          example: 0.025
          description: Procentuální úroková sazba
        fixingPeriodTypeId:
          type: string
          example: "2Y"
          description: Fixing perioda


    BSLProductType:
      type: object
      description: BSL parametrizace (busProdTp)
      required:
        - busProdClassId
        - busProdTp
        - busProdTpName
        - subtypes
      properties:
        busProdTp:
          type: string
          maxLength: 255
          description: Typ produktu
        busProdTpName:
          type: string
          maxLength: 255
          description: Název produktu
        busProdClassId:
          type: string
          maxLength: 255
          description: Product Class ID z číselníku
        prodTypeParameters:
          $ref: '#/components/schemas/BSLTypeParametrization'
        subtypes:
          type: array
          items:
            $ref: '#/components/schemas/BSLSubtypeParametrization'

    BSLTypeParametrization:
      type: object
      description: Varianta BSL parametrizace (busProdTp)
      required:
        - validFrom
        - announcNum
        - commissionCoef
        - commissionAmt
        - infoNum
        - loanPenaltyCoef
        - loanPenaltyMin
        - loanPenaltyMax
        - tariffNum
        - termCond
        - legalContrVer
        - actNum
      properties:
        validFrom:
          type: string
          description: Platnost od data
          format: date
        validTo:
          type: string
          description: Platnost do data
          format: date
        announcNum:
          type: integer
          description: Číslo pro oznámení
          format: int32
          example: 247
        commissionCoef:
          type: number
          format: double
          description: Provize zprostředkovateli v % (numerický zápis)
          example: 0.024
        commissionAmt:
          type: integer
          description: Provize zprostředkovateli - částka
          format: int32
          example: 1000
        infoNum:
          type: integer
          description: Číslo pro informační přehled
          format: int32
          example: 181
        loanPenaltyCoef:
          type: number
          format: double
          description: Úhrada za nečerpání úvěru v % (numerický zápis)
          example: 0.01
        loanPenaltyMin:
          type: integer
          description: Minimální úhrada za nečerpání úvěru v Kč
          format: int32
          example: 0
        loanPenaltyMax:
          type: integer
          description: Maximální úhrada za nečerpání úvěru v Kč
          format: int32
          example: 10000
        tariffNum:
          type: integer
          description: Číslo aktuálního sazebníku úhrad
          format: int32
          example: 241
        termCond:
          type: string
          maxLength: 255
          description: Všeobecné obchodní podmínky
          example: '22'
        legalContrVer:
          type: string
          maxLength: 255
          description: Verze dokumentu Smlouvy o právním jednání
          example: '5'
        actNum:
          type: integer
          format: int32
          description: Zákon o stavebním spoření, podle kterého je smlouva uzavřena
          example: 3
        ecmPreContrInfoId:
          type: string
          maxLength: 255
          description: ECM ID pro dokument - Informace před uzavřením smlouvy
          example: '94214561'
        ecmInfoListId:
          type: string
          maxLength: 255
          description: ECM ID pro dokument - Informační přehled
          example: '77617024'
        ecmAnnouncId:
          type: string
          maxLength: 255
          description: ECM ID pro dokument - Oznámení
          example: '98440719'

    BSLSubtypeParametrization:
      type: object
      description: Varianta BSL parametrizace (busProdSubTp)
      required:
        - busProdSubTp
        - busProdSubTpName
        - validFrom
        - savingsContractFeeCoef
        - savingsContractFeeAmtMax
        - savingsMaintenanceFeeAmt
        - savingsStatementFeeAmt
        - monthlyDepositCoef
        - loanContractFeeCoef
        - loanContractFeeAmtMin
        - loanContractFeeAmtMax
        - loanMaintenanceFeeAmt
        - loanStatementFeeAmt
        - insFeeCoef
        - tariff
        - insNum
        - insName
        - loanPurposeCategories
      properties:
        busProdSubTp:
          type: string
          maxLength: 255
          description: Typ varianty produktu
        busProdSubTpName:
          type: string
          maxLength: 255
          description: Název varianty produktu
        busProdSubTpId:
          type: integer
          description: ID varianty produktu
          format: int64
        validFrom:
          type: string
          description: Platnost parametrizace OD
          format: date
        validTo:
          type: string
          description: Platnost parametrizace DO
          format: date
        savingsContractFeeCoef:
          type: number
          format: double
          description: Sazba Úhrada za sjednání spoření v % (numerický zápis)
          example: 0.00
        savingsContractFeeAmtMax:
          type: integer
          description: Jednorázový maximální poplatek za sjednání v Kč
          format: int32
          example: 15000
        savingsMaintenanceFeeAmt:
          type: integer
          description: Čtvrtletní úhrada za vedení účtu v Kč
          format: int32
          example: 0
        savingsStatementFeeAmt:
          type: integer
          description: Roční úhrada za výpis v Kč
          format: int32
          example: 0
        monthlyDepositCoef:
          type: number
          format: double
          description: Výše vkladu spoření v % (numerický zápis)
          example: 0.0010
        loanContractFeeCoef:
          type: number
          format: double
          description: Sazba Úhrada za sjednání úvěru v % (numerický zápis)
          example: 0.0100
        loanContractFeeAmtMin:
          type: integer
          description: Minimální jednorázový poplatek za sjednání v Kč
          format: int32
          example: 1000
        loanContractFeeAmtMax:
          type: integer
          description: Maximální jednorázový poplatek za sjednání v Kč
          format: int32
          example: 10000
        loanMaintenanceFeeAmt:
          type: integer
          description: Čtvrtletní úhrada za vedení účtu v Kč
          format: int32
          example: 106
        loanStatementFeeAmt:
          type: integer
          description: Roční úhrada za výpis z účtu v Kč
          format: int32
          example: 0
        insFeeCoef:
          type: number
          format: double
          description: Sazba pojištění v % (numerický zápis)
          example: 0.0950
        tariff:
          type: string
          maxLength: 255
          description: Tarif pro odkaz do RSTS
          example: '136'
        insNum:
          type: string
          maxLength: 255
          description: Číslo pojistné smlouvy
          example: '1080500095'
        insName:
          type: string
          maxLength: 255
          description: Název smluvního pojištění
          example: 'Pojištění schopnosti splácet nezajištěný úvěr'
        insVarTp:
          type: integer
          description: Kód varianty pojištění
          format: int32
          example: 15
        ecmInsContrId:
          type: string
          maxLength: 255
          description: ECM ID pro dokument - Pojistná smlouva
          example: '96654589'
        loanPurposeCategories:
          type: array
          items:
            $ref: '#/components/schemas/BSLSubtypePurposeCategory'

    BSLSubtypePurposeCategory:
      type: object
      description: Varianta BSL parametrizace (busProdSubTp)
      required:
        - purposeCategory
        - loanValueMin
        - loanValueMax
        - loanDurationMin
        - loanDurationMax
        - interestRatePenalty
        - loanValueDflt
        - loanDurationDflt
        - upsellAmtDflt
      properties:
        purposeCategory:
          $ref: '#/components/schemas/LoanPurposeCategory'
        loanValueMin:
          type: integer
          format: int32
          description: Minimální výše úvěru
          example: 500000
        loanValueMax:
          type: integer
          format: int32
          description: Maximální výše úvěru
          example: 50000000
        loanDurationMax:
          type: integer
          format: int32
          description: Maximální délka úvěru v měsících
          example: 24
        loanDurationMin:
          type: integer
          format: int32
          description: Minimální délka úvěru v měsících
          example: 12
        interestRatePenalty:
          type: number
          format: double
          example: 0.08
          description: Sankční procentuální úroková sazba
        loanValueDflt:
          type: integer
          format: int32
          description: Přednastavená výše úvěru
          example: 600000
        loanDurationDflt:
          type: integer
          format: int32
          description: Přednastavená délka úvěru v měsících
          example: 240
        upsellAmtDflt:
          type: integer
          format: int32
          description: Přednastavené navýšení úvěru
          example: 100000
        interestRates:
          type: array
          description: Přehled typů úrokových sazeb úvěru
          items:
            $ref: '#/components/schemas/BSLInterestRate'
        design:
          $ref: '#/components/schemas/BSLDesign'
        interestVariances:
          type: array
          description: Slevy/přírážky pro PROMĚNNOU na úrokové sazbě pro business product subtyp a účel
          items:
            $ref: '#/components/schemas/BSLInterestVariance'

    LoanPurposeCategory:
      type: object
      description: Kategorie účelu úvěru
      required:
        - id
        - name
      properties:
        id:
          type: string
          description: Identifikátor kategorie účelu (kód)
          example: "REKO"
        name:
          type: string
          description: Název kategorie účelu - pojmenování
          example: "Rekonstrukce bydlení"

    BSLDesign:
      type: object
      description: Design parametry zobrazení dlaždice produktu
      required:
        - name
        - reviewDescription
        - listOrder
        - isVisibleOnStart
      properties:
        name:
          type: string
          description: Název produktu na dlaždici
          example: "Modernizace"
        reviewDescription:
          type: string
          description: Detailní popis popis dlaždice produktu
          example: "Potřebujete novou kuchyň, koupelnu nebo třeba fotovoltaiku?"
        listOrder:
          type: integer
          format: int32
          description: Pořadí zobrazení produktu
          example: 1
        isVisibleOnStart:
          type: boolean
          example: true
          description: Příznak viditelnosti dlaždice na začátku procesu žádosti

    BSLInterestRate:
      type: object
      required:
        - typeId
        - typeName
        - class
        - offsetFlag
        - defaultFlag
      properties:
        typeId:
          type: string
          example: "US_CLAS_PROFI"
          description: Identifikátor typu úrokové sazby
        typeName:
          type: string
          example: "Fixed úroková sazba pro klasik a profi"
          description: Název typu úrokové sazby
        class:
          type: string
          enum: [ M, F, X, R ]
          example: X
          description: Třída úrokové sazby (M - Market, F - Float, X - Fixed, R - Reference Fixed)
        offsetFlag:
          type: boolean
          example: true
          description: Příznak offsetové sazby
        defaultFlag:
          type: boolean
          example: true
          description: Default flag - sazba bude přednastavena v kalkulačce
        defaultFixingPeriodTypeId:
          type: string
          example: "2Y"
          description: Výchozí fixing perioda v případě class = FIXED
        margin:
          type: number
          format: double
          example: 0.6
          description: úroková marže v % v případě class = FLOAT/REF_FIXED
        interestRatesByPeriodTypeAndLoanVal:
          description: Přehled úrokových sazeb dle periody
          type: array
          items:
            $ref: '#/components/schemas/InterestRateByPeriodTypeAndLoanVal'

    BSLInterestVariance:
      type: object
      required:
        - variance
        - thresholdValue
        - condition
        - variable
        - typeId
        - typeName
      properties:
        variance:
          type: number
          format: double
          example: 0.01
          description: Odchylka od úrokové sazby v %
        thresholdValue:
          type: number
          format: double
          example: 1000000.00
          description: Prahová hodnota pro započtení slevy/přirážky
        typeId:
          type: string
          example: "XYZ"
          description: Identifikátor typu slevy/přirážky
        typeName:
          type: string
          example: "Přirážka za úvěr do částky"
          description: Název typu slevy/přirážky
        expression:
          type: string
          example: "Variable=FIN_AMT"
          description: Název typu slevy/přirážky
        condition:
          type: string
          enum: [ GT, LT, EQ, NE, LE, GE ]
          example: LT
          description: Logická podmínka slevy/přirážky typu Variable
        variable:
          type: string
          example: "FIN_AMT"
          description: Proměnná určující výpočet slevy/přirážky

    InterestRateByPeriodTypeAndLoanVal:
      type: object
      required:
        - interestRate
        - fixingPeriodTypeId
        - loanValueFrom
        - loanValueTo
      properties:
        interestRate:
          type: number
          format: double
          example: 0.025
          description: Procentuální úroková sazba
        fixingPeriodTypeId:
          type: string
          example: "2Y"
          description: Fixing perioda
        loanValueFrom:
          type: integer
          format: int32
          description: Spodní hranice výše úvěru pro kterou platí úroková sazba, včetně
          example: 0
        loanValueTo:
          type: integer
          format: int32
          description: Horní hranice výše úvěru pro kterou platí úroková sazba, včetně
          example: 700000


    RODProductType:
      type: object
      description: ROD parametrizace (busProdTp)
      required:
        - busProdClassId
        - busProdTp
        - busProdTpName
        - subtypes
      properties:
        busProdTp:
          type: string
          description: Typ produktu
        busProdTpName:
          type: string
          description: Název produktu
        busProdClassId:
          type: string
          description: Class ID z číselníku
        subtypes:
          type: array
          items:
            $ref: '#/components/schemas/RODSubtypeParametrization'

    RODSubtypeParametrization:
      type: object
      description: Varianta ROD parametrizace (busProdSubTp)
      required:
        - busProdSubTp
        - busProdSubTpName
        - busProdSubTpId
        - validFrom
        - MPROD
        - interestProducts
      properties:
        busProdSubTp:
          type: string
          description: Typ varianty produktu
        busProdSubTpName:
          type: string
          description: Název varianty produktu
        busProdSubTpId:
          type: integer
          description: ID varianty produktu
          format: int64
        validFrom:
          type: string
          description: Platnost parametrizace OD
          format: date
        validTo:
          type: string
          description: Platnost parametrizace DO
          format: date
        MPROD:
          type: integer
          description: Identifikace produktu v marketingovém katalogu
          format: int32
          example: 2114001
        interestProducts:
          type: array
          items:
            $ref: '#/components/schemas/RODInterestProductParametrization'

    RODInterestProductParametrization:
      type: object
      description: Úročící produkt daného typu kontokorentu
      required:
        - productInterestId
        - productInterestCode
        - isDefault
        - interestRate
        - interestFreeReserveAmount
        - minLimit
        - maxLimit
        - defaultLimit
        - limitMinIncrease1
        - limitMinIncrease2
        - limitIncreaseDivider
        - feeInit
        - feeMonthlyUtilization
      properties:
        productInterestId:
          type: number
          format: int64
          example: 1
          description: Identifikátor parametrizace úročícího produktu
        productInterestCode:
          type: string
          example: "PDPLUS"
          description: Kód úročícího produktu
        productInterestCodeId:
          type: string
          example: "431"
          description: Identifikátor úročícího produktu
        isDefault:
          type: boolean
          example: true
          description: Příznak defaultního úročícího produktu pro ROD produktovou řadu (busProdSubTp)
        interestRate:
          type: number
          format: double
          example: 0.219
          description: Základní úrok mimo bezúročné pásmo
        interestFreeReserveAmount:
          type: integer
          format: int64
          example: 1000
          description: Výše bezúročné rezervy
        minLimit:
          type: integer
          format: int64
          example: 1000
          description: Minimální rámec čerpání
        maxLimit:
          type: integer
          format: int64
          example: 250000
          description: Maximální rámec čerpání
        defaultLimit:
          type: integer
          format: int64
          example: 10000
          description: Defaultní částka čerpání
        limitMinIncrease1:
          type: integer
          format: int64
          example: 5000
          description: Minimální změna částky do částky limitIncreaseDivider
        limitMinIncrease2:
          type: integer
          format: int64
          example: 10000
          description: Minimální změna částky nad částku limitIncreaseDivider
        limitIncreaseDivider:
          type: integer
          format: int64
          example: 20000
          description: Zlomová částka čerpání určující délku kroku rámce při pohybu posuvníku na obrazovce
        feeInit:
          type: integer
          format: int32
          example: 200
          description: Poplatek v CZK za zřízení a obnovu KTK
        feeMonthlyUtilization:
          type: integer
          format: int32
          example: 90
          description: Poplatek v CZK za využití KTK v daném kalendářním měsíci

    AgreementProvisionTemplateSet:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          example: "CLAS_1"
          description: Identifikátor sady šablon smluvních podmínek
        name:
          type: string
          example: "Šablona klasik - základní"
          description: Název sady šablon smluvních podmínek
        groups:
          type: array
          items:
            $ref: '#/components/schemas/AgreementProvisionGroupAssignment'

    AgreementProvisionGroupAssignment:
      type: object
      required:
        - id
        - name
        - listOrder
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Identifikátor skupiny šablon smluvních podmínek
        name:
          type: string
          example: "Čerpání do Kč <AMOUNT>"
          description: Název skupiny šablon smluvních podmínek
        listOrder:
          type: integer
          format: int32
          example: 1
          description: Pořadí skupiny smluvních podmínek v sadě šablon
        agreementProvisions:
          type: array
          items:
            $ref: '#/components/schemas/AgreementProvision'

    AgreementProvision:
      type: object
      required:
        - defaultSetFlag
        - templateCode
      properties:
        defaultSetFlag:
          type: boolean
          example: true
          description: Příznak přednastavení smluvní podmínky
        templateCode:
          type: string
          example: "1A10"
          description: Kód šablony smluvní podmínky
        loanPurchFromDevProjectFlag:
          type: boolean
          example: false
          description: Koupě z developerského projektu?
        loanTrancheCount:
          type: integer
          format: int32
          example: 1
          description: Počet úvěrových tranší
        loanReverseMortFlag:
          type: boolean
          example: false
          description: Hypotéka Naruby?
        loanMortForYoungFlag:
          type: boolean
          example: false
          description: Hypotéka pro mladé?
        loanUniquaRltInsurFlag:
          type: boolean
          example: false
          description: Pojištění nemovitosti od Uniqua?
        loanUniquaPayProtctnInsurFlag:
          type: boolean
          example: false
          description: Pojištění schopnosti splácet od Uniqua?
        pledgeDrawAfterRegnOfMortFlag:
          type: boolean
          example: false
          description: Čerpání po zápisu zástavního práva?
        pledgePurchFromDevProjectFlag:
          type: boolean
          example: false
          description: Koupě z developerského projektu?
        pledgeUniquaRltInsurFlag:
          type: boolean
          example: false
          description: Pojištění nemovitosti od Uniqua?
        pledgeFloodInsurFlag:
          type: boolean
          example: false
          description: Pojištění proti povodním?
        pledgeNoEncumbranceFlag:
          type: boolean
          example: false
          description: Zákaz zatížení na listu vlastnictví?
        pledgeSellerMortPaidFlag:
          type: boolean
          example: false
          description: Vyplácíme z kupní ceny úvěr prodávajícího?
        loanPurpose:
          $ref: '#/components/schemas/LoanPurpose'

    LoanPurpose:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          example: "1"
          description: Identifikátor účelu harmonizovaný s ADB(LOV)
        name:
          type: string
          example: "Koupě nemovitosti"
          description: Název účelu
        summary:
          type: string
          example: "Byt, dům, pozemek, družstevní bydlení - podíl v družstvu"
          description: Shrnutí - popis účelu
        detailDescription:
          type: string
          example: "..."
          description: Detailní popis účelu

    AgreementProvisionGroup:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: integer
          format: int64
          example: 1
          description: Identifikátor skupiny šablon smluvních podmínek
        name:
          type: string
          example: "Čerpání do Kč <AMOUNT>"
          description: Název skupiny šablon smluvních podmínek
        templates:
          type: array
          items:
            $ref: '#/components/schemas/AgreementProvisionTemplateVersion'

    AgreementProvisionTemplateVersion:
      type: object
      required:
        - id
        - code
        - version
        - latestVersionFlag
        - name
        - validFrom
        - validTo
        - text
      properties:
        id:
          type: integer
          format: int64
          example: 190535851
          description: Identifikátor šablony smluvních podmínek
        code:
          type: string
          example: "0K2"
          description: Kód šablony smluvních podmínek (PK part n.1)
        version:
          type: integer
          format: int32
          example: 104
          description: Verze Šablony smluvních podmínek (PK part n.2)
        latestVersionFlag:
          type: boolean
          example: true
          description: Příznak nejnovější verze šablony smluvních podmínek
        name:
          type: string
          example: "Poznámka k čerpáním migrovaného úvěru"
          description: Název šablony smluvních podmínek
        validFrom:
          type: string
          description: Platnost verze šablony OD data
          format: date
        validTo:
          type: string
          description: Platnost verze šablony DO data
          format: date
        text:
          type: string
          example: "<VOLNY_TEXT2><VOLNY_TEXT>"
          description: Textace šablony smluvních podmínek
        objectType:
          $ref: '#/components/schemas/AgreementProvisionObjectType'
        dataEntries:
          type: array
          items:
            $ref: '#/components/schemas/AgreementProvisionDataEntry'

    AgreementProvisionObjectType:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
          example: "M"
          description: Identifikátor typu šablony smluvních podmínek
        name:
          type: string
          example: "Zástava nemovitosti"
          description: Název typu šablony smluvních podmínek

    AgreementProvisionDataEntry:
      type: object
      required:
        - code
        - name
        - userDataFlag
        - inputMask
      properties:
        code:
          type: string
          example: "VOLNY_TEXT"
          description: Kód vstupního pole
        name:
          type: string
          example: "Volný text (4000 znaků) "
          description: Název vstupního pole
        userDataFlag:
          type: boolean
          example: true
          description: Vstup zadávaný uživatelem?
        inputMask:
          type: string
          example: "DECIMAL_NUMBER"
          description: Validace/Formátování vstupu podle typu zadávaných dat
        listOrder:
          type: integer
          format: int32
          example: 1
          description: Pořadí zobrazení vstupního pole v šabloně smluvních podmínek

    Error:
      type: object
      description: |
        ## Error Reference Model
        Error model to hold the data which help to identify error cause and provide additional tracking information.
      required:
        - requestId
        - correlationId
        - status
        - reasons
      properties:
        requestId:
          $ref: "#/components/schemas/RequestId"
        correlationId:
          $ref: "#/components/schemas/CorrelationId"
        status:
          $ref: "#/components/schemas/ErrorStatus"
        reasons:
          $ref: "#/components/schemas/ErrorReasons"

    ErrorReasons:
      type: array
      items:
        $ref: "#/components/schemas/ErrorReason"

    ErrorReason:
      type: object
      description: Additional information about caused error.
      required:
        - code
        - message
        - severity
      properties:
        code:
          $ref: "#/components/schemas/ErrorCode"
        severity:
          $ref: "#/components/schemas/ErrorSeverity"
        message:
          $ref: "#/components/schemas/ErrorMessage"

    RequestId:
      type: string
      description: A unique UUID of a specific request or RBCZ Message ID convention (application code + 21 numbers). The value shoud be obtained from X-Request-Id header.
      example: 'MCH001207641782552682875'

    CorrelationId:
      type: string
      description: A unique UUID of the entire communication identification. The value shoud be obtained from X-Correlation-Id header.
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'

    ErrorStatus:
      type: integer
      format: int32
      description: An HTTP status code. If a different protocol than HTTP is used, we should map and transform the error to HTTP protocol style.
      minimum: 100
      maximum: 600
      exclusiveMaximum: true
      example: 400

    ErrorCode:
      type: string
      description: Error code or Backend error code returned from backend service.
      example: "XYZ8000"

    ErrorSeverity:
      type: string
      description: Shows if the reason for an unexpected situation is critical or just information.
      enum:
        - WARN
        - ERROR
        - FATAL
      example: "ERROR"

    ErrorMessage:
      type: string
      description: Human-readable message in user-requested language.
      example: "Payment rejected. Missing creditor iban."