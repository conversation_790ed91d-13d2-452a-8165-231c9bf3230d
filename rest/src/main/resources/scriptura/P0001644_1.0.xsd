<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://rb.cz/cdm/dds/crm/cpa/P0001540/1.0"
           targetNamespace="http://rb.cz/cdm/dds/crm/cpa/P0001540/1.0"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           version="1.0.0">
    <xs:element name="P0001644">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="questr">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="sbmDate" type="xs:date"/>
                            <xs:element name="quests" maxOccurs="unbounded">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="questId" type="xs:string"/>
                                        <xs:element name="rsp" type="xs:boolean"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="applDetails">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="insNumber" type="xs:string"/>
                            <xs:element name="insName" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="agent">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="agentName" type="xs:string"/>
                            <xs:element name="address">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="agentAddStreet" type="xs:string"/>
                                        <xs:element name="agentAddZip" type="xs:string"/>
                                        <xs:element name="agentAddCity" type="xs:string"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="role" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="repre">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="repreFirstName" type="xs:string" minOccurs="0"/>
                            <xs:element name="repreFamilyName" type="xs:string" minOccurs="0"/>
                            <xs:element name="address" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="repreAddStreet" type="xs:string" minOccurs="0"/>
                                        <xs:element name="repreAddNum" type="xs:string" minOccurs="0"/>
                                        <xs:element name="repreAddZip" type="xs:string" minOccurs="0"/>
                                        <xs:element name="repreAddCity" type="xs:string" minOccurs="0"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="persons">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="clientFirstName" type="xs:string"/>
                            <xs:element name="clientFamilyName" type="xs:string"/>
                            <xs:element name="permAddr">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="permAddStreet" type="xs:string"/>
                                        <xs:element name="permAddNum" type="xs:string"/>
                                        <xs:element name="permAddZip" type="xs:string"/>
                                        <xs:element name="permAddCity" type="xs:string"/>
                                        <xs:element name="permAddCountry" type="xs:string"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="regEmail" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>