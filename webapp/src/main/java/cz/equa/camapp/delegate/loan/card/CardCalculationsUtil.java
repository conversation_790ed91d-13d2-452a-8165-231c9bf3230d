package cz.equa.camapp.delegate.loan.card;

import cz.equa.camapp.lovs.LovLang;
import cz.equa.camapp.lovs.LovNotFoundException;
import cz.equa.camapp.lovs.LovPos;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.manager.RccManager;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.CreditCardApplDTO;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.NewApplVariantDTO;
import cz.rb.las.parametrization.model.RCCPriceVariantParametrization;
import cz.rb.las.parametrization.model.RCCSubtypeParametrization;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;

public class CardCalculationsUtil {

    public static final String CARD_DISTRIBUTION_TYPE_BRANCH_INTERNAL = "BRANCH_INTERNAL";
    public static final String CARD_DISTRIBUTION_TYPE_BRANCH_EXTERNAL = "BRANCH_EXTERNAL";

    private CardCalculationsUtil() {
        // static class should not be instanciated
    }
    
    public static BigDecimal getCashlimit(NewApplVariantDTO aprVariant, RCCSubtypeParametrization rccParam) throws ServiceException {
        String cashLimitPctTxt = rccParam.getCashLimitPct();
        String cashLimitMaxTxt = rccParam.getCashLimitMax();
        String cashLimitMinTxt = rccParam.getCashLimitMin();
        if (StringUtils.isEmpty(cashLimitPctTxt) || StringUtils.isEmpty(cashLimitMinTxt) || StringUtils.isEmpty(cashLimitMaxTxt)) {
            throw new ServiceException("cashLimitPct or cashLimitMax or cashLimitMin is empty in parametrization");
        }
        BigDecimal cashLimitPct = new BigDecimal(cashLimitPctTxt);
        BigDecimal cashLimitMax = new BigDecimal(cashLimitMaxTxt);
        BigDecimal cashLimitMin = new BigDecimal(cashLimitMinTxt);
        
        BigDecimal cashLimitForCard = aprVariant.getFinaAmt().multiply(cashLimitPct).divide(BigDecimal.valueOf(100));
        
        if (cashLimitForCard.compareTo(cashLimitMax) > 0) {
            return cashLimitMax;
        }
        if (cashLimitForCard.compareTo(cashLimitMin) < 0) {
            return cashLimitMin;
        }
        return cashLimitForCard;
    }
    
    public static BigInteger getCashlimitInBigInteger(NewApplVariantDTO aprVariant, RCCSubtypeParametrization rccParam) throws ServiceException {
        return getCashlimit(aprVariant, rccParam).toBigInteger();
    }

    public static String getBranchCode(CreditCardApplDTO creditCardAppl, LovLang language, LovManager lovManager) {
        String output = "5";

        if (creditCardAppl.getCardDistributionType().equals(CARD_DISTRIBUTION_TYPE_BRANCH_INTERNAL)) {
            output = creditCardAppl.getCardDistributionInternalBranchId();
        } else if (creditCardAppl.getCardDistributionType().equals(CARD_DISTRIBUTION_TYPE_BRANCH_EXTERNAL)) {
            output = getExternalBranchInternalCode(creditCardAppl, language, lovManager);
        }

        output = String.valueOf(Integer.parseInt(output));
        return output;
    }

    public static String getExternalBranchInternalCode(CreditCardApplDTO creditCardAppl, LovLang language, LovManager lovManager) {
        String detailKey = "INTERNAL_CODE";
        String branchId = creditCardAppl.getCardDistributionExternalBranchId();
        LovPos lovPos = lovManager.getLov(new LovPos(branchId), language);

        if (lovPos == null) {
            throw new LovNotFoundException("Cannot find LovPos lov with code " + branchId);
        }
        return lovPos.getLovDetail().get(detailKey);
    }
    
    public static Integer getAffinityGroup(LoanApplicationDTO loanApplicationDTO, RccManager rccManager) {
        RCCPriceVariantParametrization priceVariantParametrization = rccManager.getPriceVariantParametrization(loanApplicationDTO);
        if (priceVariantParametrization == null) {
            return null;
        }
        return priceVariantParametrization.getRpCAffinityGroup();
    }
    
    public static String getAffinityGroupString(LoanApplicationDTO loanApplicationDTO, RccManager rccManager) {
        Integer affinityGroup = getAffinityGroup(loanApplicationDTO, rccManager);
        if (affinityGroup == null) {
            return null;
        }
        return affinityGroup.toString();
    }
    
    public static Integer getAffinityGroupInteger(LoanApplicationDTO loanApplicationDTO, RccManager rccManager) {
        return getAffinityGroup(loanApplicationDTO, rccManager);
    }
}
