package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.model.GeneratedDocumentInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static cz.equa.camapp.rest.service.DmsService.DOCUMENT_ID_TYPE_EXTERNALID;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgCancelDocuments")
public class CancelDocuments extends MtgAbstractWorker {

    private static final List<String> DOCUMENT_CLASS_FILTER = List.of(
            DocumentType.P0001427.name()
    );
    private final DmsService dmsService;

    @Autowired
    public CancelDocuments(ApplicationService applicationService, DmsService dmsService) {
        super(applicationService);
        this.dmsService = dmsService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();

        GetDocumentListDto response = dmsService.getDocumentListByApplicationId(pv.getBusApplId(), DOCUMENT_CLASS_FILTER, pv.getCorrelationId());

        if (response == null || response.getDocumentList() == null) {
            log.info("No documents to cancel.");
            return;
        }

        List<GetDocumentListDto.Document> documents = response.getDocumentList();
        setProcessVariable(ProcessVariables.GENERATED_DOCUMENTS, createGeneratedDocumentsDTO(documents));
        for (GetDocumentListDto.Document document : documents) {
            String documentSmState = document.getDocument().getSmStateId();
            String documentId = document.getDocument().getDocumentId();
            if ("CLCFD_CLCFD_FULL".equals(documentSmState)) {
                dmsService.documentRenderUpdateDecline(documentId, DmsService.getDocumentIdType(documentId), pv.getCorrelationId());
            } else if ("CLCFD_EXPIRED".equals(documentSmState)) {
                dmsService.documentRenderUpdateDestroy(documentId, DmsService.getDocumentIdType(documentId), pv.getCorrelationId());
            } else if (StringUtils.isNotBlank(documentSmState)) {
                dmsService.documentRenderUpdateCancel(documentId, DmsService.getDocumentIdType(documentId), pv.getCorrelationId());
            }
        }
    }

    private GeneratedDocumentsDTO createGeneratedDocumentsDTO(List<GetDocumentListDto.Document> documents) {
        GeneratedDocumentsDTO docs = new GeneratedDocumentsDTO();
        for (GetDocumentListDto.Document doc : documents) {
            docs.addObjectId(doc.getDocument().getDocumentId(),
                    new GeneratedDocumentInfoDTO(doc.getDocument().getDocumentId(), doc.getDocument().getDocumentTypeId()));
        }
        return docs;
    }
}
