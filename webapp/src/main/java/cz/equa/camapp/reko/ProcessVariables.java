package cz.equa.camapp.reko;

import com.fasterxml.jackson.annotation.JsonAlias;
import cz.equa.camapp.lovs.LovLang;
import cz.equa.camapp.model.DealerInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.contact.ContactUploadDTO;
import cz.equa.camapp.model.document.ClientApprovalDoc;
import cz.equa.camapp.model.document.ClientApprovalDocs;
import cz.equa.camapp.model.party.BuildingPartyRstsDTO;
import cz.equa.camapp.model.party.UpdatePartyDto;
import cz.equa.camapp.rest.model.RekoCalculationResponseDTO;
import cz.equa.camapp.rest.model.cus.party.DocumentsDtoList;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.service.product_service.PersonCLInfoDTO;
import cz.rb.las.parametrization.model.BSLProductType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProcessVariables {

    public static final String REKO_BUS_PROD_TP = "REKO";

    //deprecated - use ApplicationState insteda of ProcessState!
    @Deprecated
    public static final String PROCESS_STATE = "processState";
    @Deprecated
    private ApplicationState processState;

    public static final String CORRELATION_ID = "X-Correlation-Id";
    @JsonAlias("X-Correlation-Id")
    private String correlationId;

    public static final String CL_CONTEXT_ID = "X-Cl-Context-Id";
    @JsonAlias("X-Cl-Context-Id")
    private String clContextId;

    public static final String APPL_KEY = "applKey";
    private Long applKey;

    public static final String BUS_APPL_ID = "busApplId";
    private String busApplId;

    // Siebel Id of applicant
    public static final String CLIENT_ID = "clientId";
    private String clientId;

    // CIBIS Id of applicant
    public static final String RSTS_CLIENT_ID = "rstsClientId";
    private String rstsClientId;

    public static final String APPLICATION_STATE = "applicationState";
    private ApplicationState applicationState;

    public static final String IS_INCOME = "isIncome";
    private String isIncome;

    public static final String IS_OBLIFATION = "isObligation";
    private String isObligation;

    public static final String IS_OWNER = "isOwner";
    private String isOwner;

    public static final String REQUESTED_VARIANTS = "requestedVariants";
    private String requestedVariants;

    public static final String GET_DETAIL_RESULT = "getDetailResultDto";
    private GetDetailResultDto getDetailResultDto;

    public static final String GET_PARTY_DOCUMENTS_RESULT = "getPartyDocumentsDto";
    private DocumentsDtoList getPartyDocumentsDto;

    public static final String PARTY_ID = "partyId";
    private String partyId;

    public static final String PARTY_RSTS = "partyRsts";
    private BuildingPartyRstsDTO partyRsts;

    public static final String PARTY_UPDATE = "partyUpdate";
    private UpdatePartyDto partyUpdate;

    public static final String NEED_EDIT_SBL_PARTY = "needEditSblParty";
    private Boolean needEditSblParty;

    public static final String NEED_UPLOAD_CONTACTS = "needUploadContacts";
    private Boolean needUploadContacts;

    public static final String CONTACT_UPLOAD = "contactUpload";
    private ContactUploadDTO contactUpload;

    public static final String NEED_EDIT_ADB_PARTY = "needEditAdbParty";
    private Boolean needEditAdbParty;

    public static final String BUILDING_LOAN_APPLICATION = "buildingLoanApplication";
    private BuildingLoanApplicationDTO buildingLoanApplication;

    public static final String LOAN_APPLICATION_OWNER = "loanApplicationOwner";
    private BuildingLoanApplicationOwnerDTO loanApplicationOwner;

    public static final String LOAN_APPLICATION_INCOMES = "loanApplicationIncomes";
    private PtIncomeRstsListDTO loanApplicationIncomes;

    public static final String BUILDING_LOAN_APPL_VARIANTS = "buildingLoanApplVariants";
    private BuildingApplVariantsDTO buildingLoanApplVariants;

    public static final String BUILDING_OBLIGATIONS = "buildingObligations";
    private BuildingObligationsDTO buildingObligations;

    public static final String PROCESS_TP = "processTp";
    private String processTp;

    public static final String LOAD_APPLICATION_DEALER_INFO = "loanApplicationDealerInfo";
    private DealerInfoDTO loanApplicationDealerInfo;

    public static final String IS_FAKE_CLIENT = "firstTouchPointFakeClient";
    private Boolean fakeClient;

    public static final String CHECK_APPL_USER_RIGHTS_RESULT = "checkApplUserRightsResult";
    private Boolean checkApplUserRightsResult;

    public static final String INSURANCE_SELECTED = "insuranceSelected";
    private Boolean insuranceSelected;

    public static final String INSURANCE_REMOVED = "insuranceRemoved";
    private Boolean insuranceRemoved;

    public static final String EVENT_TYPE = "eventType";
    private String eventType;

    public static final String CALL_APPROVAL_RESULT = "callApprovalResult";
    private String callApprovalResult;

    public static final String CLIENT_HAS_AML_QUESTIONAIRE = "clientHasAmlQuestionaire";
    private Boolean clientHasAmlQuestionaire;

    public static final String CLIENT_HAS_ACCOUNT = "clientHasAccount";
    private Boolean clientHasAccount;

    public static final String REJECTION_STATE = "rejectionState";
    private String rejectionState;

    public static final String TEMPLATE = "template";
    private String template;

    public static final String SUPRESS_ERROR_AND_CONTINUE = "supressErrorAndContinue";
    private String suppressErrorAndContinue;

    public static final String SM_STATE = "smState";
    private String smState;

    public static final String ORIGINAL_STATE = "originalState";
    private ApplicationState originalState;

    public static final String GENERATED_DOCUMENTS = "generatedDocuments";
    private GeneratedDocumentsDTO generatedDocuments = new GeneratedDocumentsDTO();

    public static final String OPPORTUNITY_ID = "opportunityId";
    private String opportunityId;

    public static final String CLOSE_OUT_REASON = "closeOutReason";
    private String closeOutReason;

    public static final String DOCUMENTS_TO_SIGN = "documentsToSign";
    private DocumentsToSignListDTO documentsToSign = new DocumentsToSignListDTO();

    public static final String REKO_RPSN_CALCULATIONS = "rekoRpsnCalculations";
    private RekoCalculationResponseDTO rekoRpsnCalculations;

    @Deprecated(since = "6.2.2025")
    public static final String REKO_PARAMETRIZATION_BSL = "rekoParametrizationBSL";
    @Deprecated(since = "6.2.2025")
    private BSLProductType rekoParametrizationBSL;

    public static final String LOAN_APPLICATION_PERSON_INFO = "loanApplicationPersonInfo";
    private PersonCLInfoDTO loanApplicationPersonInfo;

    public static final String LOAN_APPLICATION_SIGNING_CHANNEL = "loanApplicationSigningChannel";
    private String loanApplicationSigningChannel;
    public static final String BRANCH_CODE = "clONBChannelChange_PosId";
    private String clONBChannelChange_PosId;
    public static final String CHANNEL = "clONBChannelChange_channel";
    private String clONBChannelChange_channel;
    public static final String CL_ONB_STAT_CHANGE_APPLICATION_STATE = "clONBStatusChange_applicationState";
    private String clONBStatusChange_applicationState;
    public static final String CL_ONB_STAT_CHANGE_APPLICATION_SUB_STATE = "clONBStatusChange_applicationSubState";
    private String clONBStatusChange_applicationSubState;
    public static final String CL_ONB_STAT_CHANGE_APPLICATION_REASON = "clONBStatusChange_applicationRejectionReason";
    private String clONBStatusChange_applicationRejectionReason;

    public static final String APPROVAL_DOCUMENTS = "approvalDocuments";
    private ClientApprovalDocs approvalDocuments;
    public static final String APPROVAL_DOCUMENT = "approvalDocument";
    private ClientApprovalDoc approvalDocument;
    public static final String CLIENT_ID_FOR_CHALLENGE = "clientIdForChallenge";
    private String clientIdForChallenge;
    public static final String LANGUAGE = "language";
    private LovLang language;

    public static final String MANUAL_APPROVAL_STARTED = "manualApprovalStarted";
    private boolean manualApprovalStarted = false;

    public static final String PROCESS_PHASE = "processPhase";
    private String processPhase;

    public static final String PROCESS_PART = "processPart";
    private String processPart;

}
