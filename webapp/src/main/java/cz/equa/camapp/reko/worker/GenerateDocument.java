package cz.equa.camapp.reko.worker;

import cz.equa.camapp.lovs.*;
import cz.equa.camapp.manager.BslManager;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.model.DealerInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.document.EmailDTO;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.service.product_service.LovTranslationsItemDTO;
import cz.equa.camapp.utils.Chain;
import cz.equa.camapp.utils.ValidatorXsdUtils;
import cz.rb.cdm.dds.crm.cpa.p0001540._1.P0001540;
import cz.rb.cdm.dds.crm.cpa.p0001540._1.P0001644;
import cz.rb.cdm.dds.crm.cpa.p0001541._1.P0001541;
import cz.rb.cdm.dds.crm.cpa.p0001543._1.P0001543;
import cz.rb.cdm.dds.crm.cpa.x0000045._1.********;
import cz.rb.cdm.dds.crm.cpa.x0000046._1.********;
import cz.rb.cdm.dds.crm.cpa.x0000047._1.X0000047;
import cz.rb.cdm.dds.crm.cpa.x0000048._1.X0000048;
import cz.rb.las.parametrization.model.BSLInterestVariance;
import cz.rb.las.parametrization.model.BSLProductType;
import cz.rb.las.parametrization.model.BSLSubtypeParametrization;
import cz.rb.las.parametrization.model.BSLSubtypePurposeCategory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cz.equa.camapp.reko.ProcessVariables.GENERATED_DOCUMENTS;
import static cz.equa.camapp.rest.model.Document.DELIVERY_CHANNEL_ARCHIVE;
import static cz.equa.camapp.rest.model.Document.DELIVERY_CHANNEL_EMAIL;
import static cz.equa.camapp.rest.service.DmsService.DOCUMENT_ID_TYPE_EXTERNALID;
import static cz.equa.camapp.utils.Constants.ADB_SYSTEM_ID;
import static cz.equa.camapp.utils.Constants.CSA_SYSTEM_ID;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "rekoGenerateDocument")
public class GenerateDocument extends RekoAbstractWorker {

    public static final String TARIF = "TARIF";
    public static final String APR = "APR";
    public static final String SEL = "SEL";
    public static final String ODCHYLKAURU = "ODCHYLKAURU";
    public static final String VERIF = "VERIF";
    public static final String ODCHYLKAURS = "ODCHYLKAURS";
    public static final String VERIFDONE = "VERIFDONE";
    public static final String AGENT_NAME = "Raiffeisenbank a.s.";
    public static final String RB_AGENT = "RBAgent";
    public static final String HVEZDOVA = "Hvězdova";
    public static final String PRAHA_4 = "Praha 4";

    private final DmsService dmsService;
    private final LovManager lovManager;
    private final BslManager bslManager;

    @Value("${reko.generate.document.rea.finalization.url}")
    private String rekoReaFinalizationUrl;

    @Value("${reko.generate.document.dra.finalization.url}")
    private String rekoDraFinalizationUrl;

    @Autowired
    public GenerateDocument(ApplicationService applicationService,
                            DmsService dmsService,
                            LovManager lovManager,
                            BslManager bslManager) {
        super(applicationService);
        this.dmsService = dmsService;
        this.lovManager = lovManager;
        this.bslManager = bslManager;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();

        String clientId = pv.getClientId();
        String busApplId = pv.getBusApplId();
        String correlationId = pv.getCorrelationId();
        DocumentType docType = DocumentType.valueOf(pv.getTemplate());
        String mapTemplateId = docType.name();

        GeneratedDocumentsDTO generatedDocuments = pv.getGeneratedDocuments();
        boolean updateDocument = generatedDocuments != null && generatedDocuments.getGeneratedObjectIds().containsKey(mapTemplateId);
        EmailDTO email = null;
        if (DELIVERY_CHANNEL_EMAIL.equals(docType.getDeliveryChannel())) {
            String emailAddr = pv.getLoanApplicationOwner().getEmail();

            if (StringUtils.isBlank(emailAddr)) {
                throw new ServiceException("E-mail address is empty");
            }

            email = new EmailDTO();
            email.setReceiver(emailAddr);
        }

        Object xmlData = prepareData(docType, pv);
        try {
            ValidatorXsdUtils validatorXsdUtils = new ValidatorXsdUtils();
            validatorXsdUtils.validateByScripturaXsd(xmlData);
        } catch (Exception e) {
            log.warn("Validation by XSD failed.", e);
        }

        if (updateDocument) {
            String dctmId = generatedDocuments.getGeneratedObjectIds().get(mapTemplateId).getDocumentId();
            if (DOCUMENT_ID_TYPE_EXTERNALID.equals(DmsService.getDocumentIdType(dctmId))) {
                dctmId = dmsService.getDctmId(dctmId, correlationId);
            }
            dmsService.documentRenderUpdate(
                    docType,
                    xmlData,
                    dctmId,
                    correlationId);
        } else {
            String docId = dmsService.render(
                    clientId,
                    busApplId,
                    null,
                    docType,
                    docType.getDocumentName(),
                    xmlData,
                    email,
                    correlationId
            );
            if (DELIVERY_CHANNEL_ARCHIVE.equals(docType.getDeliveryChannel()) && generatedDocuments != null) {
                generatedDocuments.addObjectId(docType.name(), new GeneratedDocumentInfoDTO(docId, docType.name()));
                setProcessVariable(GENERATED_DOCUMENTS, generatedDocuments);
            }
        }
    }

    protected Object prepareData(DocumentType template, ProcessVariables pv) throws ServiceException {
        String product2 = "";
        if ("BSL_REA".equals(pv.getBuildingLoanApplication().getBusProdSubTp())) {
            product2 = "Rekopůjčku";
        } else if ("BSL_DRA".equals(pv.getBuildingLoanApplication().getBusProdSubTp())) {
            product2 = "Půjčku na družstevní bydlení";
        }

        var buildingLoanAppl = pv.getBuildingLoanApplication();

        switch (template) {
            case ********:
                ******** x0000045 = new ********();
                ********.DeliveryData deliveryData = new ********.DeliveryData();
                deliveryData.setProductType(product2);
                deliveryData.setReceiver(pv.getLoanApplicationOwner().getEmail());
                deliveryData.setReason(getRejectReason(buildingLoanAppl.getRejectRsnId()));
                x0000045.setDeliveryData(deliveryData);
                return x0000045;

            case ********:
                ******** x0000046 = new ********();
                x0000046.setProductType(buildingLoanAppl.getBusProdSubTp());
                x0000046.setAccessKey(buildingLoanAppl.getHash());
                x0000046.setApplUrl(getFinalizationUrl(pv, buildingLoanAppl.getHash()));
                x0000046.setAmount(pv.getBuildingLoanApplVariants().getVariant("REQ").getFinaAmt().stripTrailingZeros().toPlainString());
                return x0000046;

            case X0000047:
                X0000047 x0000047 = new X0000047();
                x0000047.setProductType(buildingLoanAppl.getBusProdSubTp());
                x0000047.setAccessKey(buildingLoanAppl.getHash());
                x0000047.setApplUrl(getFinalizationUrl(pv, buildingLoanAppl.getHash()));
                x0000047.setAmount(pv.getBuildingLoanApplVariants().getVariant("OFR").getFinaAmt().stripTrailingZeros().toPlainString());
                return x0000047;

            case X0000048:
                X0000048 x0000048 = new X0000048();
                x0000048.setProductType(buildingLoanAppl.getBusProdSubTp());
                x0000048.setAmount(pv.getBuildingLoanApplVariants().getVariant(APR).getFinaAmt().stripTrailingZeros().toPlainString());
                x0000048.setConstructionSavingsContractNumber(buildingLoanAppl.getContrNumBs());
                x0000048.setRegistrationNumber(buildingLoanAppl.getContrNumReg());
                x0000048.setProposalSignatureDate(buildingLoanAppl.getContrSignDate());
                return x0000048;

            case P0001540:
                return prepareTemplateP0001540(pv);
            case P0001541:
                return prepareTemplateP0001541(pv);
            case P0001543:
                return prepareTemplateP0001543(pv);
            case P0001644:
                return prepareTemplateP0001644(pv);

            default:
                log.error("Unknown template {}", template);
                throw new ServiceException("Unknown template " + template);
        }
    }

    private String getFinalizationUrl(ProcessVariables pv, String accessKey) throws ServiceException {
        var baseUrl = switch (pv.getBuildingLoanApplication().getBusProdSubTp()) {
            case "BSL_REA" -> rekoReaFinalizationUrl;
            case "BSL_DRA" -> rekoDraFinalizationUrl;
            default ->
                    throw new ServiceException("Unknown busProdSubTp: " + pv.getBuildingLoanApplication().getBusProdSubTp());
        };


        return baseUrl + "/dokoncitzadost?accessKey=" + URLEncoder.encode(accessKey, StandardCharsets.UTF_8);
    }

    @NotNull
    protected P0001540 prepareTemplateP0001540(ProcessVariables pv) {
        P0001540 p0001540 = new P0001540();
        P0001540.ApplDetails applDetails1540 = new P0001540.ApplDetails();
        applDetails1540.setRstsBuildingSavingsNumber(pv.getBuildingLoanApplication().getContrNumBs());
        applDetails1540.setRstsRegistrationLoanNumber(pv.getBuildingLoanApplication().getContrNumReg());
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);
        var aprVariant = pv.getBuildingLoanApplVariants().getVariant(APR);
        var selVariant = pv.getBuildingLoanApplVariants().getVariant(SEL);

        BSLSubtypeParametrization subtypeParametrization = getParamsBSL.getSubtypes().stream().filter(
                subtype -> subtype.getBusProdSubTp().equals(pv.getBuildingLoanApplication().getBusProdSubTp())
        ).findFirst().orElse(null);
        if (subtypeParametrization != null) {
            LovCsaParamCastar csaParamCastar = lovManager.getLovByDetail(LovCsaParamCastar.class, TARIF, subtypeParametrization.getTariff(), getLanguage());
            if (csaParamCastar != null) {
                String depositRateString = csaParamCastar.getLovDetail().get(ODCHYLKAURS);
                log.debug("Deposit rate: {}", depositRateString);
                applDetails1540.setDepositIRate(new BigDecimal(depositRateString));
                String odchylkaUru = csaParamCastar.getLovDetail().get(ODCHYLKAURU);
                applDetails1540.setTotalIRate(new BigDecimal(odchylkaUru));
            }
            applDetails1540.setMonthlyDepositCoef(BigDecimal.valueOf(subtypeParametrization.getMonthlyDepositCoef() * 100));
            applDetails1540.setSavingsContractFeeCoef(BigDecimal.valueOf(subtypeParametrization.getSavingsContractFeeCoef()).multiply(new BigDecimal(100)));
            applDetails1540.setFinaAmt(aprVariant.getFinaAmt().stripTrailingZeros());

            LovCsaVopTarif csaVopTariff = lovManager.getLovByDetail(LovCsaVopTarif.class, TARIF, subtypeParametrization.getTariff(), getLanguage());
            if (csaVopTariff != null) {
                applDetails1540.setFinaAmtMinValRate(new BigDecimal(csaVopTariff.getLovDetail().get("MINZUSTPRIDEL")));
                applDetails1540.setAppreciationIndicatorMinVal(new BigDecimal(csaVopTariff.getLovDetail().get("MINHODNOCENI")));
                applDetails1540.setAppreciationIndicatorCoef(new BigDecimal(csaVopTariff.getLovDetail().get("KUZ")));
                applDetails1540.setTariff(csaVopTariff.getLovDetail().get("TEXT"));
            }
            applDetails1540.setInsNumber(subtypeParametrization.getInsNum());
            List<BSLSubtypePurposeCategory> purposeCategories = subtypeParametrization.getLoanPurposeCategories();
            BuildingApplVariantPurposeDTO purposeVerified = aprVariant.getLoanPurposes().stream().filter(cat -> cat.getPurpStatId().equals(VERIF)).findFirst().orElse(null);
            if (purposeVerified != null) {
                BSLSubtypePurposeCategory filterPurposeCategory = purposeCategories.stream().filter(purposeCategory -> purposeCategory.getPurposeCategory().getName().equals(purposeVerified.getPurpCatgId())).findFirst().orElse(null);
                if (filterPurposeCategory != null && filterPurposeCategory.getInterestVariances() != null) {
                    List<BSLInterestVariance> interestVariancesSurNinSur = filterPurposeCategory.getInterestVariances().stream().filter(interestVariance -> interestVariance.getTypeId().equals("SUR_NINSUR_PPI")).toList();
                    Double minimalVariance = interestVariancesSurNinSur.stream().map(BSLInterestVariance::getVariance).min(Comparator.naturalOrder()).orElse(null);
                    if (minimalVariance != null) {
                        applDetails1540.setInsIR(BigDecimal.valueOf(minimalVariance * 100));
                    }
                    List<BSLInterestVariance> interestVariancesSurNrb = filterPurposeCategory.getInterestVariances().stream().filter(interestVariance -> interestVariance.getTypeId().equals("SUR_NRB")).toList();
                    var minimalVarianceRbAccountIR = interestVariancesSurNrb.stream().map(BSLInterestVariance::getVariance).min(Comparator.naturalOrder()).orElse(null);
                    if (minimalVarianceRbAccountIR != null) {
                        applDetails1540.setRbAccountIR(BigDecimal.valueOf(minimalVarianceRbAccountIR * 100));
                    }
                }

            }
        }

        if (getParamsBSL.getProdTypeParameters() != null) {
            applDetails1540.setTariffNumber(getParamsBSL.getProdTypeParameters().getTariffNum().toString());
            applDetails1540.setAnnouncementNumber(getParamsBSL.getProdTypeParameters().getAnnouncNum().toString());
            applDetails1540.setInfoNumber(getParamsBSL.getProdTypeParameters().getInfoNum().toString());
        }
        if (Objects.equals(pv.getLoanApplicationOwner().getTaxDomicile(), "CZ")) {
            applDetails1540.setTaxResidenceFlag("ANO");
        } else {
            applDetails1540.setTaxResidenceFlag("NE");
        }
        applDetails1540.setRstsLoanNumber(pv.getBuildingLoanApplication().getContrNum());

        applDetails1540.setClosePersonNeedFlg(String.valueOf(aprVariant.getCloseRelCoFin()));
        applDetails1540.setFinaAmtText(aprVariant.getFinaAmt().stripTrailingZeros().toPlainString());
        List<BuildingApplVariantPurposeDTO> purposes = aprVariant.getLoanPurposes();
        List<BuildingApplVariantPurposeDTO> verifPurposes = purposes.stream()
                .filter(purpose -> VERIF.equals(purpose.getPurpStatId())).toList();
        if (!verifPurposes.isEmpty()) {
            BuildingApplVariantPurposeDTO firstPurpose = verifPurposes.get(0);
            applDetails1540.setLoanPurposeCategory(lovManager.getLov(new LovCatgLoanPurp(firstPurpose.getPurpCatgId()), getLanguage()).getDescr());

            List<String> purposesDescriptions = new ArrayList<>();
            verifPurposes.forEach(purpose ->
                    Chain.of(purpose)
                            .safe(p -> lovManager.getLov(new LovLoanPurp(p.getPurpId()), getLanguage()))
                            .safe(AbstractLov::getDescr)
                            .exec(purposesDescriptions::add)
            );
            if (!purposesDescriptions.isEmpty()) {
                applDetails1540.getLoanPurposesDescription().addAll(purposesDescriptions);
            }

            applDetails1540.setLoanPurposeDescription(lovManager.getLov(new LovLoanPurp(firstPurpose.getPurpId()), getLanguage()).getDescr());
        }

        var applVariant1502 = verifPurposes.stream().filter(purpose -> "1502".equals(purpose.getPurpId())).findFirst().orElse(null);
        if (applVariant1502 != null) {
            BigInteger foundInstOblgtnKey = BigInteger.valueOf(applVariant1502.getInstOblgtnKey());
            BuildingObligationDTO obligation = pv.getBuildingObligations().getObligations().stream().filter(
                    obl -> obl.getInstOblgtnKey().equals(foundInstOblgtnKey)
            ).findFirst().orElse(null);
            if (obligation != null) {
                applDetails1540.setOrigBankName(lovManager.getLov(new LovFinInstn(obligation.getOfFinInstnId()), getLanguage()).getLabel());
                applDetails1540.setOrigContractDate(obligation.getOfContrSignDate());
                applDetails1540.setOrigContractName(obligation.getOfContrName());
                applDetails1540.setOrigContractNumber(obligation.getOfContrNumReg());
                applDetails1540.setOrigRstsLoanNumber(obligation.getScIntContrNum());
                applDetails1540.setOrigRstsRegistrationLoanDate(obligation.getOfContrSignDate());
                applDetails1540.setOrigRstsRegistrationLoanNumber(obligation.getOfContrNumReg());
            }
        }
        applDetails1540.setDisbursementAccNumPref(selVariant.getAccNumPrefix());
        applDetails1540.setDisbursementAccNum(selVariant.getAccNum());
        applDetails1540.setDisbursementBank(selVariant.getAccBankCode());
        applDetails1540.setFinaInsAmt(pv.getRekoRpsnCalculations().getFinaInsAmt());
        applDetails1540.setFinaInsAmtText(String.valueOf(pv.getRekoRpsnCalculations().getFinaInsAmt()));
        applDetails1540.setInsFeeAmt(pv.getRekoRpsnCalculations().getInsFeeAmt());
        applDetails1540.setBusProdSubTp(pv.getBuildingLoanApplication().getBusProdSubTp());

        applDetails1540.setDeposit(pv.getRekoRpsnCalculations().getDeposit());
        applDetails1540.setFinaInstCnt(pv.getRekoRpsnCalculations().getFinaInstCnt().intValue());
        applDetails1540.setDueDate(LocalDate.parse(pv.getRekoRpsnCalculations().getDueDate()));
        applDetails1540.setToBePaidAmt(pv.getRekoRpsnCalculations().getToBePaidAmt());

        List<SpecCondDTO> verifSpecConds = verifPurposes.stream()
                .map(BuildingApplVariantPurposeDTO::getSpecConds)
                .flatMap(Collection::stream)
                .filter(specCond -> VERIF.equals(specCond.getCondStatId()))
                .toList();


        if (!verifSpecConds.isEmpty()) {
            List<LovVPurpSpeccond> purpSpecconds = lovManager.getLovs(LovVPurpSpeccond.class, getLanguage(), null);
            if (purpSpecconds != null && !purpSpecconds.isEmpty()) {
                Map<String, List<String>> phaseGroups = verifSpecConds.stream()
                        .flatMap(specCond -> purpSpecconds.stream()
                                .filter(lovItem -> lovItem.getCode().equals(specCond.getCondId()))
                                .map(matchedLov -> Map.entry(
                                        matchedLov.getLovDetail().get("PHASE_ID"),
                                        specCond.getCondDetail()
                                ))
                        )
                        .filter(entry -> "A".equals(entry.getKey()) || "B".equals(entry.getKey()))
                        .collect(Collectors.groupingBy(
                                Map.Entry::getKey,
                                Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                        ));

                if (phaseGroups.containsKey("A")) {
                    applDetails1540.getSpecificPreConditionsDescription().addAll(phaseGroups.get("A"));
                }

                if (phaseGroups.containsKey("B")) {
                    applDetails1540.getSpecificPostConditionsDescription().addAll(phaseGroups.get("B"));
                }
            }
        }

        boolean existSurchargeDisRbFlag = selVariant.getSurcharges().stream().anyMatch(surcharge -> "DIS_RB_FLAG".equals(surcharge.getSurchrgTpId()));
        applDetails1540.setRbAccount(String.valueOf(existSurchargeDisRbFlag));

        boolean existPPI = selVariant.getInsurances().stream().anyMatch(insurance -> "PPI".equals(insurance.getInsurTpId()));
        applDetails1540.setInsurSelFlg(String.valueOf(existPPI));


        PtIncomeRstsListDTO incomes = pv.getLoanApplicationIncomes();
        List<PtIncomeRstsDTO> allVerifDone = incomes.getIncomes().stream().filter(income -> VERIFDONE.equals(income.getIncVerifRsltId())).toList();
        List<PtIncomeRstsDTO> allVerifDoneMaxListOrderByOrdNum = sortIncomes(allVerifDone, getLanguage());

        if (!allVerifDoneMaxListOrderByOrdNum.isEmpty() && allVerifDoneMaxListOrderByOrdNum.get(0) != null) {
            var currLov = lovManager.getLov(new LovCcy(allVerifDoneMaxListOrderByOrdNum.get(0).getCcyId()), getLanguage());
            if (currLov != null) {
                applDetails1540.setCurrency(currLov.getCode());
            }
        }

        applDetails1540.setFinaIR(aprVariant.getIntrsRx().multiply(new BigDecimal(100)));
        applDetails1540.setFixPeriod(Integer.parseInt(aprVariant.getFixPeriod()));

        applDetails1540.setLoanContractFeeAmt(pv.getRekoRpsnCalculations().getLoanContractFeeAmt());


        if (getParamsBSL.getProdTypeParameters() != null) {
            double loanPenaltyCoef = getParamsBSL.getProdTypeParameters().getLoanPenaltyCoef();
            int loanPenaltyMin = getParamsBSL.getProdTypeParameters().getLoanPenaltyMin();
            int loanPenaltyMax = getParamsBSL.getProdTypeParameters().getLoanPenaltyMax();
            BigDecimal finaAmt = pv.getRekoRpsnCalculations().getFinaAmt();

            if (finaAmt.multiply(BigDecimal.valueOf(loanPenaltyCoef)).compareTo(BigDecimal.valueOf(loanPenaltyMin)) < 0) {
                applDetails1540.setLoanPenaltyFeeAmt(BigDecimal.valueOf(loanPenaltyMin));
            } else if (finaAmt.multiply(BigDecimal.valueOf(loanPenaltyCoef)).compareTo(BigDecimal.valueOf(loanPenaltyMax)) > 0) {
                applDetails1540.setLoanPenaltyFeeAmt(BigDecimal.valueOf(loanPenaltyMax));
            } else {
                applDetails1540.setLoanPenaltyFeeAmt(finaAmt.multiply(BigDecimal.valueOf(loanPenaltyCoef)));
            }
        }

        applDetails1540.setRPSN(String.valueOf(pv.getRekoRpsnCalculations().getRPSN().multiply(new java.math.BigDecimal(100))));
        applDetails1540.setEstimDisbursDate(LocalDate.parse(pv.getRekoRpsnCalculations().getEstimDisbursDate()));
        applDetails1540.setFirstPaymentDate(LocalDate.parse(pv.getRekoRpsnCalculations().getFirstPaymentDate()));
        applDetails1540.setAddRPSN(pv.getRekoRpsnCalculations().getAddRPSN().multiply(new java.math.BigDecimal(100)).toString());
        applDetails1540.setReportDate(LocalDate.now());
        applDetails1540.setPreContrReportDate(getPreContrReportDate(pv));
        p0001540.setApplDetails(applDetails1540);

        P0001540.Party party1540 = new P0001540.Party();
        party1540.setFirstName(pv.getLoanApplicationOwner().getFirstName());
        party1540.setLastName(pv.getLoanApplicationOwner().getFamilyName());
        if (!pv.getLoanApplicationOwner().getRcNum().isBlank()) {
            party1540.setBirthCode(pv.getLoanApplicationOwner().getRcNum());
        } else {
            party1540.setBirthPseudoCode(pv.getLoanApplicationOwner().getRstsPseudoBirthCode());
        }
        party1540.setBirthDate(pv.getLoanApplicationOwner().getBirthDate());

        party1540.setNationality(getCountry(new LovCntry(
                pv.getLoanApplicationOwner().getCitizenship()
        )));
        party1540.setGender(lovManager.getLov(new LovGender(pv.getLoanApplicationOwner().getGenderId()), getLanguage()).getLabel());
        List<PtIncomeRstsDTO> verifiedIncomes = incomes.getIncomes().stream()
                .filter(income -> VERIFDONE.equals(income.getIncVerifRsltId()))
                .toList();

        if (!verifiedIncomes.isEmpty()) {
            List<PtIncomeRstsDTO> sortedIncomes = sortIncomes(verifiedIncomes, getLanguage());

            if (!sortedIncomes.isEmpty() && sortedIncomes.get(0) != null) {
                var selectedIncome = sortedIncomes.get(0);
                Set<String> employmentTypes = Set.of("EMPLT", "EMPLT2");
                Set<String> businessTypes = Set.of("OWNBS", "BS");

                String incTpId = selectedIncome.getIncTpId();
                if (employmentTypes.contains(incTpId)) {
                    LovOccCatg lovOccCatg = lovManager.getTranslatedLovBySystem(
                            LovOccCatg.class,
                            selectedIncome.getOccCatgId(),
                            ADB_SYSTEM_ID,
                            CSA_SYSTEM_ID,
                            LovLang.CZE2);
                    party1540.setJobTp(lovOccCatg.getDescr());
                } else if (businessTypes.contains(incTpId)) {
                    party1540.setJobTp("Podnikatel");
                } else {
                    party1540.setJobTp("Ostatní");
                }
            }
        }

        String residencyTpId = pv.getLoanApplicationOwner().getResidencyTpId();
        LovStayTp lovStayTp = lovManager.getLov(new LovStayTp(residencyTpId), getLanguage());
        if (lovStayTp != null) {
            party1540.setTypeOfResidency(lovStayTp.getLabel());
        }
        party1540.setBirthPlace(pv.getLoanApplicationOwner().getBirthPlace());
        party1540.setBirthCountry(getCountry(new LovCntry(pv.getLoanApplicationOwner().getBirthCntryId())));
        if (StringUtils.isNotBlank(pv.getLoanApplicationOwner().getResidencyFrom())) {
            party1540.setResidencyFrom(LocalDate.parse(pv.getLoanApplicationOwner().getResidencyFrom()));
        }
        if (StringUtils.isNotBlank(pv.getLoanApplicationOwner().getResidencyTo())) {
            party1540.setResidencyTo(LocalDate.parse(pv.getLoanApplicationOwner().getResidencyTo()));
        }

        CtIdCardDTO ctIdCard = pv.getLoanApplicationOwner().getIdCards().stream().filter(ctIdCardDTO -> ctIdCardDTO.getIdCardPurpId().equals("1STID")).findFirst().orElse(null);
        if (ctIdCard != null) {
            String idCardTpId = ctIdCard.getIdCardTpId();
            //String citzspId = lovManager.getLov(new LovStayTp(pv.getLoanApplicationOwner().getResidencyTpId()), getLanguage()).getLovDetail().get("CITZSP_ID");
            party1540.setFirstDocTp(lovManager.getLov(new LovIdCardTp(idCardTpId), getLanguage()).getDescr());
            party1540.setFirstDocNumber(ctIdCard.getCardId());
            party1540.setFirstDocIssuedBy(ctIdCard.getIssuer());
            party1540.setFirstDocValidTo(ctIdCard.getExprDate());
        }
        CtIdCardDTO ctIdCardSecond = pv.getLoanApplicationOwner().getIdCards().stream().filter(ctIdCardDTO -> ctIdCardDTO.getIdCardPurpId().equals("2NDID")).findFirst().orElse(null);
        if (ctIdCardSecond != null) {
            String secondidCardTpId = ctIdCardSecond.getIdCardTpId();
            party1540.setSecondDocTp(lovManager.getLov(new LovIdCardTp(secondidCardTpId), getLanguage()).getDescr());
            party1540.setSecondDocNumber(ctIdCardSecond.getCardId());
            party1540.setSecondDocValidTo(ctIdCardSecond.getExprDate());
            party1540.setSecondDocIssuedBy(ctIdCardSecond.getIssuer());
        }

        party1540.setPepFlag(pv.getLoanApplicationOwner().getPep() == Boolean.TRUE ? "ANO" : "NE");

        P0001540.Party.Contact contact1540 = new P0001540.Party.Contact();
        contact1540.setRegMobile(pv.getLoanApplicationOwner().getPhoneNum());
        contact1540.setRegEmail(pv.getLoanApplicationOwner().getEmail());
        party1540.setContact(contact1540);

        P0001540.Party.Address partyAddress1540 = new P0001540.Party.Address();
        if (StringUtils.isBlank(pv.getLoanApplicationOwner().getPermAddr().getStreetName())) {
            partyAddress1540.setPermAddStreet(pv.getLoanApplicationOwner().getPermAddr().getCityName());
        } else {
            partyAddress1540.setPermAddStreet(pv.getLoanApplicationOwner().getPermAddr().getStreetName());
        }
        partyAddress1540.setPermAddNumber(pv.getLoanApplicationOwner().getPermAddr().getStreetNum());
        partyAddress1540.setPermAddZip(pv.getLoanApplicationOwner().getPermAddr().getZip());
        partyAddress1540.setPermAddCity(pv.getLoanApplicationOwner().getPermAddr().getCityName());
        partyAddress1540.setPermAddCountry(getCountry(new LovCntry(pv.getLoanApplicationOwner().getPermAddr().getCntryId())));

        if (pv.getLoanApplicationOwner().getPostalAddr() != null) {
            if (StringUtils.isBlank(pv.getLoanApplicationOwner().getPostalAddr().getStreetName())) {
                partyAddress1540.setCorrAddStreet(pv.getLoanApplicationOwner().getPostalAddr().getCityName());
            } else {
                partyAddress1540.setCorrAddStreet(pv.getLoanApplicationOwner().getPostalAddr().getStreetName());
            }
            partyAddress1540.setCorrAddNumber(pv.getLoanApplicationOwner().getPostalAddr().getStreetNum());
            partyAddress1540.setCorrAddZip(pv.getLoanApplicationOwner().getPostalAddr().getZip());
            partyAddress1540.setCorrAddCity(pv.getLoanApplicationOwner().getPostalAddr().getCityName());
            partyAddress1540.setCorrAddCountry(getCountry(new LovCntry(pv.getLoanApplicationOwner().getPostalAddr().getCntryId())));
        }
        party1540.setAddress(partyAddress1540);

        P0001540.Agent agent1540 = new P0001540.Agent();
        agent1540.setAgentName(AGENT_NAME);
        agent1540.setAgentPhoneNo("412 440 000");
        agent1540.setAgentEmail("<EMAIL>");
        p0001540.setAgent(agent1540);
        p0001540.setParty(party1540);

        return p0001540;
    }

    @NotNull
    private P0001541 prepareTemplateP0001541(ProcessVariables pv) {
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);
        BSLSubtypeParametrization subtypeParametrization = getParamsBSL.getSubtypes().stream().filter(
                subtype -> subtype.getBusProdSubTp().equals(pv.getBuildingLoanApplication().getBusProdSubTp())
        ).findFirst().orElse(null);
        P0001541 p0001541 = new P0001541();
        P0001541.Party party1541 = new P0001541.Party();
        party1541.setFirstName(pv.getLoanApplicationOwner().getFirstName());
        party1541.setLastName(pv.getLoanApplicationOwner().getFamilyName());
        if (!pv.getLoanApplicationOwner().getRcNum().isBlank()) {
            party1541.setBirthCode(pv.getLoanApplicationOwner().getRcNum());
        } else {
            party1541.setBirthPseudoCode(pv.getLoanApplicationOwner().getRstsPseudoBirthCode());
        }

        party1541.setBirthDate(pv.getLoanApplicationOwner().getBirthDate());
        party1541.setNationality(getCountry(new LovCntry(
                pv.getLoanApplicationOwner().getCitizenship()
        )));

        P0001541.Party.Address address1541 = new P0001541.Party.Address();
        if (StringUtils.isBlank(pv.getLoanApplicationOwner().getPermAddr().getStreetName())) {
            address1541.setPermAddStreet(pv.getLoanApplicationOwner().getPermAddr().getCityName());
        } else {
            address1541.setPermAddStreet(pv.getLoanApplicationOwner().getPermAddr().getStreetName());
        }
        address1541.setPermAddNumber(pv.getLoanApplicationOwner().getPermAddr().getStreetNum());
        address1541.setPermAddZip(pv.getLoanApplicationOwner().getPermAddr().getZip());
        address1541.setPermAddCity(pv.getLoanApplicationOwner().getPermAddr().getCityName());
        address1541.setPermAddCountry(getCountry(new LovCntry(pv.getLoanApplicationOwner().getPermAddr().getCntryId())));

        // corrAdd - mandatory
        if (pv.getLoanApplicationOwner().getPostalAddr() == null) {
            if (StringUtils.isBlank(pv.getLoanApplicationOwner().getPermAddr().getStreetName())) {
                address1541.setCorrAddStreet(pv.getLoanApplicationOwner().getPermAddr().getCityName());
            } else {
                address1541.setCorrAddStreet(pv.getLoanApplicationOwner().getPermAddr().getStreetName());
            }
            address1541.setCorrAddNumber(pv.getLoanApplicationOwner().getPermAddr().getStreetNum());
            address1541.setCorrAddZip(pv.getLoanApplicationOwner().getPermAddr().getZip());
            address1541.setCorrAddCity(pv.getLoanApplicationOwner().getPermAddr().getCityName());
            address1541.setCorrAddCountry(getCountry(new LovCntry(pv.getLoanApplicationOwner().getPermAddr().getCntryId())));
        } else {
            if (StringUtils.isBlank(pv.getLoanApplicationOwner().getPermAddr().getStreetName())) {
                address1541.setCorrAddStreet(pv.getLoanApplicationOwner().getPostalAddr().getCityName());
            } else {
                address1541.setCorrAddStreet(pv.getLoanApplicationOwner().getPostalAddr().getStreetName());
            }
            address1541.setCorrAddNumber(pv.getLoanApplicationOwner().getPostalAddr().getStreetNum());
            address1541.setCorrAddZip(pv.getLoanApplicationOwner().getPostalAddr().getZip());
            address1541.setCorrAddCity(pv.getLoanApplicationOwner().getPostalAddr().getCityName());
            address1541.setCorrAddCountry(getCountry(new LovCntry(pv.getLoanApplicationOwner().getPostalAddr().getCntryId())));
        }


        party1541.setAddress(address1541);

        P0001541.Party.Contact contact1541 = new P0001541.Party.Contact();
        contact1541.setRegMobile(pv.getLoanApplicationOwner().getPhoneNum());
        contact1541.setRegEmail(pv.getLoanApplicationOwner().getEmail());
        party1541.setContact(contact1541);

        p0001541.setParty(party1541);

        P0001541.ApplDetails applDetails1541 = new P0001541.ApplDetails();
        applDetails1541.setRstsBuildingSavingsNumber(pv.getBuildingLoanApplication().getContrNumBs());
        applDetails1541.setRstsRegistrationLoanNumber(pv.getBuildingLoanApplication().getContrNumReg());
        if (subtypeParametrization != null) {
            applDetails1541.setInsNumber(subtypeParametrization.getInsNum());
            LovCsaVopTarif csaVopTarif = lovManager.getLovByDetail(LovCsaVopTarif.class, TARIF, subtypeParametrization.getTariff(), getLanguage());
            applDetails1541.setTariff(csaVopTarif.getLovDetail().get("TEXT"));
            applDetails1541.setFinaAmtMinValRate(new BigDecimal(csaVopTarif.getLovDetail().get("MINZUSTPRIDEL")));
            applDetails1541.setAppreciationIndicatorMinVal(new BigDecimal(csaVopTarif.getLovDetail().get("MINHODNOCENI")));
            applDetails1541.setAppreciationIndicatorCoef(new BigDecimal(csaVopTarif.getLovDetail().get("KUZ")));
            LovCsaParamCastar lovCsaParamCastar = lovManager.getLovByDetail(LovCsaParamCastar.class, TARIF, subtypeParametrization.getTariff(), getLanguage());
            String odchylkaUrs = lovCsaParamCastar.getLovDetail().get(ODCHYLKAURS);
            if (odchylkaUrs != null) {
                applDetails1541.setDepositIRate(BigDecimal.valueOf(Long.parseLong(odchylkaUrs)));
            } else {
                applDetails1541.setDepositIRate(BigDecimal.ZERO);
            }
            String odchylkauru = lovCsaParamCastar.getLovDetail().get(ODCHYLKAURU);
            applDetails1541.setTotalIRate(new BigDecimal(odchylkauru));
            applDetails1541.setMonthlyDepositCoef(BigDecimal.valueOf(subtypeParametrization.getMonthlyDepositCoef() * 100));
            applDetails1541.setSavingsContractFeeCoef(BigDecimal.valueOf(subtypeParametrization.getSavingsContractFeeCoef()).multiply(BigDecimal.valueOf(100)));
            applDetails1541.setSavingsContractFeeCoef(BigDecimal.valueOf(subtypeParametrization.getSavingsContractFeeCoef()).multiply(new BigDecimal(100)));
        }

        var aprVariant = pv.getBuildingLoanApplVariants().getVariant(APR);
        var selVariant = pv.getBuildingLoanApplVariants().getVariant(SEL);

        applDetails1541.setFinaAmt(aprVariant.getFinaAmt().stripTrailingZeros());

        applDetails1541.setFixPeriod(Integer.parseInt(aprVariant.getFixPeriod()));
        applDetails1541.setFinaIR(aprVariant.getIntrsRx().multiply(new BigDecimal(100)));
        applDetails1541.setFinaInsAmt(pv.getRekoRpsnCalculations().getFinaInsAmt());
        applDetails1541.setRstsLoanNumber(pv.getBuildingLoanApplication().getContrNum());
        applDetails1541.setLoanContractFeeAmt(pv.getRekoRpsnCalculations().getLoanContractFeeAmt());
        applDetails1541.setReportDate(LocalDate.now());
        applDetails1541.setBusProdSubTp(pv.getBuildingLoanApplication().getBusProdSubTp());
        boolean existPPI = selVariant.getInsurances().stream().anyMatch(insurance -> "PPI".equals(insurance.getInsurTpId()));
        applDetails1541.setInsurSelFlg(String.valueOf(existPPI));
        p0001541.setApplDetails(applDetails1541);
        return p0001541;
    }

    @NotNull
    private P0001543 prepareTemplateP0001543(ProcessVariables pv) {
        LovLang lang = getLanguage();
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);
        P0001543 p0001543 = new P0001543();
        BSLSubtypeParametrization subtypeParametrization = getParamsBSL.getSubtypes().stream().filter(
                subtype -> subtype.getBusProdSubTp().equals(pv.getBuildingLoanApplication().getBusProdSubTp())
        ).findFirst().orElse(null);
        P0001543.Agent agent1543 = new P0001543.Agent();


        agent1543.setAgentName(AGENT_NAME);
        agent1543.setAgentReqNo("49240901");
        agent1543.setAgentPhoneNo("412 440 000");
        agent1543.setAgentEmail("<EMAIL>");
        P0001543.Agent.Address agentAddress1543 = new P0001543.Agent.Address();
        agentAddress1543.setStreet(HVEZDOVA);
        agentAddress1543.setNumber("1716/2b");
        agentAddress1543.setCity(PRAHA_4);
        agentAddress1543.setPostalCode("140 78");
        agent1543.setAddress(agentAddress1543);

        assert getParamsBSL.getProdTypeParameters() != null;
        if (pv.getBuildingLoanApplication().getFirstTouchPointOwnr().startsWith("15") ||
                pv.getBuildingLoanApplication().getFirstTouchPointOwnr().startsWith("35")) {
            agent1543.setCommission(BigDecimal.valueOf(getParamsBSL.getProdTypeParameters().getCommissionAmt()));
        } else {
            BigDecimal commissionValue = BigDecimal.valueOf(getParamsBSL.getProdTypeParameters().getCommissionCoef() * pv.getRekoRpsnCalculations().getFinaAmt().doubleValue());
            agent1543.setCommission(commissionValue);
        }

        p0001543.setAgent(agent1543);
        p0001543.setRole(RB_AGENT);
        P0001543.ApplDetails applDetails1543 = new P0001543.ApplDetails();

        var aprVariant = pv.getBuildingLoanApplVariants().getVariant(APR);
        var selVariant = pv.getBuildingLoanApplVariants().getVariant(SEL);

        applDetails1543.setAddRSPN(pv.getRekoRpsnCalculations().getAddRPSN().multiply(new java.math.BigDecimal(100)).toString());
        applDetails1543.setReportDate(LocalDate.now());
        applDetails1543.setDueDate(LocalDate.parse(pv.getRekoRpsnCalculations().getDueDate()));
        applDetails1543.setFinaAmt(aprVariant.getFinaAmt().stripTrailingZeros());
        PtIncomeRstsListDTO incomes = pv.getLoanApplicationIncomes();


        List<PtIncomeRstsDTO> allVerifDone = incomes.getIncomes().stream()
                .filter(income -> VERIFDONE.equals(income.getIncVerifRsltId()))
                .toList();
        List<PtIncomeRstsDTO> allVerifDoneMaxListOrderByOrdNum = sortIncomes(allVerifDone, getLanguage());

        if (!allVerifDoneMaxListOrderByOrdNum.isEmpty() && allVerifDoneMaxListOrderByOrdNum.get(0) != null) {
            var currLov = lovManager.getLov(new LovCcy(allVerifDoneMaxListOrderByOrdNum.get(0).getCcyId()), getLanguage());
            if (currLov != null) {
                applDetails1543.setCurrency(currLov.getCode());
            }
        }

        List<PtIncomeRstsDTO> foreignVerifDone = incomes.getIncomes().stream()
                .filter(income -> VERIFDONE.equals(income.getIncVerifRsltId()) && !"CZK".equals(income.getCcyId()))
                .toList();
        List<PtIncomeRstsDTO> foreignVerifDoneMaxListOrderByOrdNum = sortIncomes(foreignVerifDone, getLanguage());


        if (!foreignVerifDoneMaxListOrderByOrdNum.isEmpty() && foreignVerifDoneMaxListOrderByOrdNum.get(0) != null) {
            var firstItem = foreignVerifDoneMaxListOrderByOrdNum.get(0);

            BigDecimal exchangeRate = firstItem.getExchangeRate();
            if (exchangeRate != null && exchangeRate.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal multiplier = BigDecimal.valueOf(0.8);
                BigDecimal divisor = exchangeRate.multiply(multiplier);

                BigDecimal finaAmt = pv.getRekoRpsnCalculations().getFinaAmt();
                BigDecimal finaInsAmt = pv.getRekoRpsnCalculations().getFinaInsAmt();

                if (finaAmt != null) {
                    applDetails1543.setFinaAmtAdjustment(finaAmt.divide(divisor, RoundingMode.HALF_UP));
                }

                if (finaInsAmt != null) {
                    BigDecimal finaInsAmtAdjustment = finaInsAmt.divide(divisor, 10, RoundingMode.HALF_UP)
                            .subtract(finaInsAmt.divide(exchangeRate, 10, RoundingMode.HALF_UP));
                    applDetails1543.setFinaInsAmtAdjustment(finaInsAmtAdjustment.setScale(2, RoundingMode.HALF_UP));
                }
            }
        }

        applDetails1543.setFinaInstCnt(pv.getRekoRpsnCalculations().getFinaInstCnt().intValue());

        String fixPeriod = aprVariant.getFixPeriod();
        if (fixPeriod != null) {
            applDetails1543.setFixPeriod(new BigInteger(fixPeriod));
        }
        if (subtypeParametrization != null) {
            LovCsaParamCastar lovCsaParamCastar = lovManager.getLovByDetail(LovCsaParamCastar.class, TARIF, subtypeParametrization.getTariff(), lang);
            if (lovCsaParamCastar != null) {
                applDetails1543.setTotalIRate(new BigDecimal(lovCsaParamCastar.getLovDetail().get(ODCHYLKAURU)));
            }
            applDetails1543.setSavingsMaintenanceFeeAmt(BigDecimal.valueOf(subtypeParametrization.getSavingsMaintenanceFeeAmt()));
            applDetails1543.setSavingsStatementFeeAmt(BigDecimal.valueOf(subtypeParametrization.getSavingsStatementFeeAmt()));
            applDetails1543.setLoanMaintenanceFeeAmt(BigDecimal.valueOf(subtypeParametrization.getLoanMaintenanceFeeAmt()));
            applDetails1543.setLoanStatementFeeAmt(BigDecimal.valueOf(subtypeParametrization.getLoanStatementFeeAmt()));
            List<BSLSubtypePurposeCategory> purposeCategories = subtypeParametrization.getLoanPurposeCategories();
            BuildingApplVariantPurposeDTO purposeVerified = aprVariant.getLoanPurposes().stream().filter(cat -> cat.getPurpStatId().equals(VERIF)).findFirst().orElse(null);
            if (purposeVerified != null) {
                BSLSubtypePurposeCategory filterPurposeCategory = purposeCategories.stream().filter(purposeCategory -> purposeCategory.getPurposeCategory().getName().equals(purposeVerified.getPurpCatgId())).findFirst().orElse(null);
                if (filterPurposeCategory != null && filterPurposeCategory.getInterestVariances() != null) {
                    List<BSLInterestVariance> interestVariancesSurNinSur = filterPurposeCategory.getInterestVariances().stream().filter(interestVariance -> interestVariance.getTypeId().equals("SUR_NINSUR_PPI")).toList();
                    interestVariancesSurNinSur.stream().map(BSLInterestVariance::getVariance).min(Comparator.naturalOrder()).ifPresent(minimalVariance -> applDetails1543.setInsIRAdd(BigDecimal.valueOf(minimalVariance * 100)));
                }
            }
        }
        applDetails1543.setToBePaidAmt(pv.getRekoRpsnCalculations().getToBePaidAmt());
        applDetails1543.setCostToBePaid(pv.getRekoRpsnCalculations().getCostToBePaid());
        applDetails1543.setRPSN(String.valueOf(pv.getRekoRpsnCalculations().getRPSN()));
        applDetails1543.setFinaIR(aprVariant.getIntrsRx().multiply(new BigDecimal(100)));
        applDetails1543.setFinaLoanLength(pv.getRekoRpsnCalculations().getFinaLoanLength().intValue());

        applDetails1543.setSavingsContractFeeAmt(pv.getRekoRpsnCalculations().getSavingsContractFeeAmt());
        applDetails1543.setLoanContractFeeAmt(pv.getRekoRpsnCalculations().getLoanContractFeeAmt());
        applDetails1543.setInsFeeAmt(pv.getRekoRpsnCalculations().getInsFeeAmt());
        applDetails1543.setEstimDisbursDate(LocalDate.parse(pv.getRekoRpsnCalculations().getEstimDisbursDate()));
        applDetails1543.setFirstPaymentDate(LocalDate.parse(pv.getRekoRpsnCalculations().getFirstPaymentDate()));
        applDetails1543.setRPSN(String.valueOf(pv.getRekoRpsnCalculations().getRPSN().multiply(new java.math.BigDecimal(100))));
        applDetails1543.setFinaInsAmt(pv.getRekoRpsnCalculations().getFinaInsAmt());
        applDetails1543.setDeposit(pv.getRekoRpsnCalculations().getDeposit());

        boolean existSurchargeDisRbFlag = selVariant.getSurcharges().stream().anyMatch(surcharge -> "DIS_RB_FLAG".equals(surcharge.getSurchrgTpId()));
        applDetails1543.setRbAccount(String.valueOf(existSurchargeDisRbFlag));

        boolean existsPPI = selVariant.getInsurances().stream().anyMatch(insurance -> "PPI".equals(insurance.getInsurTpId()));
        applDetails1543.setInsurSelFlg(String.valueOf(existsPPI));

        if (getParamsBSL.getProdTypeParameters() != null) {
            double loanPenaltyCoef = getParamsBSL.getProdTypeParameters().getLoanPenaltyCoef();
            int loanPenaltyMin = getParamsBSL.getProdTypeParameters().getLoanPenaltyMin();
            int loanPenaltyMax = getParamsBSL.getProdTypeParameters().getLoanPenaltyMax();
            BigDecimal finaAmt = pv.getRekoRpsnCalculations().getFinaAmt();

            if (finaAmt.multiply(BigDecimal.valueOf(loanPenaltyCoef)).compareTo(BigDecimal.valueOf(loanPenaltyMin)) < 0) {
                applDetails1543.setLoanPenaltyFeeAmt(BigDecimal.valueOf(loanPenaltyMin));
            } else if (finaAmt.multiply(BigDecimal.valueOf(loanPenaltyCoef)).compareTo(BigDecimal.valueOf(loanPenaltyMax)) > 0) {
                applDetails1543.setLoanPenaltyFeeAmt(BigDecimal.valueOf(loanPenaltyMax));
            } else {
                applDetails1543.setLoanPenaltyFeeAmt(finaAmt.multiply(BigDecimal.valueOf(loanPenaltyCoef)));
            }
        }
        applDetails1543.setRstsBuildingSavingsNumber(pv.getBuildingLoanApplication().getContrNumBs());
        applDetails1543.setRstsRegistrationLoanNumber(pv.getBuildingLoanApplication().getContrNumReg());

        P0001543.ApplDetails.RepaymentSchedule repaymentSchedule1543 = new P0001543.ApplDetails.RepaymentSchedule();
        if (pv.getRekoRpsnCalculations() != null && pv.getRekoRpsnCalculations().getIpsScheduleRowsCount() != null) {
            repaymentSchedule1543.setMscheduleNoOfRows(pv.getRekoRpsnCalculations().getIpsScheduleRowsCount().intValue());
        }
        if (pv.getRekoRpsnCalculations() != null && pv.getRekoRpsnCalculations().getIpsScheduleRows() != null) {
            pv.getRekoRpsnCalculations().getIpsScheduleRows().forEach(row -> {
                P0001543.ApplDetails.RepaymentSchedule.RepaymentScheduleItem periodId1543 = new P0001543.ApplDetails.RepaymentSchedule.RepaymentScheduleItem();
                periodId1543.setMPeriod(row.getPeriodID());
                if (row.getSavings() != null) {
                    periodId1543.setMSavings(String.valueOf(row.getSavings()));
                }
                periodId1543.setMFinaAmt(row.getFinaAmt());
                periodId1543.setMFinaInsAmt(row.getFinaInsAmt());
                if (row.getFinalIR() != null) {
                    periodId1543.setMFinalIR(row.getFinalIR().multiply(new BigDecimal(100)));
                }
                periodId1543.setMItem(row.getIpsScheduleRowType().getValue());
                periodId1543.setMOtherCosts(row.getOtherCosts());
                periodId1543.setMOtherCostsExluded(row.getOtherCostsExluded());
                periodId1543.setMRemainingInterest(row.getRemainingInterest());
                periodId1543.setMRemainingPrincipal(row.getRemainingPrincipal());
                periodId1543.setMRepaidInterest(row.getRepaidInterest());
                periodId1543.setMRepaidPrincipal(row.getRepaidPrincipal());
                periodId1543.setMSavingsIncome(row.getSavingsIncome());

                repaymentSchedule1543.getRepaymentScheduleItem().add(periodId1543);
            });
        }
        applDetails1543.setRepaymentSchedule(repaymentSchedule1543);

        p0001543.setApplDetails(applDetails1543);

        if (pv.getLoanApplicationOwner() != null) {
            P0001543.Party party1543 = new P0001543.Party();
            party1543.setFirstName(pv.getLoanApplicationOwner().getFirstName());
            party1543.setLastName(pv.getLoanApplicationOwner().getFamilyName());
            p0001543.setParty(party1543);
        }
        return p0001543;
    }
    @NotNull
    private P0001644 prepareTemplateP0001644(ProcessVariables pv) {
        BuildingLoanApplicationOwnerDTO loanApplicationOwner = pv.getLoanApplicationOwner();
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);
        BSLSubtypeParametrization subtypeParametrization = getParamsBSL.getSubtypes().stream().filter(
                subtype -> subtype.getBusProdSubTp().equals(pv.getBuildingLoanApplication().getBusProdSubTp())
        ).findFirst().orElse(null);
        DealerInfoDTO dealerInfo = pv.getLoanApplicationDealerInfo();

        P0001644 p0001644 = new P0001644();

        if (buildingLoanAppl.getQuester() != null) {
            P0001644.Questr questr = new P0001644.Questr();
            questr.setSbmDate(buildingLoanAppl.getQuester().getSbmDate().toLocalDate());
            List<P0001644.Questr.Quests> questions = new ArrayList<>();
            buildingLoanAppl.getQuester().getQuests().forEach(quest -> {
                P0001644.Questr.Quests quests = new P0001644.Questr.Quests();
                quests.setQuestId(quest.getQuestId());
                if (quest.getQuestRsp().equals("TRUE")){
                    quests.setRsp(Boolean.TRUE);
                }
                questions.add(quests);
            });
            questr.getQuests().addAll(questions);
            p0001644.setQuestr(questr);
        }

        P0001644.ApplDetails applDetails1644 = new P0001644.ApplDetails();
        if (subtypeParametrization != null) {
            applDetails1644.setInsNumber(subtypeParametrization.getInsNum());
            applDetails1644.setInsName(subtypeParametrization.getInsName());
        }
        p0001644.setApplDetails(applDetails1644);

        P0001644.Agent agent1644 = new P0001644.Agent();
        agent1644.setAgentName(AGENT_NAME);
        P0001644.Agent.Address agentAddress= new P0001644.Agent.Address();
        agentAddress.setAgentAddStreet(dealerInfo.getCompanyAddressStreet());
        agentAddress.setAgentAddZip(dealerInfo.getCompanyAddressZipCode());
        agentAddress.setAgentAddCity(dealerInfo.getCompanyAddressCity());
        agent1644.setAddress(agentAddress);
        agent1644.setRole(RB_AGENT);
        p0001644.setAgent(agent1644);

        P0001644.Repre repre = new P0001644.Repre();
        if (buildingLoanAppl.getFirstTouchPointOwnr().startsWith("103")) {
            repre.setRepreFirstName(dealerInfo.getFirstName());
            repre.setRepreFamilyName(dealerInfo.getLastName());
            P0001644.Repre.Address address = new P0001644.Repre.Address();
            address.setRepreAddStreet(HVEZDOVA);
            address.setRepreAddNum("1716/2b");
            address.setRepreAddZip("140 78");
            address.setRepreAddCity(PRAHA_4);
            repre.setAddress(address);

        }
        p0001644.setRepre(repre);

        P0001644.Persons persons = new P0001644.Persons();
        persons.setClientFirstName(loanApplicationOwner.getFirstName());
        persons.setClientFamilyName(loanApplicationOwner.getFamilyName());
        P0001644.Persons.PermAddr permAddr = new P0001644.Persons.PermAddr();
        if (StringUtils.isBlank(loanApplicationOwner.getPermAddr().getStreetName())) {
            permAddr.setPermAddStreet(loanApplicationOwner.getPermAddr().getCityName());
        }else {
            permAddr.setPermAddStreet(loanApplicationOwner.getPermAddr().getStreetName());
        }
        permAddr.setPermAddNum(loanApplicationOwner.getPermAddr().getStreetNum());
        permAddr.setPermAddZip(loanApplicationOwner.getPermAddr().getZip());
        permAddr.setPermAddCity(loanApplicationOwner.getPermAddr().getCityName());
        permAddr.setPermAddCountry(getCountry(new LovCntry(loanApplicationOwner.getPermAddr().getCntryId())));
        persons.setPermAddr(permAddr);

        persons.setRegEmail(loanApplicationOwner.getEmail());
        p0001644.setPersons(persons);



        return p0001644;
    }


    protected String getCountry(LovCntry lovCntry) {
        return lovManager.getLov(lovCntry, getLanguage()).getLabel();
    }

    private String getRejectReason(String rejectionReason) throws ServiceException {
        if (rejectionReason == null) {
            log.warn("rejectionReason (rejectRsnId) is null, but should be filled");
        } else {
            var translation = getLovTranslationFirst("EXTN_REJ_RSN_TP", rejectionReason);
            if (translation != null && translation.getTrgDescr() != null) {
                return translation.getTrgDescr();
            }
            log.warn("rejectionReason (rejectRsnId) is {} not found in lov", rejectionReason);
        }
        return "";
    }

    LovTranslationsItemDTO getLovTranslationFirst(String lovId, String srcId) throws ServiceException {
        List<LovTranslationsItemDTO> response = lovManager.getLovTranslationFromCache(lovId, srcId, LovLang.CZE2, "CMN", "CSA");
        return response.get(0);
    }

    protected List<PtIncomeRstsDTO> sortIncomes(List<PtIncomeRstsDTO> verifDoneIncomes, LovLang lang) {
        List<PtIncomeRstsDTO> nonNullIncomes = verifDoneIncomes.stream()
                .filter(income -> income.getNetIncFinal() != null)
                .toList();

        if (nonNullIncomes.isEmpty()) {
            return verifDoneIncomes.stream()
                    .sorted(Comparator.comparing(income -> {
                        try {
                            return Integer.parseInt(lovManager.getLov(LovIncTp.fromString(income.getIncTpId()), lang)
                                    .getLovDetail().get("ORD_NUM"));
                        } catch (Exception e) {
                            return Integer.MAX_VALUE;
                        }
                    }))
                    .toList();
        }

        List<PtIncomeRstsDTO> allVerifDoneMaxList = nonNullIncomes.stream()
                .filter(income -> income.getNetIncFinal().equals(nonNullIncomes.stream()
                        .max(Comparator.comparing(PtIncomeRstsDTO::getNetIncFinal))
                        .orElse(null).getNetIncFinal()))
                .toList();
        return allVerifDoneMaxList.stream()
                .sorted(Comparator.comparing(income -> Integer.parseInt(lovManager.getLov(LovIncTp.fromString(income.getIncTpId()), lang).getLovDetail().get("ORD_NUM"))))
                .toList();
    }

    protected LocalDate getPreContrReportDate(ProcessVariables pv) {
        GeneratedDocumentsDTO generatedDocuments = pv.getGeneratedDocuments();
        if (generatedDocuments != null && generatedDocuments.getGeneratedObjectIds() != null && generatedDocuments.getGeneratedObjectIds().containsKey(DocumentType.P0001543.name())) {
            GeneratedDocumentInfoDTO info = generatedDocuments.getGeneratedObjectIds().get(DocumentType.P0001543.name());
            if (info != null && info.getCreationDate() != null) {
                return info.getCreationDate();
            }
        }

        log.warn("{} creationDate not found, current date used", DocumentType.P0001543.name());
        return LocalDate.now();
    }
}
