<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_12fr83s" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.35.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.19.0">
  <bpmn:process id="MortgageStorno" name="Mortgage Storno" isExecutable="true" camunda:historyTimeToLive="7">
    <bpmn:parallelGateway id="Gateway_joinEnd">
      <bpmn:incoming>Flow_toJoin2</bpmn:incoming>
      <bpmn:incoming>Flow_toJoin1</bpmn:incoming>
      <bpmn:incoming>Flow_toJoin3</bpmn:incoming>
      <bpmn:outgoing>Flow_toCancelDocs</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_join">
      <bpmn:incoming>Flow_toParallel</bpmn:incoming>
      <bpmn:outgoing>Flow_toFakeDecision</bpmn:outgoing>
      <bpmn:outgoing>Flow_toEmailDecision</bpmn:outgoing>
      <bpmn:outgoing>Flow_toCloseMutManualTask</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:startEvent id="Event_ProcessStarted" name="Start">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_01cg1t5</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="Event_Final" name="Final">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_toFinal</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="delete_docs" name="Delete docs" camunda:type="external" camunda:topic="mtgDeleteDocuments">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_toDeleteDocs</bpmn:incoming>
      <bpmn:outgoing>Flow_toFinal</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="cancel_docs" name="Cancel docs" camunda:type="external" camunda:topic="mtgCancelDocuments">
      <bpmn:documentation>Volá DMS.getList a filtruje pouze dokumenty, které u kterých je možné provést cancel a mazat je. Kolekci uloží do PV generatedDocuments. Aktuálně P0001427.</bpmn:documentation>
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_toCancelDocs</bpmn:incoming>
      <bpmn:outgoing>Flow_toTimer</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="updateOpportunity" name="updateOpportunity" camunda:type="external" camunda:topic="mtgUpdateOpportunity">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="status">Lost</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_toUpdateOpportunity</bpmn:incoming>
      <bpmn:outgoing>Flow_toOpportunityJoin</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_rejectionEmail" default="Flow_toNoEmail">
      <bpmn:incoming>Flow_toEmailDecision</bpmn:incoming>
      <bpmn:outgoing>Flow_toEmail</bpmn:outgoing>
      <bpmn:outgoing>Flow_toNoEmail</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_rejectionEmailEnd">
      <bpmn:incoming>Flow_toNoEmail</bpmn:incoming>
      <bpmn:incoming>Flow_toAfterEmailJoin</bpmn:incoming>
      <bpmn:outgoing>Flow_toJoin2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:intermediateCatchEvent id="Event_TimerWaitOnLTV" name="waiting 60 hours&#10;on LTV">
      <bpmn:incoming>Flow_toTimer</bpmn:incoming>
      <bpmn:outgoing>Flow_toDeleteDocs</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1lypfs6">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${processConfiguration.getValue('waitingOnLTV')}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="Gateway_fakeClient" name="is fake client?">
      <bpmn:incoming>Flow_toFakeDecision</bpmn:incoming>
      <bpmn:outgoing>Flow_toUpdateOpportunity</bpmn:outgoing>
      <bpmn:outgoing>Flow_toNoOpoortunity</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_fakeClientEnd">
      <bpmn:incoming>Flow_toOpportunityJoin</bpmn:incoming>
      <bpmn:incoming>Flow_toNoOpoortunity</bpmn:incoming>
      <bpmn:outgoing>Flow_toJoin1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_toJoin2" sourceRef="Gateway_rejectionEmailEnd" targetRef="Gateway_joinEnd" />
    <bpmn:sequenceFlow id="Flow_toJoin1" sourceRef="Gateway_fakeClientEnd" targetRef="Gateway_joinEnd" />
    <bpmn:sequenceFlow id="Flow_toFakeDecision" sourceRef="Gateway_join" targetRef="Gateway_fakeClient" />
    <bpmn:sequenceFlow id="Flow_toEmailDecision" sourceRef="Gateway_join" targetRef="Gateway_rejectionEmail" />
    <bpmn:sequenceFlow id="Flow_toDeleteDocs" sourceRef="Event_TimerWaitOnLTV" targetRef="delete_docs" />
    <bpmn:sequenceFlow id="Flow_toFinal" sourceRef="delete_docs" targetRef="Event_Final" />
    <bpmn:sequenceFlow id="Flow_toEmail" name="rejectionState == &#39;ZDZAM&#39;" sourceRef="Gateway_rejectionEmail" targetRef="generateEmail_X0000030">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
      </bpmn:extensionElements>
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${rejectionState=='ZDZAM'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_toTimer" sourceRef="cancel_docs" targetRef="Event_TimerWaitOnLTV" />
    <bpmn:sequenceFlow id="Flow_toUpdateOpportunity" name="NO" sourceRef="Gateway_fakeClient" targetRef="updateOpportunity">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!firstTouchPointFakeClient}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_toOpportunityJoin" sourceRef="updateOpportunity" targetRef="Gateway_fakeClientEnd" />
    <bpmn:sequenceFlow id="Flow_toNoEmail" sourceRef="Gateway_rejectionEmail" targetRef="Gateway_rejectionEmailEnd">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_toNoOpoortunity" name="YES" sourceRef="Gateway_fakeClient" targetRef="Gateway_fakeClientEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${firstTouchPointFakeClient}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_mortgageApplState_rejectionState" name="mortgageApplState {rejectionState}" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">${rejectionState}</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_07ifs2p</bpmn:incoming>
      <bpmn:outgoing>Flow_1sa5hyt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_CheckFTPCodeTask" name="checkFTPCode" camunda:type="external" camunda:topic="mtgCheckFTPCode">
      <bpmn:incoming>Flow_toCheckFTP</bpmn:incoming>
      <bpmn:outgoing>Flow_toParallel</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_toParallel" sourceRef="Activity_CheckFTPCodeTask" targetRef="Gateway_join" />
    <bpmn:serviceTask id="generateEmail_X0000030" name="generateEmail&#10;(X0000030)" camunda:type="external" camunda:topic="mtgGenerateEmailRejection">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="template">X0000030</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_toEmail</bpmn:incoming>
      <bpmn:outgoing>Flow_toAfterEmailJoin</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_toAfterEmailJoin" sourceRef="generateEmail_X0000030" targetRef="Gateway_rejectionEmailEnd" />
    <bpmn:serviceTask id="Activity_guarOfferState" name="guarOfferState" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">${rejectionState}</camunda:inputParameter>
          <camunda:inputParameter name="applId">${offerId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1sa5hyt</bpmn:incoming>
      <bpmn:outgoing>Flow_toCheckFTP</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="${activeOfferIds}" camunda:elementVariable="offerId" />
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_getMtgApplication" name="getMtgApplication" camunda:type="external" camunda:topic="mtgGetApplication">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="activeOnly">0</camunda:inputParameter>
          <camunda:inputParameter name="busApplId">${busApplId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01cg1t5</bpmn:incoming>
      <bpmn:outgoing>Flow_07ifs2p</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_toCheckFTP" sourceRef="Activity_guarOfferState" targetRef="Activity_CheckFTPCodeTask" />
    <bpmn:sequenceFlow id="Flow_toCancelDocs" sourceRef="Gateway_joinEnd" targetRef="cancel_docs" />
    <bpmn:serviceTask id="Activity_closeMutManualTask" name="Close MUT Manual Task" camunda:type="external" camunda:topic="mtgCloseMutManualTask">
      <bpmn:documentation>vstup:
- busApplId
- corrleationId</bpmn:documentation>
      <bpmn:incoming>Flow_toCloseMutManualTask</bpmn:incoming>
      <bpmn:outgoing>Flow_toJoin3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_toCloseMutManualTask" sourceRef="Gateway_join" targetRef="Activity_closeMutManualTask" />
    <bpmn:sequenceFlow id="Flow_toJoin3" sourceRef="Activity_closeMutManualTask" targetRef="Gateway_joinEnd" />
    <bpmn:sequenceFlow id="Flow_01cg1t5" sourceRef="Event_ProcessStarted" targetRef="Activity_getMtgApplication" />
    <bpmn:sequenceFlow id="Flow_07ifs2p" sourceRef="Activity_getMtgApplication" targetRef="Activity_mortgageApplState_rejectionState" />
    <bpmn:sequenceFlow id="Flow_1sa5hyt" sourceRef="Activity_mortgageApplState_rejectionState" targetRef="Activity_guarOfferState" />
    <bpmn:textAnnotation id="TextAnnotation_0i03lrl">
      <bpmn:text>Vstup:
- busApplId
- rejectionState
- corrleationId</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0uf8nhp" associationDirection="None" sourceRef="Event_ProcessStarted" targetRef="TextAnnotation_0i03lrl" />
    <bpmn:textAnnotation id="TextAnnotation_14qy9qc">
      <bpmn:text>Volá DMS.getList a filtruje pouze dokumenty, které u kterých je možné provést cancel a mazat je. Kolekci uloží do PV generatedDocuments. Aktuálně P0001427.</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1n3lti2" associationDirection="None" sourceRef="cancel_docs" targetRef="TextAnnotation_14qy9qc" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MortgageStorno">
      <bpmndi:BPMNShape id="Gateway_1rxk0wj_di" bpmnElement="Gateway_joinEnd">
        <dc:Bounds x="1209" y="285" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ckpxnn_di" bpmnElement="Gateway_join">
        <dc:Bounds x="841" y="285" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ihermt_di" bpmnElement="Event_ProcessStarted">
        <dc:Bounds x="182" y="292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="187" y="335" width="25" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1f6iamb_di" bpmnElement="Event_Final">
        <dc:Bounds x="1662" y="292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1669" y="335" width="25" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bed5mv_di" bpmnElement="delete_docs">
        <dc:Bounds x="1520" y="270" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1rodyw6_di" bpmnElement="cancel_docs">
        <dc:Bounds x="1290" y="270" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0923sn9_di" bpmnElement="updateOpportunity">
        <dc:Bounds x="1000" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dpsnhh_di" bpmnElement="Gateway_rejectionEmail" isMarkerVisible="true">
        <dc:Bounds x="841" y="385" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cldh46_di" bpmnElement="Gateway_rejectionEmailEnd" isMarkerVisible="true">
        <dc:Bounds x="1209" y="385" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1tjbqqy_di" bpmnElement="Event_TimerWaitOnLTV">
        <dc:Bounds x="1442" y="292" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1421" y="335" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uv5vsd_di" bpmnElement="Gateway_fakeClient" isMarkerVisible="true">
        <dc:Bounds x="923" y="285" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="915" y="342" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vxktsg_di" bpmnElement="Gateway_fakeClientEnd" isMarkerVisible="true">
        <dc:Bounds x="1125" y="285" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jf9mja" bpmnElement="Activity_mortgageApplState_rejectionState" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="410" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1neurkk_di" bpmnElement="Activity_CheckFTPCodeTask" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="710" y="270" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pf1x62_di" bpmnElement="generateEmail_X0000030" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1000" y="460" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wznupl" bpmnElement="Activity_guarOfferState" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="560" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0g1gmt3" bpmnElement="Activity_getMtgApplication" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="260" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1evlogd" bpmnElement="Activity_closeMutManualTask">
        <dc:Bounds x="1000" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0i03lrl_di" bpmnElement="TextAnnotation_0i03lrl">
        <dc:Bounds x="150" y="180" width="100" height="70" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_14qy9qc_di" bpmnElement="TextAnnotation_14qy9qc">
        <dc:Bounds x="1290" y="140" width="200" height="96" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0a81qfp_di" bpmnElement="Flow_toJoin2">
        <di:waypoint x="1234" y="385" />
        <di:waypoint x="1234" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cvienc_di" bpmnElement="Flow_toJoin1">
        <di:waypoint x="1175" y="310" />
        <di:waypoint x="1209" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13vipqg_di" bpmnElement="Flow_toFakeDecision">
        <di:waypoint x="891" y="310" />
        <di:waypoint x="923" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04nnxre_di" bpmnElement="Flow_toEmailDecision">
        <di:waypoint x="866" y="335" />
        <di:waypoint x="866" y="385" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c8059p_di" bpmnElement="Flow_toDeleteDocs">
        <di:waypoint x="1478" y="310" />
        <di:waypoint x="1520" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19ovzim_di" bpmnElement="Flow_toFinal">
        <di:waypoint x="1620" y="310" />
        <di:waypoint x="1662" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wrvipo_di" bpmnElement="Flow_toEmail">
        <di:waypoint x="866" y="435" />
        <di:waypoint x="866" y="500" />
        <di:waypoint x="1000" y="500" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="878" y="465" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kdzirs_di" bpmnElement="Flow_toTimer">
        <di:waypoint x="1390" y="310" />
        <di:waypoint x="1442" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jczake_di" bpmnElement="Flow_toUpdateOpportunity">
        <di:waypoint x="948" y="285" />
        <di:waypoint x="948" y="220" />
        <di:waypoint x="1000" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="944" y="193" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vzu5bq_di" bpmnElement="Flow_toOpportunityJoin">
        <di:waypoint x="1100" y="220" />
        <di:waypoint x="1150" y="220" />
        <di:waypoint x="1150" y="285" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ny8mz4_di" bpmnElement="Flow_toNoEmail">
        <di:waypoint x="891" y="410" />
        <di:waypoint x="1209" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ooj025_di" bpmnElement="Flow_toNoOpoortunity">
        <di:waypoint x="973" y="310" />
        <di:waypoint x="1125" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="989" y="292" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aselyb_di" bpmnElement="Flow_toParallel">
        <di:waypoint x="810" y="310" />
        <di:waypoint x="841" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pn5tl5_di" bpmnElement="Flow_toAfterEmailJoin">
        <di:waypoint x="1100" y="500" />
        <di:waypoint x="1234" y="500" />
        <di:waypoint x="1234" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ymj1na_di" bpmnElement="Flow_toCheckFTP">
        <di:waypoint x="660" y="310" />
        <di:waypoint x="710" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s7lb1b_di" bpmnElement="Flow_toCancelDocs">
        <di:waypoint x="1259" y="310" />
        <di:waypoint x="1290" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ag9lxi_di" bpmnElement="Flow_toCloseMutManualTask">
        <di:waypoint x="866" y="285" />
        <di:waypoint x="866" y="120" />
        <di:waypoint x="1000" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gwy4yi_di" bpmnElement="Flow_toJoin3">
        <di:waypoint x="1100" y="120" />
        <di:waypoint x="1234" y="120" />
        <di:waypoint x="1234" y="285" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01cg1t5_di" bpmnElement="Flow_01cg1t5">
        <di:waypoint x="218" y="310" />
        <di:waypoint x="260" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07ifs2p_di" bpmnElement="Flow_07ifs2p">
        <di:waypoint x="360" y="310" />
        <di:waypoint x="410" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sa5hyt_di" bpmnElement="Flow_1sa5hyt">
        <di:waypoint x="510" y="310" />
        <di:waypoint x="560" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0uf8nhp_di" bpmnElement="Association_0uf8nhp">
        <di:waypoint x="200" y="292" />
        <di:waypoint x="200" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1n3lti2_di" bpmnElement="Association_1n3lti2">
        <di:waypoint x="1340" y="270" />
        <di:waypoint x="1340" y="236" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
