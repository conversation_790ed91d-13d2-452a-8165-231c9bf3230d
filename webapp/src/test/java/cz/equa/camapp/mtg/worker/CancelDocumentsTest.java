package cz.equa.camapp.mtg.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.model.document.DocumentRenderUpdateDto;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.util.Collections;
import java.util.Map;

import static cz.equa.camapp.rest.service.DmsService.DOCUMENT_ID_TYPE_EXTERNALID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringJUnitWebConfig(classes = {CancelDocuments.class})
class CancelDocumentsTest {

    @MockBean
    private DmsService dmsService;

    @MockBean
    public ApplicationService applicationService;
    @Autowired
    CancelDocuments underTest;

    private Map<String, Object> getProcessVariablesA() throws JsonProcessingException {
        String jsonString = """
                {
                    "applicationState": "STR",
                    "busApplId": "1",
                    "applKey": 1,
                    "subApplKey": 222222201,
                    "X-Correlation-Id": "1"
                }""";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(jsonString, new TypeReference<>() {
        });
    }

    @Test
    void executeWorker() throws JsonProcessingException, ServiceException {
        GetDocumentListDto dto = new GetDocumentListDto();
        GetDocumentListDto.Document doc = new GetDocumentListDto.Document();
        GetDocumentListDto.Document.DocumentDetailFull detail = new GetDocumentListDto.Document.DocumentDetailFull();
        detail.setSmStateId("CLCFD_CLCFD_FULL");
        detail.setDocumentClass("P0001241");
        detail.setDocumentId("CMNL1");
        doc.setDocument(detail);
        dto.setDocumentList(Collections.singletonList(doc));
        when(dmsService.getDocumentListByApplicationId(anyString(), anyList(), anyString())).thenReturn(dto);
        DocumentRenderUpdateDto documentRenderUpdateDto = new DocumentRenderUpdateDto();
        documentRenderUpdateDto.setVersion("test");
        when(dmsService.documentRenderUpdateDecline(anyString(), anyString(), anyString())).thenReturn(documentRenderUpdateDto);
        when(dmsService.documentRenderUpdateCancel(anyString(), anyString(), anyString())).thenReturn(documentRenderUpdateDto);

        when(applicationService.getApplState(any(), anyString(), anyString())).thenReturn("STR");

        ExternalTask externalTask = mock(ExternalTask.class);
        ExternalTaskService externalTaskService = mock(ExternalTaskService.class);
        doNothing().when(externalTaskService).complete(any(), any());

        when(externalTask.getAllVariables()).thenReturn(getProcessVariablesA());
        underTest.execute(externalTask, externalTaskService);
        verify(dmsService, times(1)).getDocumentListByApplicationId(anyString(), anyList(), anyString());
        verify(dmsService, times(1)).documentRenderUpdateDecline(anyString(), eq(DOCUMENT_ID_TYPE_EXTERNALID), anyString());
    }
}